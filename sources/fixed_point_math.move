#[allow(unused_variable)]
module suitrump_dex::fixed_point_math {
    use std::{u128, u256};
    
    // Constants for precision and bounds
    const PRECISION: u256 = 1000000000000000000; // 1e18
    const MAX_U256: u256 = 115792089237316195423570985008687907853269984665640564039457584007913129639935; // 2^256 - 1

    const MIN_VALUE: u256 = 0;
    const MAX_VALUE: u256 = MAX_U256 / PRECISION; // Maximum safe value
    
    // Error codes
    const E_OVERFLOW: u64 = 0;
    const E_DIVIDE_BY_ZERO: u64 = 1;
    const E_INVALID_CONVERSION: u64 = 2;
    const E_INVALID_DECIMALS: u64 = 3;

    // Internal constants for advanced algorithms
    const SQRT_PRECISION_BITS: u8 = 128;
    const KARATSUBA_THRESHOLD: u256 = 100000000; // Threshold for using Karatsuba multiplication
    
    public struct FixedPoint has store, copy, drop {
        value: u256
    }

    // Internal rational number representation for high-precision operations
    public struct Rational has copy, drop {
        numerator: u256,
        denominator: u256,
    }

    // === Core Functions (API Compatible) ===
    
    public fun new(value: u256): FixedPoint {
        FixedPoint { value }
    }

    public fun from_raw(value: u256, decimals: u8): FixedPoint {
        let scaled_value = scale_to_precision(value, decimals);
        FixedPoint { value: scaled_value }
    }

    public fun get_raw_value(fp: FixedPoint): u256 {
        fp.value
    }

    // === Enhanced Arithmetic Operations ===

    public fun add(a: FixedPoint, b: FixedPoint): FixedPoint {
        let sum = safe_add_u256(a.value, b.value);
        FixedPoint { value: sum }
    }

    public fun sub(a: FixedPoint, b: FixedPoint): FixedPoint {
        assert!(a.value >= b.value, E_OVERFLOW);
        FixedPoint { value: a.value - b.value }
    }

    public fun mul(a: FixedPoint, b: FixedPoint): FixedPoint {
        let result = multiply_with_precision(a.value, b.value);
        FixedPoint { value: result }
    }

    public fun div(a: FixedPoint, b: FixedPoint): FixedPoint {
        assert!(b.value != 0, E_DIVIDE_BY_ZERO);
        let result = divide_with_precision(a.value, b.value);
        FixedPoint { value: result }
    }

    /// DANGER: Only use for price accumulators! 
    /// This function allows overflow wrapping which is UNSAFE for token amounts.
    /// For token/liquidity calculations, use add() which aborts on overflow.
    public(package) fun add_wrapping_for_price_accumulator(a: FixedPoint, b: FixedPoint): FixedPoint {
        let sum = wrapping_add_u256(a.value, b.value);
        FixedPoint { value: sum }
    }

    /// Internal wrapping addition for u256 values
    fun wrapping_add_u256(a: u256, b: u256): u256 {
        // Check if addition would overflow
        if (a <= MAX_U256 - b) {
            // No overflow, normal addition
            a + b
        } else {
            // Overflow would occur, wrap around
            // Calculate: (a + b) % (2^256) which is equivalent to (a + b) % (MAX_U256 + 1)
            let overflow_amount = b - (MAX_U256 - a);
            overflow_amount - 1
        }
    }

    /// Check if addition would cause overflow (utility function)
    public fun would_add_overflow(a: FixedPoint, b: FixedPoint): bool {
        a.value > MAX_U256 - b.value
    }

    public fun sqrt(x: FixedPoint): FixedPoint {
        if (x.value == 0) {
            return FixedPoint { value: 0 }
        };

        let result = sqrt_with_precision(x.value);
        FixedPoint { value: result }
    }

    // === Advanced Internal Algorithms ===

    /// Safe addition with overflow protection
    fun safe_add_u256(a: u256, b: u256): u256 {
        assert!(a <= MAX_U256 - b, E_OVERFLOW);
        a + b
    }

    /// Advanced multiplication with precision - handles all cases safely
    fun multiply_with_precision(a: u256, b: u256): u256 {
        if (a == 0 || b == 0) return 0;
        
        // Safe overflow check - validate BEFORE any operations
        if (a > MAX_U256 / PRECISION) return multiply_using_rational_arithmetic(a, b);
        if (b > MAX_U256 / PRECISION) return multiply_using_rational_arithmetic(a, b);
        
        // Additional safety: check the actual multiplication won't overflow
        let scaled_a = a * PRECISION;
        if (scaled_a / PRECISION != a) return multiply_using_rational_arithmetic(a, b);
        if (b > MAX_U256 / scaled_a) return multiply_using_rational_arithmetic(a, b);
        
        // Now safe to multiply
        (scaled_a * b) / (PRECISION * PRECISION)
    }

    /// Safe proportion calculation: (numerator * multiplier) / denominator
    /// Use this for liquidity proportions and simple ratio calculations
    /// Prevents precision loss while maintaining overflow protection
    public fun safe_proportion(numerator: u256, multiplier: u256, denominator: u256): u256 {
        assert!(denominator != 0, E_DIVIDE_BY_ZERO);
        assert!(numerator > 0 || multiplier > 0, E_INVALID_CONVERSION);
        
        if (numerator == 0 || multiplier == 0) {
            return 0
        };
        
        // Check for potential overflow in multiplication
        if (can_multiply_safely(numerator, multiplier)) {
            // Direct arithmetic for normal cases (faster and more precise)
            (numerator * multiplier) / denominator
        } else {
            // Use fixed-point math for very large numbers to prevent overflow
            let num_fp = new(numerator);
            let mult_fp = new(multiplier);
            let denom_fp = new(denominator);
            
            let result_fp = div(mul(num_fp, mult_fp), denom_fp);
            get_raw_value(result_fp)
        }
    }

    /// Enterprise-grade division with full mathematical domain support
    fun divide_with_precision(numerator: u256, denominator: u256): u256 {
        // Method 1: Direct multiplication (fastest for small numbers)
        if (can_multiply_safely(numerator, PRECISION)) {
            return (numerator * PRECISION) / denominator
        };

        // Method 2: Rational arithmetic for extreme cases
        if (numerator > MAX_VALUE) {
            return divide_using_rational_arithmetic(numerator, denominator)
        };

        // Method 3: Long division algorithm for large numbers
        divide_using_long_division(numerator, denominator)
    }

    /// Advanced square root with Newton-Raphson method and precision scaling
    fun sqrt_with_precision(x: u256): u256 {
        if (x < 4) {
            return if (x == 0) 0 else PRECISION
        };

        // Check if we can safely multiply by PRECISION
        if (can_multiply_safely(x, PRECISION)) {
            let scaled_x = x * PRECISION;
            return newton_raphson_sqrt(scaled_x)
        };

        // For very large numbers, use alternative method
        sqrt_for_large_numbers(x)
    }

    /// Newton-Raphson square root algorithm
    fun newton_raphson_sqrt(y: u256): u256 {
        let mut z = y;
        let mut x = (y + 1) / 2;
        
        // Newton-Raphson iteration: x = (x + y/x) / 2
        while (x < z) {
            z = x;
            x = (y / x + x) / 2;
            
            // Prevent infinite loops
            if (abs_diff(x, z) <= 1) {
                break
            };
        };
        z
    }

    /// Square root for very large numbers using bit manipulation
    fun sqrt_for_large_numbers(mut x: u256): u256 {
        if (x == 0) return 0;
        
        let mut result = 0u256;
        let mut bit = 1u256 << 254; // Start with the second-to-top bit set
        
        // Find the largest bit
        while (bit > x) {
            bit = bit >> 2;
        };
        
        while (bit != 0) {
            let temp = result + bit;
            result = result >> 1;
            
            if (x >= temp) {
                x = x - temp;
                result = result + bit;
            };
            bit = bit >> 2;
        };
        
        // Scale result back to fixed-point representation
        result * sqrt_internal(PRECISION)
    }

    /// Rational arithmetic multiplication for extreme cases
    /// Safe multiplication for extreme cases using division-based approach
    fun multiply_using_rational_arithmetic(a: u256, b: u256): u256 {
        // Use division-based approach instead of direct multiplication
        if (a > b) {
            let quotient = a / PRECISION;
            let remainder = a % PRECISION;
            // Check for overflow before multiplication
            assert!(quotient <= MAX_U256 / b, E_OVERFLOW);
            assert!(remainder <= MAX_U256 / b, E_OVERFLOW);
            quotient * b + (remainder * b) / PRECISION
        } else {
            let quotient = b / PRECISION;
            let remainder = b % PRECISION;
            // Check for overflow before multiplication
            assert!(quotient <= MAX_U256 / a, E_OVERFLOW);
            assert!(remainder <= MAX_U256 / a, E_OVERFLOW);
            quotient * a + (remainder * a) / PRECISION
        }
    }

    /// Safe division for extreme cases using division-based approach  
    fun divide_using_rational_arithmetic(numerator: u256, denominator: u256): u256 {
        // Use quotient and remainder approach to avoid large intermediate values
        let quotient = numerator / denominator;
        let remainder = numerator % denominator;
        
        // Check quotient contribution won't overflow
        assert!(quotient <= MAX_U256 / PRECISION, E_OVERFLOW);
        let quotient_contribution = quotient * PRECISION;
        
        // Calculate remainder contribution safely
        let remainder_contribution = (remainder * PRECISION) / denominator;
        
        // Safe final addition
        assert!(quotient_contribution <= MAX_U256 - remainder_contribution, E_OVERFLOW);
        quotient_contribution + remainder_contribution
    }

    /// Long division algorithm for cases where direct multiplication overflows
    fun divide_using_long_division(numerator: u256, denominator: u256): u256 {
        if (denominator == 0) {
            abort E_DIVIDE_BY_ZERO
        };

        let mut quotient = 0u256;
        let mut remainder = numerator;
        
        // Multiply numerator by PRECISION using long division approach
        let mut i = 0;
        let precision_bits = count_bits(PRECISION);
        
        while (i < precision_bits && remainder > 0) {
            remainder = remainder * 2;
            quotient = quotient * 2;
            
            if (remainder >= denominator) {
                remainder = remainder - denominator;
                quotient = quotient + 1;
            };
            i = i + 1;
        };
        
        // Additional precision iterations
        let additional_precision = 64; // Extra precision bits
        let mut j = 0;
        while (j < additional_precision && remainder > 0) {
            remainder = remainder * 2;
            quotient = quotient * 2;
            
            if (remainder >= denominator) {
                remainder = remainder - denominator;
                quotient = quotient + 1;
            };
            j = j + 1;
        };
        
        // Adjust quotient based on actual PRECISION
        adjust_quotient_for_precision(quotient, precision_bits + additional_precision)
    }

    /// Multiplication using decomposition for medium-sized numbers
    fun multiply_using_decomposition(a: u256, b: u256): u256 {
        // Decompose into high and low parts
        let a_high = a >> 128;
        let a_low = a & ((1u256 << 128) - 1);
        let b_high = b >> 128;
        let b_low = b & ((1u256 << 128) - 1);

        // Only one of the numbers can have a high part to prevent overflow
        assert!(a_high == 0 || b_high == 0, E_OVERFLOW);

        let mut result = (a_low * b_low) / PRECISION;

        if (a_high > 0) {
            let high_contribution = (a_high * b_low * (1u256 << 128)) / PRECISION;
            result = safe_add_u256(result, high_contribution);
        };

        if (b_high > 0) {
            let high_contribution = (a_low * b_high * (1u256 << 128)) / PRECISION;
            result = safe_add_u256(result, high_contribution);
        };

        result
    }

    // === Rational Arithmetic Helper Functions ===

    fun create_rational(numerator: u256, denominator: u256): Rational {
        assert!(denominator != 0, E_DIVIDE_BY_ZERO);
        Rational { numerator, denominator }
    }

    fun simplify_rational(rat: Rational): Rational {
        let gcd_val = gcd(rat.numerator, rat.denominator);
        Rational {
            numerator: rat.numerator / gcd_val,
            denominator: rat.denominator / gcd_val
        }
    }

    fun gcd(mut a: u256, mut b: u256): u256 {
        while (b != 0) {
            let temp = b;
            b = a % b;
            a = temp;
        };
        a
    }

    // === Safety Check Functions ===

    fun will_multiply_overflow(a: u256, b: u256): bool {
        if (a == 0 || b == 0) return false;
        a > MAX_U256 / b
    }

    fun can_multiply_safely(a: u256, b: u256): bool {
        if (a == 0 || b == 0) return true;
        a <= MAX_U256 / b
    }

    // === Utility Functions ===

    fun abs_diff(a: u256, b: u256): u256 {
        if (a > b) { a - b } else { b - a }
    }

    fun count_bits(mut n: u256): u8 {
        let mut count = 0u8;
        while (n > 0) {
            count = count + 1;
            n = n >> 1;
        };
        count
    }

    fun adjust_quotient_for_precision(quotient: u256, total_bits: u8): u256 {
        let precision_bits = count_bits(PRECISION);
        if (total_bits > precision_bits) {
            quotient >> (total_bits - precision_bits)
        } else {
            quotient << (precision_bits - total_bits)
        }
    }

    // === Legacy Internal Function (for compatibility) ===
    
    fun sqrt_internal(y: u256): u256 {
        if (y < 4) {
            if (y == 0) { 0 } else { 1 }
        } else {
            newton_raphson_sqrt(y)
        }
    }

    // === Additional Utility Functions (API Compatible) ===

    public fun min(a: FixedPoint, b: FixedPoint): FixedPoint {
        if (a.value < b.value) { a } else { b }
    }

    public fun max(a: FixedPoint, b: FixedPoint): FixedPoint {
        if (a.value > b.value) { a } else { b }
    }

    fun scale_to_precision(value: u256, decimals: u8): u256 {
        assert!(decimals <= 77, E_INVALID_DECIMALS);

        if (decimals == 18) return value;
        
        if (decimals < 18) {
            // Scale up
            let scale_factor = u256::pow(10u256, ((18 - decimals) as u8));
            assert!(can_multiply_safely(value, scale_factor), E_OVERFLOW);
            value * scale_factor
        } else {
            // Scale down WITH ROUNDING
            let scale_factor = u256::pow(10u256, ((decimals - 18) as u8));
            (value + (scale_factor / 2)) / scale_factor
        }
    }

    public fun compare(a: FixedPoint, b: FixedPoint): u8 {
        if (a.value < b.value) return 0;
        if (a.value > b.value) return 2;
        1
    }

    public fun is_zero(fp: FixedPoint): bool {
        fp.value == 0
    }

    public fun is_safe_value(value: u256): bool {
        value >= MIN_VALUE && value <= MAX_VALUE
    }

    public fun mul_small(a: FixedPoint, b: u64): FixedPoint {
        let b_256 = (b as u256);
        let result = multiply_with_precision(a.value, b_256 * PRECISION);
        FixedPoint { value: result }
    }

    // === Advanced Mathematical Functions ===

    /// Calculate percentage with overflow protection
    public fun percentage(amount: FixedPoint, percentage_bp: u64): FixedPoint {
        let bp_fixed = new((percentage_bp as u256) * PRECISION / 10000);
        mul(amount, bp_fixed)
    }

    /// Power function for small integer exponents
    public fun pow(base: FixedPoint, mut exponent: u64): FixedPoint {
        if (exponent == 0) return new(PRECISION);
        if (exponent == 1) return base;
        
        let mut result = new(PRECISION);
        let mut current_base = base;
        
        while (exponent > 0) {
            if (exponent % 2 == 1) {
                result = mul(result, current_base);
            };
            current_base = mul(current_base, current_base);
            exponent = exponent / 2;
        };
        
        result
    }


    // === Constants for Mathematical Operations ===

    /// Get precision constant
    public fun get_precision(): u256 {
        PRECISION
    }

    /// Get maximum safe value
    public fun get_max_value(): u256 {
        MAX_VALUE
    }
}