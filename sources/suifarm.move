#[allow(unused_variable)]
module suitrump_dex::farm {
    use sui::object::{Self, UID};
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use sui::event;
    use sui::table::{Self, Table};
    use sui::balance::{Self, Balance};
    use sui::coin::{Self, Coin, TreasuryCap};
    use std::string::{Self, String};
    use std::type_name::{Self, TypeName};
    use std::option::{Self, Option};
    use std::vector;
    use suitrump_dex::pair::{Self, Pair, LPCoin};
    use suitrump_dex::fixed_point_math::{Self, FixedPoint};
    use suitrump_dex::victory_token::{Self, VICTORY_TOKEN,TreasuryCapWrapper};
    use sui::clock::{Self, Clock};
    use suitrump_dex::factory::{Self, Factory};
    use suitrump_dex::global_emission_controller::{Self, GlobalEmissionConfig};

    // Error codes
    const ERROR_NOT_ADMIN: u64 = 1;
    const ERROR_ZERO_ADDRESS: u64 = 2;
    const ERROR_INVALID_FEE: u64 = 3;
    const ERROR_POOL_EXISTS: u64 = 4;
    const ERROR_POOL_NOT_FOUND: u64 = 5;
    const ERROR_INVALID_AMOUNT: u64 = 6;
    const ERROR_INACTIVE_POOL: u64 = 7;
    const ERROR_INSUFFICIENT_BALANCE: u64 = 8;
    const ERROR_NO_REWARDS: u64 = 9;
    const ERROR_NOT_OWNER: u64 = 10;
    const ERROR_CALCULATION_OVERFLOW: u64 = 11;
    const ERROR_INVALID_LP_TYPE: u64 = 12;
    const ERROR_MISSING_TREASURY_CAP: u64 = 13;
    const ERROR_INVALID_VAULT: u64 = 14;
    const ERROR_ALREADY_INITIALIZED: u64 = 15;
    // New emission-related error codes
    const ERROR_EMISSIONS_NOT_INITIALIZED: u64 = 16;
    const ERROR_EMISSIONS_PAUSED: u64 = 17;
    const ERROR_EMISSIONS_ENDED: u64 = 18;
    const ERROR_SINGLE_REWARDS_ENDED: u64 = 19;
    const ERROR_UPDATE_TOO_FREQUENT: u64 = 20;
    const ERROR_WEEK_156_EXCEEDED: u64 = 21;
    const ERROR_ACCUMULATOR_OVERFLOW: u64 = 22;
    const ERROR_MIN_STAKE_DURATION: u64 = 23;
    const ERROR_CLAIM_TOO_FREQUENT: u64 = 24;
    const ERROR_INSUFFICIENT_VAULT_BALANCE: u64 = 25;
    const ERROR_INVALID_SWEEP_AMOUNT: u64 = 26;

    const MAX_FEE: u256 = 1000; // 10% maximum fee in basis points (10000 = 100%)
    const BASIS_POINTS: u256 = 10000;
    const MIN_STAKE_AMOUNT: u256 = 1000000; // 0.001 tokens
    
    const MIN_UPDATE_INTERVAL: u64 = 0; // 0 seconds minimum
    const MIN_FARM_STAKE_DURATION: u64 = 3600; // 1 hour minimum
    const MIN_CLAIM_INTERVAL: u64 = 3600; // 1 hour between claims
    const PRECISION_FACTOR: u256 = 1000000000000000000u256; // 1e18 for MasterChef
    const SECONDS_PER_DAY: u64 = 86400;
    const SECONDS_PER_WEEK: u64 = 604800; // 7 * 24 * 60 * 60 (for timestamp calculations)
    const MAX_SAFE_ACCUMULATOR: u256 = 100000000000000000000000000000000000000000000000000000000000000000000000u256; // 1e70
    const DEFAULT_BATCH_SIZE: u64 = 5;
    const POOL_UPDATE_WINDOW: u64 = 28800; // 8 hours in seconds
    const ERROR_POOL_RECENTLY_UPDATED: u64 = 36;
    
    public struct FARM has drop {}

    /// Simple Reward Vault - holds Victory tokens for distribution
    public struct RewardVault has key {
        id: UID,
        victory_balance: Balance<VICTORY_TOKEN>,
    }

    /// Farm configuration object
    public struct Farm has key {
        id: UID,
        admin: address,
        pools: Table<TypeName, Pool>,
        pool_list: vector<TypeName>,
        burn_address: address,
        locker_address: address,
        team_address: address,
        dev_address: address,
        total_victory_distributed: u256,
        last_update_timestamp: u64,
        paused: bool,
        allowed_lp_types: Table<TypeName, bool>,
        
        total_allocation_points: u256,           
        total_lp_allocation_points: u256,        
        total_single_allocation_points: u256,    
        
        position_to_vault: Table<ID, ID>,
        user_positions: Table<address, Table<TypeName, vector<ID>>>,
        
        // Victory token distribution tracking
        total_lp_victory_distributed: u256,      // Track total LP Victory rewards distributed
        total_single_victory_distributed: u256,  // Track total single Victory rewards distributed
        emission_start_timestamp: u64,           // Cache emission start time
        emission_end_timestamp: u64,             // Cache emission end time
    }

    public struct Pool has store {
        pool_type: TypeName,
        total_staked: u256,
        allocation_points: u256,
        acc_reward_per_share: u256,            
        last_reward_time: u64,                 
        deposit_fee: u256,
        withdrawal_fee: u256,
        active: bool,
        stakers: Table<address, Staker>,
        is_native_pair: bool,
        is_lp_token: bool,
        accumulated_deposit_fees: u256,
        accumulated_withdrawal_fees: u256
    }

    /// Individual staker information
    public struct Staker has store, drop {
        amount: u256,                          
        reward_debt: u256,                     
        rewards_claimed: u256,
        last_stake_timestamp: u64,
        last_claim_timestamp: u64
    }

    public struct StakedTokenVault<phantom T> has key {
        id: UID,
        balance: Balance<T>,
        owner: address,
        pool_type: TypeName,
        amount: u256,
        initial_stake_timestamp: u64
    }

    /// Staking position NFT that represents a user's stake
    public struct StakingPosition<phantom T> has key {
        id: UID,
        owner: address,
        pool_type: TypeName,
        amount: u256,
        initial_stake_timestamp: u64,
        vault_id: ID // ID of the corresponding vault
    }

    public struct PositionSummary has copy, drop {
        id: ID,
        token_type: TypeName,
        amount: u256
    }

    // Events
    public struct PoolCreated has copy, drop {
        pool_type: TypeName,
        allocation_points: u256,
        deposit_fee: u256,
        withdrawal_fee: u256,
        is_native_pair: bool,
        is_lp_token: bool
    }

    public struct LPTypeAllowed has copy, drop {
        lp_type: TypeName
    }

    public struct Staked has copy, drop {
        staker: address,
        pool_type: TypeName,
        amount: u256,
        timestamp: u64
    }

    public struct Unstaked has copy, drop {
        staker: address,
        pool_type: TypeName,
        amount: u256,
        timestamp: u64
    }

    public struct RewardClaimed has copy, drop {
        staker: address,
        pool_type: TypeName,
        amount: u256,
        timestamp: u64
    }

    public struct FeesCollected has copy, drop {
        pool_type: TypeName,
        amount: u256,
        fee_type: String, // "deposit" or "withdrawal"
        timestamp: u64
    }

    public struct VaultDeposit has copy, drop {
        amount: u256,
        total_balance: u256,
        timestamp: u64
    }

    public struct AdminVaultSweep has copy, drop {
        amount: u256,
        recipient: address,
        remaining_vault_balance: u256,
        timestamp: u64
    }

    // New emission-related events
    public struct EmissionWarning has copy, drop {
        message: String,
        pool_type: Option<TypeName>,
        timestamp: u64
    }

    public struct EmissionStatusChange has copy, drop {
        old_status: String,
        new_status: String,
        week_number: u64,
        timestamp: u64
    }

    public struct FarmVictoryDistributionTracking has copy, drop {
        lp_victory_distributed: u256,
        single_victory_distributed: u256,
        total_victory_distributed: u256,
        timestamp: u64,
    }

    public struct PoolConfigUpdated has copy, drop {
        pool_type: TypeName,
        old_allocation_points: u256,
        new_allocation_points: u256,
        old_deposit_fee: u256,
        new_deposit_fee: u256,
        old_withdrawal_fee: u256,
        new_withdrawal_fee: u256,
        old_active: bool,
        new_active: bool,
        timestamp: u64
    }

    public struct AdminAddressesUpdated has copy, drop {
        old_burn_address: address,
        new_burn_address: address,
        old_locker_address: address,
        new_locker_address: address,
        old_team_address: address,
        new_team_address: address,
        old_dev_address: address,
        new_dev_address: address,
        timestamp: u64
    }

    public struct FarmPauseStateChanged has copy, drop {
        old_paused: bool,
        new_paused: bool,
        admin: address,
        timestamp: u64
    }

    public struct FarmInitialized has copy, drop {
        admin: address,
        initial_timestamp: u64
    }

    public struct EmissionsPausedSafely has copy, drop {
        pools_updated_count: u64,
        admin: address,
        timestamp: u64
    }

    public struct RewardVaultCreated has copy, drop {
        vault_id: ID,
        admin: address,
        timestamp: u64
    }

    public struct PoolBatchUpdateCompleted has copy, drop {
        batch_start_index: u64,
        batch_end_index: u64,
        pools_updated_in_batch: u64,
        total_pools_in_farm: u64,
        has_more_batches: bool,
        next_batch_start_index: u64,
        timestamp: u64
    }

    public struct PoolUpdated has copy, drop {
        pool_type: TypeName,
        timestamp: u64
    }

    fun init(witness: FARM, ctx: &mut TxContext) {
        let sender = tx_context::sender(ctx);
        
        let farm = Farm {
            id: object::new(ctx),
            admin: sender,
            pools: table::new(ctx),
            pool_list: vector::empty(),
            burn_address: @0x0,
            locker_address: sender,
            team_address: sender,
            dev_address: sender,
            total_victory_distributed: 0,
            last_update_timestamp: 0,
            paused: false,
            allowed_lp_types: table::new(ctx),
            total_allocation_points: 0,
            total_lp_allocation_points: 0,
            total_single_allocation_points: 0,
            position_to_vault: table::new(ctx),
            user_positions: table::new(ctx),
            total_lp_victory_distributed : 0,
            total_single_victory_distributed : 0,
            emission_start_timestamp : 0,
            emission_end_timestamp : 0,
        };
        
        transfer::share_object(farm);
        
        transfer::transfer(
            AdminCap {
                id: object::new(ctx)
            },
            sender
        );
    }

    /// Admin capability for privileged operations
    public struct AdminCap has key, store {
        id: UID
    }

    // === EMISSION INTEGRATION HELPERS ===
    
    /// Validate emission state safely
    fun validate_emission_state(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): (bool, bool, bool) {
        let (start_timestamp, is_paused) = global_emission_controller::get_config_info(global_config);
        let is_initialized = start_timestamp > 0;
        let is_active = global_emission_controller::is_emissions_active(global_config, clock);
        
        (is_initialized, is_active, is_paused)
    }

    /// Get allocations safely - returns (0,0) if any emission issue
    fun get_allocations_safe(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): (u256, u256) {
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        
        if (!is_initialized || !is_active || is_paused) {
            return (0, 0) // No allocations if any issue (controller handles week 156 in is_active)
        };
        
        // Safe to call Global Controller now
        global_emission_controller::get_farm_allocations(global_config, clock)
    }

    // === VAULT FUNCTIONS ===
    
    /// Create reward vault for Victory token distribution
    public entry fun create_reward_vault(
        _admin: &AdminCap,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let vault_id = object::new(ctx);
        let vault = RewardVault {
            id: vault_id,
            victory_balance: balance::zero<VICTORY_TOKEN>(),
        };
        
        let vault_uid = object::uid_to_inner(&vault.id);
        transfer::share_object(vault);
        
        // 🆕 EMIT EVENT
        event::emit(RewardVaultCreated {
            vault_id: vault_uid,
            admin: tx_context::sender(ctx),
            timestamp: clock::timestamp_ms(clock) / 1000
        });
    }

    /// Deposit Victory tokens into vault for distribution
    public entry fun deposit_victory_tokens(
        vault: &mut RewardVault,
        tokens: Coin<VICTORY_TOKEN>,
        _admin: &AdminCap,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let amount = coin::value(&tokens);
        assert!(amount > 0, ERROR_INVALID_AMOUNT);
        
        balance::join(&mut vault.victory_balance, coin::into_balance(tokens));
        
        let current_time = clock::timestamp_ms(clock) / 1000;
        
        event::emit(VaultDeposit {
            amount: (amount as u256),
            total_balance: balance::value(&vault.victory_balance) as u256,
            timestamp: current_time
        });
    }

    /// Distribute rewards from vault
    // In distribute_from_vault, handle insufficient balance gracefully
    fun distribute_from_vault(
        vault: &mut RewardVault,
        farm: &mut Farm,                  
        amount: u256,
        recipient: address,
        is_lp_victory_reward: bool,        
        global_config: &GlobalEmissionConfig, 
        clock: &Clock,                     
        ctx: &mut TxContext
    ) {
        // Emission validation (warn but don't block earned rewards)
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        
        if (!is_initialized || is_paused) {
            // Block distribution if not initialized or paused
            return
        };
        
        if (!is_active) {
            // Warn but allow distribution of earned rewards after emission ends
            event::emit(EmissionWarning {
                message: string::utf8(b"Distributing earned Victory rewards after emission end"),
                pool_type: option::none(),
                timestamp: clock::timestamp_ms(clock) / 1000,
            });
        };
        
        let amount_u64 = if (amount > 18446744073709551615) {
            18446744073709551615
        } else {
            (amount as u64)
        };
        
        let available = balance::value(&vault.victory_balance);
        
        if (available < amount_u64) {
            // Only distribute what's available
            if (available > 0) {
                let reward_balance = balance::split(&mut vault.victory_balance, available);
                let reward_coin = coin::from_balance(reward_balance, ctx);
                transfer::public_transfer(reward_coin, recipient);
                
                // Track partial distribution
                track_victory_distribution(farm, (available as u256), is_lp_victory_reward, clock);
            };
            return
        };
        
        // Normal distribution
        let reward_balance = balance::split(&mut vault.victory_balance, amount_u64);
        let reward_coin = coin::from_balance(reward_balance, ctx);
        transfer::public_transfer(reward_coin, recipient);
        
        // Track full distribution
        track_victory_distribution(farm, amount, is_lp_victory_reward, clock);
    }

    // === REWARD CALCULATION ===
    
    fun calculate_pool_reward(
        is_lp_token: bool,
        allocation_points: u256,
        total_lp_allocation_points: u256,
        total_single_allocation_points: u256,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        time_elapsed: u256
    ): u256 {
        let current_time = clock::timestamp_ms(clock) / 1000;
        let start_time = current_time - (time_elapsed as u64);
        
        calculate_farm_rewards_multi_week(
            is_lp_token,
            allocation_points,
            total_lp_allocation_points,
            total_single_allocation_points,
            global_config,
            start_time,
            current_time
        )
    }

    // === Admin Functions ===
    public entry fun initialize_timestamps(
        farm: &mut Farm,
        _admin: &AdminCap,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        assert!(farm.last_update_timestamp == 0, ERROR_ALREADY_INITIALIZED);
        
        let current_time = clock::timestamp_ms(clock) / 1000;
        farm.last_update_timestamp = current_time;
        
        // 🆕 EMIT EVENT
        event::emit(FarmInitialized {
            admin: tx_context::sender(ctx),
            initial_timestamp: current_time
        });
    }

    public entry fun create_single_asset_pool<T>(
        farm: &mut Farm,
        allocation_points: u256,
        deposit_fee: u256,
        withdrawal_fee: u256,
        is_native_token: bool,
        _admin: &AdminCap,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let pool_type = type_name::get<T>();
        assert!(!table::contains(&farm.pools, pool_type), ERROR_POOL_EXISTS);
        assert!(deposit_fee <= MAX_FEE && withdrawal_fee <= MAX_FEE, ERROR_INVALID_FEE);
        
        let pool = Pool {
            pool_type,
            total_staked: 0,
            allocation_points,
            acc_reward_per_share: 0,
            last_reward_time: clock::timestamp_ms(clock) / 1000,
            deposit_fee,
            withdrawal_fee,
            active: true,
            stakers: table::new(ctx),
            is_native_pair: is_native_token,
            is_lp_token: false,
            accumulated_deposit_fees: 0,
            accumulated_withdrawal_fees: 0
        };
        
        farm.total_allocation_points = farm.total_allocation_points + allocation_points;
        farm.total_single_allocation_points = farm.total_single_allocation_points + allocation_points;
        
        table::add(&mut farm.pools, pool_type, pool);
        vector::push_back(&mut farm.pool_list, pool_type);
        
        event::emit(PoolCreated {
            pool_type,
            allocation_points,
            deposit_fee,
            withdrawal_fee,
            is_native_pair: is_native_token,
            is_lp_token: false
        });
    }
    
    public entry fun create_lp_pool<T0, T1>(
        farm: &mut Farm,
        allocation_points: u256,
        deposit_fee: u256,
        withdrawal_fee: u256,
        is_native_pair: bool,
        _admin: &AdminCap,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sorted_pair = factory::sort_tokens<T0, T1>();
        let is_sorted = factory::is_token0<T0>(&sorted_pair);

        if (is_sorted) {
            create_lp_pool_sorted<T0, T1>(
                farm,
                allocation_points,
                deposit_fee,
                withdrawal_fee,
                is_native_pair,
                clock,
                ctx
            );
        } else {
            create_lp_pool_sorted<T1, T0>(
                farm,
                allocation_points,
                deposit_fee,
                withdrawal_fee,
                is_native_pair,
                clock,
                ctx
            );
        };
    }

    fun create_lp_pool_sorted<T0, T1>(
        farm: &mut Farm,
        allocation_points: u256,
        deposit_fee: u256,
        withdrawal_fee: u256,
        is_native_pair: bool,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let lp_type = type_name::get<LPCoin<T0, T1>>();
        assert!(!table::contains(&farm.pools, lp_type), ERROR_POOL_EXISTS);
        assert!(deposit_fee <= MAX_FEE, ERROR_INVALID_FEE);
        assert!(withdrawal_fee <= MAX_FEE, ERROR_INVALID_FEE);

        let current_time = clock::timestamp_ms(clock) / 1000;

        let pool = Pool {
            pool_type: lp_type,
            total_staked: 0,
            allocation_points,
            acc_reward_per_share: 0,
            last_reward_time: current_time,
            deposit_fee,
            withdrawal_fee,
            active: true,
            stakers: table::new(ctx),
            is_native_pair,
            is_lp_token: true,
            accumulated_deposit_fees: 0,
            accumulated_withdrawal_fees: 0
        };
        
        farm.total_allocation_points = farm.total_allocation_points + allocation_points;
        farm.total_lp_allocation_points = farm.total_lp_allocation_points + allocation_points;
        
        table::add(&mut farm.pools, lp_type, pool);
        vector::push_back(&mut farm.pool_list, lp_type);
        
        table::add(&mut farm.allowed_lp_types, lp_type, true);
        
        event::emit(PoolCreated {
            pool_type: lp_type,
            allocation_points,
            deposit_fee,
            withdrawal_fee,
            is_native_pair,
            is_lp_token: true
        });
        
        event::emit(LPTypeAllowed {
            lp_type
        });
    }

    public entry fun allow_lp_type<T0, T1>(
        farm: &mut Farm,
        _admin: &AdminCap
    ) {
        let lp_type = type_name::get<LPCoin<T0, T1>>();
        
        if (!table::contains(&farm.allowed_lp_types, lp_type)) {
            table::add(&mut farm.allowed_lp_types, lp_type, true);
            
            event::emit(LPTypeAllowed {
                lp_type
            });
        };
    }

    public entry fun update_pool_config<T>(
        farm: &mut Farm,
        allocation_points: u256,
        deposit_fee: u256,
        withdrawal_fee: u256,
        active: bool,
        _admin: &AdminCap,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let pool_type = type_name::get<T>();
        assert!(table::contains(&farm.pools, pool_type), ERROR_POOL_NOT_FOUND);
        assert!(deposit_fee <= MAX_FEE && withdrawal_fee <= MAX_FEE, ERROR_INVALID_FEE);
        
        let current_time = clock::timestamp_ms(clock) / 1000;
        farm.last_update_timestamp = current_time;
        
        let pool = table::borrow_mut(&mut farm.pools, pool_type);
        
        let current_total_lp = farm.total_lp_allocation_points;
        let current_total_single = farm.total_single_allocation_points;
        
        let old_allocation = pool.allocation_points;
        let old_deposit_fee = pool.deposit_fee;
        let old_withdrawal_fee = pool.withdrawal_fee;
        let old_active = pool.active;

        update_pool_accumulator_masterchef(
            pool, 
            current_total_lp, 
            current_total_single, 
            global_config, 
            clock, 
            current_time
        );
        
        let old_allocation = pool.allocation_points;
        farm.total_allocation_points = farm.total_allocation_points - old_allocation + allocation_points;
        
        if (pool.is_lp_token) {
            farm.total_lp_allocation_points = farm.total_lp_allocation_points - old_allocation + allocation_points;
        } else {
            farm.total_single_allocation_points = farm.total_single_allocation_points - old_allocation + allocation_points;
        };
        
        pool.allocation_points = allocation_points;
        pool.deposit_fee = deposit_fee;
        pool.withdrawal_fee = withdrawal_fee;
        pool.active = active;

        event::emit(PoolConfigUpdated {
            pool_type,
            old_allocation_points: old_allocation,
            new_allocation_points: allocation_points,
            old_deposit_fee,
            new_deposit_fee: deposit_fee,
            old_withdrawal_fee,
            new_withdrawal_fee: withdrawal_fee,
            old_active,
            new_active: active,
            timestamp: current_time
        });
    }

    public entry fun admin_sweep_vault_tokens(
        vault: &mut RewardVault,
        sweep_amount: u64,
        recipient_address: address,
        _admin: &AdminCap,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let vault_balance = balance::value(&vault.victory_balance);
        assert!(vault_balance >= sweep_amount, ERROR_INSUFFICIENT_VAULT_BALANCE);
        assert!(sweep_amount > 0, ERROR_INVALID_SWEEP_AMOUNT);
        
        let token_balance = balance::split(&mut vault.victory_balance, sweep_amount);
        let token_coin = coin::from_balance(token_balance, ctx);
        transfer::public_transfer(token_coin, recipient_address);
        
        event::emit(AdminVaultSweep {
            amount: (sweep_amount as u256),
            recipient: recipient_address,
            remaining_vault_balance: balance::value(&vault.victory_balance) as u256,
            timestamp: clock::timestamp_ms(clock) / 1000
        });
    }

    public entry fun set_addresses(
        farm: &mut Farm,
        burn_address: address,
        locker_address: address,
        team_address: address,
        dev_address: address,
        _admin: &AdminCap,
        clock: &Clock 
    ) {
        assert!(
            burn_address != @0x0 && 
            locker_address != @0x0 && 
            team_address != @0x0 &&
            dev_address != @0x0,
            ERROR_ZERO_ADDRESS
        );
        
        // 🆕 STORE OLD VALUES
        let old_burn = farm.burn_address;
        let old_locker = farm.locker_address;
        let old_team = farm.team_address;
        let old_dev = farm.dev_address;
        
        farm.burn_address = burn_address;
        farm.locker_address = locker_address;
        farm.team_address = team_address;
        farm.dev_address = dev_address;
        
        // 🆕 EMIT EVENT
        event::emit(AdminAddressesUpdated {
            old_burn_address: old_burn,
            new_burn_address: burn_address,
            old_locker_address: old_locker,
            new_locker_address: locker_address,
            old_team_address: old_team,
            new_team_address: team_address,
            old_dev_address: old_dev,
            new_dev_address: dev_address,
            timestamp: clock::timestamp_ms(clock) / 1000
        });
    }

    public entry fun set_pause_state(
        farm: &mut Farm, 
        paused: bool,
        _admin: &AdminCap,
        clock: &Clock,  
        ctx: &mut TxContext  
    ) {
        let old_paused = farm.paused;
        farm.paused = paused;
        
        // 🆕 EMIT EVENT
        event::emit(FarmPauseStateChanged {
            old_paused,
            new_paused: paused,
            admin: tx_context::sender(ctx),
            timestamp: clock::timestamp_ms(clock) / 1000
        });
    }

    public entry fun pause_emissions_safely(
        farm: &mut Farm,
        global_config: &mut GlobalEmissionConfig,
        global_admin_cap: &global_emission_controller::AdminCap,
        _admin: &AdminCap,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        // Pause emissions immediately without mass update
        global_emission_controller::set_pause_state(
            global_admin_cap,
            global_config,
            true,
            clock,
            ctx
        );
        
        event::emit(EmissionsPausedSafely {
            pools_updated_count: 0,
            admin: tx_context::sender(ctx),
            timestamp: clock::timestamp_ms(clock) / 1000
        });
    }

    public entry fun mass_update_pools_batch(
        farm: &mut Farm,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        start_index: u64,
        batch_size: u64,
        _admin: &AdminCap,
        ctx: &mut TxContext
    ): (bool, u64) {
        let current_time = clock::timestamp_ms(clock) / 1000;
        let total_pools = vector::length(&farm.pool_list);
        let actual_batch_size = if (batch_size == 0) DEFAULT_BATCH_SIZE else batch_size;
        
        let end_index = if (start_index + actual_batch_size > total_pools) {
            total_pools
        } else {
            start_index + actual_batch_size
        };
        
        assert!(start_index < total_pools, ERROR_POOL_NOT_FOUND);
        
        let total_lp_allocation_points = farm.total_lp_allocation_points;
        let total_single_allocation_points = farm.total_single_allocation_points;
        
        let mut i = start_index;
        let mut pools_updated = 0;
        
        while (i < end_index) {
            let pool_type = *vector::borrow(&farm.pool_list, i);
            let pool = table::borrow_mut(&mut farm.pools, pool_type);
            
            if (pool.total_staked > 0 && pool.active) {
                update_pool_accumulator_masterchef(
                    pool, 
                    total_lp_allocation_points, 
                    total_single_allocation_points, 
                    global_config, 
                    clock, 
                    current_time
                );
                pools_updated = pools_updated + 1;
            };
            
            i = i + 1;
        };
        
        let has_more = end_index < total_pools;
        let next_start_index = if (has_more) end_index else 0;
        
        event::emit(PoolBatchUpdateCompleted {
            batch_start_index: start_index,
            batch_end_index: end_index,
            pools_updated_in_batch: pools_updated,
            total_pools_in_farm: total_pools,
            has_more_batches: has_more,
            next_batch_start_index: next_start_index,
            timestamp: current_time
        });
        
        (has_more, next_start_index)
    }

    public entry fun update_single_pool<T>(
        farm: &mut Farm,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let pool_type = type_name::get<T>();
        assert!(table::contains(&farm.pools, pool_type), ERROR_POOL_NOT_FOUND);
        
        let current_time = clock::timestamp_ms(clock) / 1000;
        
        let total_lp_allocation_points = farm.total_lp_allocation_points;
        let total_single_allocation_points = farm.total_single_allocation_points;
        
        let pool = table::borrow_mut(&mut farm.pools, pool_type);
        
        if (pool.total_staked > 0 && pool.active) {
            update_pool_accumulator_masterchef(
                pool, 
                total_lp_allocation_points, 
                total_single_allocation_points, 
                global_config, 
                clock, 
                current_time
            );
            
            event::emit(PoolUpdated {
                pool_type,
                timestamp: current_time
            });
        };
    }

    // === User Functions ===

    public entry fun stake_lp<T0, T1>(
        farm: &mut Farm,
        vault: &mut RewardVault,
        mut lp_tokens: vector<Coin<LPCoin<T0, T1>>>,
        amount: u256,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        assert!(!farm.paused, ERROR_INACTIVE_POOL);
        assert!(amount >= MIN_STAKE_AMOUNT, ERROR_INVALID_AMOUNT);

        let lp_type = type_name::get<LPCoin<T0, T1>>();
        assert!(table::contains(&farm.pools, lp_type), ERROR_POOL_NOT_FOUND);
        assert!(table::contains(&farm.allowed_lp_types, lp_type), ERROR_INVALID_LP_TYPE);

        validate_new_stake_allowed(global_config, clock);
        
        let current_time = clock::timestamp_ms(clock) / 1000;
        farm.last_update_timestamp = current_time;
        let sender = tx_context::sender(ctx);
        
        // Extract farm values BEFORE borrowing pool
        let total_lp_allocation_points = farm.total_lp_allocation_points;
        let total_single_allocation_points = farm.total_single_allocation_points;
        let mut farm_total_victory_distributed = farm.total_victory_distributed;
        
        let pool = table::borrow_mut(&mut farm.pools, lp_type);
        assert!(pool.active, ERROR_INACTIVE_POOL);
        
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);

        if (pool.total_staked > 0) {
            update_pool_accumulator_masterchef(
                pool, 
                total_lp_allocation_points, 
                total_single_allocation_points, 
                global_config, 
                clock, 
                current_time
            );
        };
        
        let pending_rewards = if (table::contains(&pool.stakers, sender)) {
            let staker = table::borrow(&pool.stakers, sender);
            calculate_pending_rewards_masterchef(staker.amount, staker.reward_debt, pool.acc_reward_per_share)
        } else { 0 };
        
        let fee_amount = (amount * pool.deposit_fee) / BASIS_POINTS;
        let stake_amount = amount - fee_amount;
        
        // Handle LP token merging
        let mut merged_lp = vector::pop_back(&mut lp_tokens);
        while (!vector::is_empty(&lp_tokens)) {
            coin::join(&mut merged_lp, vector::pop_back(&mut lp_tokens));
        };
        vector::destroy_empty(lp_tokens);
        
        let total_value = (coin::value(&merged_lp) as u256);
        assert!(total_value >= amount, ERROR_INSUFFICIENT_BALANCE);
        
        // Initialize staker if needed
        if (!table::contains(&pool.stakers, sender)) {
            table::add(&mut pool.stakers, sender, Staker {
                amount: 0,
                reward_debt: 0,
                rewards_claimed: 0,
                last_stake_timestamp: current_time,
                last_claim_timestamp: current_time
            });
        };
        
        // Handle deposit fees
        if (fee_amount > 0) {
            let mut fee_coin = coin::split(&mut merged_lp, (fee_amount as u64), ctx);
            let fee_amount_u256 = (coin::value(&fee_coin) as u256);
            let burn_amount = (fee_amount_u256 * 4000) / 10000;
            let locker_amount = (fee_amount_u256 * 4000) / 10000;
            let team_amount = (fee_amount_u256 * 1000) / 10000;
            let dev_amount = fee_amount_u256 - burn_amount - locker_amount - team_amount;
            
            if (burn_amount > 0) {
                let burn_coin = coin::split(&mut fee_coin, (burn_amount as u64), ctx);
                transfer::public_transfer(burn_coin, farm.burn_address);
            };
            if (locker_amount > 0) {
                let locker_coin = coin::split(&mut fee_coin, (locker_amount as u64), ctx);
                transfer::public_transfer(locker_coin, farm.locker_address);
            };
            if (team_amount > 0) {
                let team_coin = coin::split(&mut fee_coin, (team_amount as u64), ctx);
                transfer::public_transfer(team_coin, farm.team_address);
            };
            transfer::public_transfer(fee_coin, farm.dev_address);
            
            pool.accumulated_deposit_fees = pool.accumulated_deposit_fees + fee_amount_u256;
            
            event::emit(FeesCollected {
                pool_type: pool.pool_type,
                amount: fee_amount_u256,
                fee_type: string::utf8(b"deposit"),
                timestamp: current_time
            });
        };
        
        // Create vault and position
        let stake_coin = coin::split(&mut merged_lp, (stake_amount as u64), ctx);
        let vault_id = object::new(ctx);
        let token_vault = StakedTokenVault<LPCoin<T0, T1>> {
            id: vault_id,
            balance: coin::into_balance(stake_coin),
            owner: sender,
            pool_type: lp_type,
            amount: stake_amount,
            initial_stake_timestamp: current_time
        };
        
        // Return remaining tokens if any
        if (coin::value(&merged_lp) > 0) {
            transfer::public_transfer(merged_lp, sender);
        } else {
            coin::destroy_zero(merged_lp);
        };
        
        let position_id = object::new(ctx);
        let position = StakingPosition<LPCoin<T0, T1>> {
            id: position_id,
            owner: sender,
            pool_type: lp_type,
            amount: stake_amount,
            initial_stake_timestamp: current_time,
            vault_id: object::uid_to_inner(&token_vault.id)
        };
        
        let position_uid_bytes = object::uid_to_inner(&position.id);
        
        // Update farm user positions
        if (!table::contains(&mut farm.user_positions, sender)) {
            table::add(&mut farm.user_positions, sender, table::new(ctx));
        };
        
        let user_table = table::borrow_mut(&mut farm.user_positions, sender);
        if (!table::contains(user_table, lp_type)) {
            table::add(user_table, lp_type, vector::empty<ID>());
        };
        
        let positions = table::borrow_mut(user_table, lp_type);
        vector::push_back(positions, position_uid_bytes);
        table::add(&mut farm.position_to_vault, position_uid_bytes, object::uid_to_inner(&token_vault.id));
        
        transfer::share_object(token_vault);
        transfer::transfer(position, sender);
        
        // Update staker info
        let staker = table::borrow_mut(&mut pool.stakers, sender);
        staker.amount = staker.amount + stake_amount;
        staker.last_stake_timestamp = current_time;
        
        // Update pool total
        pool.total_staked = pool.total_staked + stake_amount;
        
        // Handle pending rewards AFTER updating staker amount but BEFORE updating debt
        if (pending_rewards > 0) {
            farm_total_victory_distributed = farm_total_victory_distributed + pending_rewards;
            
            // Update staker reward debt with new amount
            staker.reward_debt = safe_mul_div_u256(staker.amount, pool.acc_reward_per_share, PRECISION_FACTOR);
            
            // Update farm total BEFORE calling distribute_from_vault
            farm.total_victory_distributed = farm_total_victory_distributed;
            
            distribute_from_vault(vault, farm, pending_rewards, sender, true, global_config, clock, ctx);
            
            // Re-borrow for final staker updates
            let pool_ref = table::borrow_mut(&mut farm.pools, lp_type);
            let staker_ref = table::borrow_mut(&mut pool_ref.stakers, sender);
            staker_ref.rewards_claimed = staker_ref.rewards_claimed + pending_rewards;
            staker_ref.last_claim_timestamp = current_time;
            
            event::emit(RewardClaimed {
                staker: sender,
                pool_type: lp_type,
                amount: pending_rewards,
                timestamp: current_time
            });
        } else {
            // No pending rewards - just update debt
            staker.reward_debt = safe_mul_div_u256(staker.amount, pool.acc_reward_per_share, PRECISION_FACTOR);
        };
        
        event::emit(Staked {
            staker: sender,
            pool_type: lp_type,
            amount: stake_amount,
            timestamp: current_time
        });
    }

    public entry fun unstake_lp<T0, T1>(
        farm: &mut Farm,
        vault: &mut RewardVault,
        mut position: StakingPosition<LPCoin<T0, T1>>,
        token_vault: &mut StakedTokenVault<LPCoin<T0, T1>>,
        amount: u256,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        assert!(!farm.paused, ERROR_INACTIVE_POOL);

        validate_unstake_allowed(global_config, clock);

        let lp_type = type_name::get<LPCoin<T0, T1>>();
        assert!(table::contains(&farm.pools, lp_type), ERROR_POOL_NOT_FOUND);
        
        let sender = tx_context::sender(ctx);
        assert!(position.owner == sender, ERROR_NOT_OWNER);
        
        let position_id = object::uid_to_inner(&position.id);
        let vault_id = object::uid_to_inner(&token_vault.id);
        assert!(position.vault_id == vault_id, ERROR_INVALID_VAULT);
        
        assert!(token_vault.owner == sender, ERROR_NOT_OWNER);
        assert!(position.amount == token_vault.amount, ERROR_CALCULATION_OVERFLOW);
        assert!(amount > 0 && amount <= position.amount, ERROR_INVALID_AMOUNT);
        assert!(balance::value(&token_vault.balance) >= (amount as u64), ERROR_INSUFFICIENT_BALANCE);
        
        let current_time = clock::timestamp_ms(clock) / 1000;
        farm.last_update_timestamp = current_time;
        
        // Extract farm values BEFORE borrowing pool
        let total_lp_allocation_points = farm.total_lp_allocation_points;
        let total_single_allocation_points = farm.total_single_allocation_points;
        let mut farm_total_victory_distributed = farm.total_victory_distributed;
        
        let pool = table::borrow_mut(&mut farm.pools, lp_type);
        assert!(pool.active, ERROR_INACTIVE_POOL);
        
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        
        if (pool.total_staked > 0) {
            update_pool_accumulator_masterchef(
                pool, 
                total_lp_allocation_points, 
                total_single_allocation_points, 
                global_config, 
                clock, 
                current_time
            );
        };
        
        let mut pending_rewards = 0;
        
        if (table::contains(&pool.stakers, sender)) {
            let staker = table::borrow(&pool.stakers, sender);
            let min_unstake_time = staker.last_stake_timestamp + MIN_FARM_STAKE_DURATION;
            assert!(
                current_time >= min_unstake_time,
                ERROR_MIN_STAKE_DURATION
            );
            if (staker.amount > 0 && is_initialized && is_active && !is_paused) {
                pending_rewards = calculate_pending_rewards_masterchef(staker.amount, staker.reward_debt, pool.acc_reward_per_share);
            } else if (staker.amount > 0) {
                let warning_msg = if (!is_initialized) {
                    string::utf8(b"Unstaking allowed but no rewards - emissions not started")
                } else if (!is_active) {
                    string::utf8(b"Unstaking allowed but no rewards - emissions ended")  
                } else {
                    string::utf8(b"Unstaking allowed but no rewards - emissions paused")
                };
                
                event::emit(EmissionWarning {
                    message: warning_msg,
                    pool_type: option::some(lp_type),
                    timestamp: current_time
                });
            };
        };
        
        let fee_amount = (amount * pool.withdrawal_fee) / BASIS_POINTS;
        let unstake_amount = amount - fee_amount;
        
        let fee_amount_u64 = (fee_amount as u64);
        let unstake_amount_u64 = (unstake_amount as u64);
        
        // Update staker and pool state BEFORE distribution
        if (table::contains(&pool.stakers, sender)) {
            let staker_ref = table::borrow_mut(&mut pool.stakers, sender);
            staker_ref.amount = staker_ref.amount - amount;
            staker_ref.reward_debt = safe_mul_div_u256(staker_ref.amount, pool.acc_reward_per_share, PRECISION_FACTOR);
        };
        
        pool.total_staked = pool.total_staked - amount;
        
        // Handle pending rewards AFTER pool updates
        if (pending_rewards > 0) {
            farm_total_victory_distributed = farm_total_victory_distributed + pending_rewards;
            farm.total_victory_distributed = farm_total_victory_distributed;
            
            distribute_from_vault(vault, farm, pending_rewards, sender, true, global_config, clock, ctx);
            
            // Re-borrow for final staker updates
            let pool_ref = table::borrow_mut(&mut farm.pools, lp_type);
            if (table::contains(&pool_ref.stakers, sender)) {
                let staker_ref = table::borrow_mut(&mut pool_ref.stakers, sender);
                staker_ref.rewards_claimed = staker_ref.rewards_claimed + pending_rewards;
                staker_ref.last_claim_timestamp = current_time;
            };
            
            event::emit(RewardClaimed {
                staker: sender,
                pool_type: lp_type,
                amount: pending_rewards,
                timestamp: current_time
            });
        };
        
        // Handle token transfers
        if (unstake_amount_u64 > 0) {
            let unstake_coin = coin::from_balance(
                balance::split(&mut token_vault.balance, unstake_amount_u64),
                ctx
            );
            transfer::public_transfer(unstake_coin, sender);
        };
        
        // Handle withdrawal fees
        if (fee_amount_u64 > 0) {
            let fee_balance = balance::split(&mut token_vault.balance, fee_amount_u64);
            let mut fee_coin = coin::from_balance(fee_balance, ctx);
            let fee_amount_u256 = (coin::value(&fee_coin) as u256);
            
            let burn_amount = (fee_amount_u256 * 4000) / 10000;
            let locker_amount = (fee_amount_u256 * 4000) / 10000;
            let team_amount = (fee_amount_u256 * 1000) / 10000;
            let dev_amount = fee_amount_u256 - burn_amount - locker_amount - team_amount;
            
            let burn_amount_u64 = (burn_amount as u64);
            let locker_amount_u64 = (locker_amount as u64);
            let team_amount_u64 = (team_amount as u64);
            
            if (burn_amount_u64 > 0) {
                let burn_coin = coin::split(&mut fee_coin, burn_amount_u64, ctx);
                transfer::public_transfer(burn_coin, farm.burn_address);
            };
            if (locker_amount_u64 > 0) {
                let locker_coin = coin::split(&mut fee_coin, locker_amount_u64, ctx);
                transfer::public_transfer(locker_coin, farm.locker_address);
            };
            if (team_amount_u64 > 0) {
                let team_coin = coin::split(&mut fee_coin, team_amount_u64, ctx);
                transfer::public_transfer(team_coin, farm.team_address);
            };
            transfer::public_transfer(fee_coin, farm.dev_address);
            
            // Update accumulated fees
            let pool_ref = table::borrow_mut(&mut farm.pools, lp_type);
            pool_ref.accumulated_withdrawal_fees = pool_ref.accumulated_withdrawal_fees + fee_amount;
            
            event::emit(FeesCollected {
                pool_type: lp_type,
                amount: fee_amount,
                fee_type: string::utf8(b"withdrawal"),
                timestamp: current_time
            });
        };
        
        // Update vault and position
        token_vault.amount = token_vault.amount - amount;
        
        if (amount == position.amount) {
            // Complete unstake - remove position
            if (table::contains(&farm.user_positions, sender)) {
                let user_table = table::borrow_mut(&mut farm.user_positions, sender);
                if (table::contains(user_table, lp_type)) {
                    let positions = table::borrow_mut(user_table, lp_type);
                    let mut i = 0;
                    let len = vector::length(positions);
                    let mut found = false;
                    
                    while (i < len && !found) {
                        if (*vector::borrow(positions, i) == position_id) {
                            vector::swap_remove(positions, i);
                            found = true;
                        } else {
                            i = i + 1;
                        };
                    };
                };
            };
            
            if (table::contains(&farm.position_to_vault, position_id)) {
                table::remove(&mut farm.position_to_vault, position_id);
            };
            
            let StakingPosition<LPCoin<T0, T1>> { 
                id, 
                owner: _, 
                pool_type: _, 
                amount: _, 
                initial_stake_timestamp: _, 
                vault_id: _ 
            } = position;
            object::delete(id);
            
            if (balance::value(&token_vault.balance) == 0) {
                token_vault.owner = @0x0;
            };
        } else {
            // Partial unstake - update position
            position.amount = position.amount - amount;
            transfer::transfer(position, sender);
        };
        
        event::emit(Unstaked {
            staker: sender,
            pool_type: lp_type,
            amount,
            timestamp: current_time
        });
    }
    
    public entry fun stake_single<T>(
        farm: &mut Farm,
        vault: &mut RewardVault,
        mut tokens: Coin<T>,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        assert!(!farm.paused, ERROR_INACTIVE_POOL);

        let amount = (coin::value(&tokens) as u256);
        assert!(amount >= MIN_STAKE_AMOUNT, ERROR_INVALID_AMOUNT);
        
        let token_type = type_name::get<T>();
        assert!(table::contains(&farm.pools, token_type), ERROR_POOL_NOT_FOUND);
        
        validate_new_stake_allowed(global_config, clock);
        
        let current_time = clock::timestamp_ms(clock) / 1000;
        farm.last_update_timestamp = current_time;
        let sender = tx_context::sender(ctx);
        
        // Extract farm fields BEFORE borrowing pool
        let total_lp_allocation_points = farm.total_lp_allocation_points;
        let total_single_allocation_points = farm.total_single_allocation_points;
        let mut farm_total_victory_distributed = farm.total_victory_distributed;

        let pool = table::borrow_mut(&mut farm.pools, token_type);
        assert!(pool.active, ERROR_INACTIVE_POOL);

        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        let (_, single_allocation) = get_allocations_safe(global_config, clock);
        
        if (single_allocation == 0 && is_initialized) {
            event::emit(EmissionWarning {
                message: string::utf8(b"Single asset rewards ended - consider LP staking"),
                pool_type: option::some(token_type),
                timestamp: current_time
            });
        };
        
        if (pool.total_staked > 0) {
            update_pool_accumulator_masterchef(
                pool, 
                total_lp_allocation_points, 
                total_single_allocation_points, 
                global_config, 
                clock, 
                current_time
            );
        };
        
        let fee_amount = (amount * pool.deposit_fee) / BASIS_POINTS;
        let stake_amount = amount - fee_amount;
        
        if (!table::contains(&pool.stakers, sender)) {
            table::add(&mut pool.stakers, sender, Staker {
                amount: 0,
                reward_debt: 0,
                rewards_claimed: 0,
                last_stake_timestamp: current_time,
                last_claim_timestamp: current_time
            });
        };
        
        let pending_rewards = if (table::contains(&pool.stakers, sender)) {
            let staker = table::borrow(&pool.stakers, sender);
            if (staker.amount > 0 && is_initialized && is_active && !is_paused) {
                calculate_pending_rewards_masterchef(staker.amount, staker.reward_debt, pool.acc_reward_per_share)
            } else { 0 }
        } else { 0 };
        
        // Handle fee distribution
        if (fee_amount > 0) {
            let mut fee_coin = coin::split(&mut tokens, (fee_amount as u64), ctx);
            let fee_amount_u256 = (coin::value(&fee_coin) as u256);
            let burn_amount = (fee_amount_u256 * 4000) / 10000;
            let locker_amount = (fee_amount_u256 * 4000) / 10000;
            let team_amount = (fee_amount_u256 * 1000) / 10000;
            let dev_amount = fee_amount_u256 - burn_amount - locker_amount - team_amount;
            
            if (burn_amount > 0) {
                let burn_coin = coin::split(&mut fee_coin, (burn_amount as u64), ctx);
                transfer::public_transfer(burn_coin, farm.burn_address);
            };
            if (locker_amount > 0) {
                let locker_coin = coin::split(&mut fee_coin, (locker_amount as u64), ctx);
                transfer::public_transfer(locker_coin, farm.locker_address);
            };
            if (team_amount > 0) {
                let team_coin = coin::split(&mut fee_coin, (team_amount as u64), ctx);
                transfer::public_transfer(team_coin, farm.team_address);
            };
            transfer::public_transfer(fee_coin, farm.dev_address);
            
            pool.accumulated_deposit_fees = pool.accumulated_deposit_fees + fee_amount_u256;
            
            event::emit(FeesCollected {
                pool_type: pool.pool_type,
                amount: fee_amount_u256,
                fee_type: string::utf8(b"deposit"),
                timestamp: current_time
            });
        };
        
        // Create vault and position
        let vault_id = object::new(ctx);
        let token_vault = StakedTokenVault<T> {
            id: vault_id,
            balance: coin::into_balance(tokens),
            owner: sender,
            pool_type: token_type,
            amount: stake_amount,
            initial_stake_timestamp: current_time
        };
        
        let position_id = object::new(ctx);
        let position = StakingPosition<T> {
            id: position_id,
            owner: sender,
            pool_type: token_type,
            amount: stake_amount,
            initial_stake_timestamp: current_time,
            vault_id: object::uid_to_inner(&token_vault.id)
        };
        
        let position_uid_bytes = object::uid_to_inner(&position.id);
        
        // Update farm user positions
        if (!table::contains(&mut farm.user_positions, sender)) {
            table::add(&mut farm.user_positions, sender, table::new(ctx));
        };
        
        let user_table = table::borrow_mut(&mut farm.user_positions, sender);
        if (!table::contains(user_table, token_type)) {
            table::add(user_table, token_type, vector::empty<ID>());
        };
        
        let positions = table::borrow_mut(user_table, token_type);
        vector::push_back(positions, position_uid_bytes);
        table::add(&mut farm.position_to_vault, position_uid_bytes, object::uid_to_inner(&token_vault.id));
        
        transfer::share_object(token_vault);
        transfer::transfer(position, sender);
        
        // Update staker info
        let staker = table::borrow_mut(&mut pool.stakers, sender);
        staker.amount = staker.amount + stake_amount;
        staker.last_stake_timestamp = current_time;
        
        // Handle pending rewards AFTER updating pool
        if (pending_rewards > 0) {
            farm_total_victory_distributed = farm_total_victory_distributed + pending_rewards;
            
            // Now safe to call distribute_from_vault as pool operations are done
            // We need to drop the pool reference first
            pool.total_staked = pool.total_staked + stake_amount;
            staker.reward_debt = safe_mul_div_u256(staker.amount, pool.acc_reward_per_share, PRECISION_FACTOR);
            
            // Update farm total BEFORE calling distribute_from_vault
            farm.total_victory_distributed = farm_total_victory_distributed;
            
            distribute_from_vault(vault, farm, pending_rewards, sender, false, global_config, clock, ctx);
            
            // Update staker after distribution
            let pool_ref = table::borrow_mut(&mut farm.pools, token_type);
            let staker_ref = table::borrow_mut(&mut pool_ref.stakers, sender);
            staker_ref.rewards_claimed = staker_ref.rewards_claimed + pending_rewards;
            staker_ref.last_claim_timestamp = current_time;
            
            event::emit(RewardClaimed {
                staker: sender,
                pool_type: token_type,
                amount: pending_rewards,
                timestamp: current_time
            });
        } else {
            // No pending rewards case
            pool.total_staked = pool.total_staked + stake_amount;
            staker.reward_debt = safe_mul_div_u256(staker.amount, pool.acc_reward_per_share, PRECISION_FACTOR);
        };
        
        event::emit(Staked {
            staker: sender,
            pool_type: token_type,
            amount: stake_amount,
            timestamp: current_time
        });
    }

    public entry fun unstake_single<T>(
        farm: &mut Farm,
        vault: &mut RewardVault,
        mut position: StakingPosition<T>,
        token_vault: &mut StakedTokenVault<T>,
        amount: u256,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        assert!(!farm.paused, ERROR_INACTIVE_POOL);
        
        validate_unstake_allowed(global_config, clock);
        
        let token_type = type_name::get<T>();
        assert!(table::contains(&farm.pools, token_type), ERROR_POOL_NOT_FOUND);
        
        let sender = tx_context::sender(ctx);
        assert!(position.owner == sender, ERROR_NOT_OWNER);
        
        let position_id = object::uid_to_inner(&position.id);
        let vault_id = object::uid_to_inner(&token_vault.id);
        assert!(position.vault_id == vault_id, ERROR_INVALID_VAULT);
        
        assert!(token_vault.owner == sender, ERROR_NOT_OWNER);
        assert!(position.amount == token_vault.amount, ERROR_CALCULATION_OVERFLOW);
        assert!(amount > 0 && amount <= position.amount, ERROR_INVALID_AMOUNT);
        assert!(balance::value(&token_vault.balance) >= (amount as u64), ERROR_INSUFFICIENT_BALANCE);
        
        let current_time = clock::timestamp_ms(clock) / 1000;
        farm.last_update_timestamp = current_time;
        
        // Extract farm values before pool operations
        let total_lp_allocation_points = farm.total_lp_allocation_points;
        let total_single_allocation_points = farm.total_single_allocation_points;
        let mut farm_total_victory_distributed = farm.total_victory_distributed;
        
        let pool = table::borrow_mut(&mut farm.pools, token_type);
        assert!(pool.active, ERROR_INACTIVE_POOL);
        
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        
        if (pool.total_staked > 0) {
            update_pool_accumulator_masterchef(
                pool, 
                total_lp_allocation_points, 
                total_single_allocation_points, 
                global_config, 
                clock, 
                current_time
            );
        };
        
        let mut pending_rewards = 0;
        
        if (table::contains(&pool.stakers, sender)) {
            let staker = table::borrow(&pool.stakers, sender);
            let min_unstake_time = staker.last_stake_timestamp + MIN_FARM_STAKE_DURATION;
            assert!(
                current_time >= min_unstake_time,
                ERROR_MIN_STAKE_DURATION
            );
            if (staker.amount > 0 && is_initialized && is_active && !is_paused) {
                pending_rewards = calculate_pending_rewards_masterchef(staker.amount, staker.reward_debt, pool.acc_reward_per_share);
            } else if (staker.amount > 0) {
                let warning_msg = if (!is_initialized) {
                    string::utf8(b"Unstaking allowed but no rewards - emissions not started")
                } else if (!is_active) {
                    string::utf8(b"Unstaking allowed but no rewards - emissions ended")  
                } else {
                    string::utf8(b"Unstaking allowed but no rewards - emissions paused")
                };
                
                event::emit(EmissionWarning {
                    message: warning_msg,
                    pool_type: option::some(token_type),
                    timestamp: current_time
                });
            };
        };
        
        let fee_amount = (amount * pool.withdrawal_fee) / BASIS_POINTS;
        let unstake_amount = amount - fee_amount;
        
        let fee_amount_u64 = (fee_amount as u64);
        let unstake_amount_u64 = (unstake_amount as u64);
        
        // Update pool and staker state first
        if (table::contains(&pool.stakers, sender)) {
            let staker_ref = table::borrow_mut(&mut pool.stakers, sender);
            staker_ref.amount = staker_ref.amount - amount;
            staker_ref.reward_debt = safe_mul_div_u256(staker_ref.amount, pool.acc_reward_per_share, PRECISION_FACTOR);
        };
        
        pool.total_staked = pool.total_staked - amount;
        
        // Handle pending rewards after pool updates
        if (pending_rewards > 0) {
            farm_total_victory_distributed = farm_total_victory_distributed + pending_rewards;
            farm.total_victory_distributed = farm_total_victory_distributed;
            
            distribute_from_vault(vault, farm, pending_rewards, sender, false, global_config, clock, ctx);
            
            // Update staker timestamp after distribution
            let pool_ref = table::borrow_mut(&mut farm.pools, token_type);
            if (table::contains(&pool_ref.stakers, sender)) {
                let staker_ref = table::borrow_mut(&mut pool_ref.stakers, sender);
                staker_ref.rewards_claimed = staker_ref.rewards_claimed + pending_rewards;
                staker_ref.last_claim_timestamp = current_time;
            };
            
            event::emit(RewardClaimed {
                staker: sender,
                pool_type: token_type,
                amount: pending_rewards,
                timestamp: current_time
            });
        };
        
        // Handle token transfers
        if (unstake_amount_u64 > 0) {
            let unstake_coin = coin::from_balance(
                balance::split(&mut token_vault.balance, unstake_amount_u64),
                ctx
            );
            transfer::public_transfer(unstake_coin, sender);
        };
        
        if (fee_amount_u64 > 0) {
            let fee_balance = balance::split(&mut token_vault.balance, fee_amount_u64);
            let mut fee_coin = coin::from_balance(fee_balance, ctx);
            let fee_amount_u256 = (coin::value(&fee_coin) as u256);
            
            let burn_amount = (fee_amount_u256 * 4000) / 10000;
            let locker_amount = (fee_amount_u256 * 4000) / 10000;
            let team_amount = (fee_amount_u256 * 1000) / 10000;
            let dev_amount = fee_amount_u256 - burn_amount - locker_amount - team_amount;
            
            let burn_amount_u64 = (burn_amount as u64);
            let locker_amount_u64 = (locker_amount as u64);
            let team_amount_u64 = (team_amount as u64);
            
            if (burn_amount_u64 > 0) {
                let burn_coin = coin::split(&mut fee_coin, burn_amount_u64, ctx);
                transfer::public_transfer(burn_coin, farm.burn_address);
            };
            if (locker_amount_u64 > 0) {
                let locker_coin = coin::split(&mut fee_coin, locker_amount_u64, ctx);
                transfer::public_transfer(locker_coin, farm.locker_address);
            };
            if (team_amount_u64 > 0) {
                let team_coin = coin::split(&mut fee_coin, team_amount_u64, ctx);
                transfer::public_transfer(team_coin, farm.team_address);
            };
            transfer::public_transfer(fee_coin, farm.dev_address);
            
            // Update accumulated fees
            let pool_ref = table::borrow_mut(&mut farm.pools, token_type);
            pool_ref.accumulated_withdrawal_fees = pool_ref.accumulated_withdrawal_fees + fee_amount;
            
            event::emit(FeesCollected {
                pool_type: token_type,
                amount: fee_amount,
                fee_type: string::utf8(b"withdrawal"),
                timestamp: current_time
            });
        };
        
        // Update vault and position
        token_vault.amount = token_vault.amount - amount;
        
        if (amount == position.amount) {
            // Remove position tracking
            if (table::contains(&farm.user_positions, sender)) {
                let user_table = table::borrow_mut(&mut farm.user_positions, sender);
                if (table::contains(user_table, token_type)) {
                    let positions = table::borrow_mut(user_table, token_type);
                    let mut i = 0;
                    let len = vector::length(positions);
                    let mut found = false;
                    
                    while (i < len && !found) {
                        if (*vector::borrow(positions, i) == position_id) {
                            vector::swap_remove(positions, i);
                            found = true;
                        } else {
                            i = i + 1;
                        };
                    };
                };
            };
            
            if (table::contains(&farm.position_to_vault, position_id)) {
                table::remove(&mut farm.position_to_vault, position_id);
            };
            
            let StakingPosition<T> { 
                id, 
                owner: _, 
                pool_type: _, 
                amount: _, 
                initial_stake_timestamp: _, 
                vault_id: _ 
            } = position;
            object::delete(id);
            
            if (balance::value(&token_vault.balance) == 0) {
                token_vault.owner = @0x0;
            };
        } else {
            position.amount = position.amount - amount;
            transfer::transfer(position, sender);
        };
        
        event::emit(Unstaked {
            staker: sender,
            pool_type: token_type,
            amount,
            timestamp: current_time
        });
    }

    public entry fun claim_rewards_lp<T0, T1>(
        farm: &mut Farm,
        vault: &mut RewardVault,
        position: &StakingPosition<LPCoin<T0, T1>>,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        assert!(!farm.paused, ERROR_INACTIVE_POOL);
        
        validate_claim_allowed(global_config, clock); // Only checks initialized + not paused
        
        let lp_type = type_name::get<LPCoin<T0, T1>>();
        assert!(table::contains(&farm.pools, lp_type), ERROR_POOL_NOT_FOUND);
        
        let sender = tx_context::sender(ctx);
        assert!(position.owner == sender, ERROR_NOT_OWNER);
        
        let current_time = clock::timestamp_ms(clock) / 1000;
        farm.last_update_timestamp = current_time;
        
        let position_id = object::uid_to_inner(&position.id);
        assert!(table::contains(&farm.position_to_vault, position_id), ERROR_POOL_NOT_FOUND);
        
        // Extract farm values BEFORE borrowing pool
        let total_lp_allocation_points = farm.total_lp_allocation_points;
        let total_single_allocation_points = farm.total_single_allocation_points;
        let mut farm_total_victory_distributed = farm.total_victory_distributed;
        
        let pool = table::borrow_mut(&mut farm.pools, lp_type);
        assert!(pool.active, ERROR_INACTIVE_POOL);
        
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        if (pool.total_staked > 0) {
            update_pool_accumulator_masterchef(
                pool, 
                total_lp_allocation_points, 
                total_single_allocation_points, 
                global_config, 
                clock, 
                current_time
            );
        };
        
        assert!(table::contains(&pool.stakers, sender), ERROR_NOT_OWNER);
        let staker = table::borrow(&pool.stakers, sender);
        assert!(
            current_time >= staker.last_claim_timestamp + MIN_CLAIM_INTERVAL,
            ERROR_CLAIM_TOO_FREQUENT
        );
        assert!(staker.amount > 0, ERROR_INSUFFICIENT_BALANCE);
        
        // Calculate pending rewards with current pool state
        let pending_rewards = calculate_pending_rewards_masterchef(staker.amount, staker.reward_debt, pool.acc_reward_per_share);
        assert!(pending_rewards > 0, ERROR_NO_REWARDS);
        
        // Update staker reward debt BEFORE distribution
        let staker_ref = table::borrow_mut(&mut pool.stakers, sender);
        staker_ref.reward_debt = safe_mul_div_u256(staker_ref.amount, pool.acc_reward_per_share, PRECISION_FACTOR);
        
        // Update farm total BEFORE calling distribute_from_vault
        farm_total_victory_distributed = farm_total_victory_distributed + pending_rewards;
        farm.total_victory_distributed = farm_total_victory_distributed;
        
        // NOW safe to call distribute_from_vault (pool operations complete)
        distribute_from_vault(vault, farm, pending_rewards, sender, true, global_config, clock, ctx);
        
        // Re-borrow pool for final staker updates
        let pool_ref = table::borrow_mut(&mut farm.pools, lp_type);
        let staker_final = table::borrow_mut(&mut pool_ref.stakers, sender);
        staker_final.rewards_claimed = staker_final.rewards_claimed + pending_rewards;
        staker_final.last_claim_timestamp = current_time;
        
        event::emit(RewardClaimed {
            staker: sender,
            pool_type: lp_type,
            amount: pending_rewards,
            timestamp: current_time
        });
    }

    public entry fun claim_rewards_single<T>(
        farm: &mut Farm,
        vault: &mut RewardVault,
        position: &StakingPosition<T>,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        assert!(!farm.paused, ERROR_INACTIVE_POOL);

        validate_claim_allowed(global_config, clock); // Only checks initialized + not paused
        
        let (_, single_allocation) = get_allocations_safe(global_config, clock);
        assert!(single_allocation > 0, ERROR_SINGLE_REWARDS_ENDED);
        
        let token_type = type_name::get<T>();
        assert!(table::contains(&farm.pools, token_type), ERROR_POOL_NOT_FOUND);
        
        let sender = tx_context::sender(ctx);
        assert!(position.owner == sender, ERROR_NOT_OWNER);
        
        let current_time = clock::timestamp_ms(clock) / 1000;
        farm.last_update_timestamp = current_time;
        
        let position_id = object::uid_to_inner(&position.id);
        assert!(table::contains(&farm.position_to_vault, position_id), ERROR_POOL_NOT_FOUND);
        
        // Extract farm values BEFORE borrowing pool
        let total_lp_allocation_points = farm.total_lp_allocation_points;
        let total_single_allocation_points = farm.total_single_allocation_points;
        let mut farm_total_victory_distributed = farm.total_victory_distributed;

        let pool = table::borrow_mut(&mut farm.pools, token_type);
        assert!(pool.active, ERROR_INACTIVE_POOL);

        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);

        if (pool.total_staked > 0) {
            update_pool_accumulator_masterchef(
                pool, 
                total_lp_allocation_points, 
                total_single_allocation_points, 
                global_config, 
                clock, 
                current_time
            );
        };
        
        assert!(table::contains(&pool.stakers, sender), ERROR_NOT_OWNER);
        let staker = table::borrow(&pool.stakers, sender);
        assert!(
            current_time >= staker.last_claim_timestamp + MIN_CLAIM_INTERVAL,
            ERROR_CLAIM_TOO_FREQUENT
        );
        assert!(staker.amount > 0, ERROR_INSUFFICIENT_BALANCE);
        
        // Calculate pending rewards with current pool state
        let pending_rewards = calculate_pending_rewards_masterchef(staker.amount, staker.reward_debt, pool.acc_reward_per_share);
        assert!(pending_rewards > 0, ERROR_NO_REWARDS);
        
        // Update staker reward debt BEFORE distribution
        let staker_ref = table::borrow_mut(&mut pool.stakers, sender);
        staker_ref.reward_debt = safe_mul_div_u256(staker_ref.amount, pool.acc_reward_per_share, PRECISION_FACTOR);
        
        // Update farm total BEFORE calling distribute_from_vault
        farm_total_victory_distributed = farm_total_victory_distributed + pending_rewards;
        farm.total_victory_distributed = farm_total_victory_distributed;
        
        // NOW safe to call distribute_from_vault (pool operations complete)
        distribute_from_vault(vault, farm, pending_rewards, sender, false, global_config, clock, ctx);
        
        // Re-borrow pool for final staker updates
        let pool_ref = table::borrow_mut(&mut farm.pools, token_type);
        let staker_final = table::borrow_mut(&mut pool_ref.stakers, sender);
        staker_final.rewards_claimed = staker_final.rewards_claimed + pending_rewards;
        staker_final.last_claim_timestamp = current_time;
        
        event::emit(RewardClaimed {
            staker: sender,
            pool_type: token_type,
            amount: pending_rewards,
            timestamp: current_time
        });
    }

    // === View Functions ===
    
    public fun get_pending_rewards<T>(
        farm: &Farm, 
        staker_address: address,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
    ): u256 {
        let pool_type = type_name::get<T>();
        
        if (!table::contains(&farm.pools, pool_type)) {
            return 0
        };
        
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        if (!is_initialized || !is_active || is_paused) {
            return 0
        };
        
        let pool = table::borrow(&farm.pools, pool_type);
        
        if (!table::contains(&pool.stakers, staker_address)) {
            return 0
        };
        
        let staker = table::borrow(&pool.stakers, staker_address);
        
        if (staker.amount == 0 || pool.total_staked == 0) {
            return 0
        };
        
        let current_time = clock::timestamp_ms(clock) / 1000;
        let start_time = pool.last_reward_time;
        
        let additional_rewards = calculate_farm_rewards_multi_week(
            pool.is_lp_token,
            pool.allocation_points,
            farm.total_lp_allocation_points,
            farm.total_single_allocation_points,
            global_config,
            start_time,
            current_time
        );
        
        let new_reward_per_share_delta = if (additional_rewards > 0) {
            safe_mul_div_u256(additional_rewards, PRECISION_FACTOR, pool.total_staked)
        } else { 0 };
        
        let projected_acc_reward_per_share = pool.acc_reward_per_share + new_reward_per_share_delta;
        
        calculate_pending_rewards_masterchef(
            staker.amount,
            staker.reward_debt,
            projected_acc_reward_per_share
        )
    }
    
    // === EMISSION-RELATED VIEW FUNCTIONS ===
    
    /// Get emission status for farm
    public fun get_emission_status_for_farm(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): (bool, bool, bool, u64, u8) {
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        let (current_week, phase, _, _, _) = global_emission_controller::get_emission_status(global_config, clock);
        
        (is_initialized, is_active, is_paused, current_week, phase)
    }
    
    /// Check if single assets can earn rewards
    public fun can_stake_single_assets(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): bool {
        let (_, single_allocation) = get_allocations_safe(global_config, clock);
        single_allocation > 0
    }
    
    /// Get pool-specific reward status
    public fun get_pool_reward_status<T>(
        farm: &Farm,
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): (bool, u256, String) {
        let pool_type = type_name::get<T>();
        
        if (!table::contains(&farm.pools, pool_type)) {
            return (false, 0, string::utf8(b"Pool not found"))
        };
        
        let pool = table::borrow(&farm.pools, pool_type);
        let (lp_allocation, single_allocation) = get_allocations_safe(global_config, clock);
        
        if (pool.is_lp_token) {
            (lp_allocation > 0, lp_allocation, 
             if (lp_allocation > 0) string::utf8(b"Active") else string::utf8(b"Ended"))
        } else {
            (single_allocation > 0, single_allocation,
             if (single_allocation > 0) string::utf8(b"Active") else string::utf8(b"Single rewards ended"))
        }
    }
    
    /// Get current allocations with status
    public fun get_current_allocations(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): (u256, u256, bool, u64) {
        let (lp_allocation, single_allocation) = get_allocations_safe(global_config, clock);
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        let active_allocations = is_initialized && is_active && !is_paused;
        let (current_week, _, _, _, _) = global_emission_controller::get_emission_status(global_config, clock);
        
        (lp_allocation, single_allocation, active_allocations, current_week)
    }
    
    // === EXISTING VIEW FUNCTIONS (unchanged) ===
    
    public fun get_vault_balance(vault: &RewardVault): u256 {
        balance::value(&vault.victory_balance) as u256
    }
    
    public fun get_pool_info<T>(farm: &Farm): (u256, u256, u256, bool, bool, bool) {
        let pool_type = type_name::get<T>();
        
        if (!table::contains(&farm.pools, pool_type)) {
            return (0, 0, 0, false, false, false)
        };
        
        let pool = table::borrow(&farm.pools, pool_type);
        
        (
            pool.total_staked,
            pool.deposit_fee,
            pool.withdrawal_fee,
            pool.active,
            pool.is_native_pair,
            pool.is_lp_token
        )
    }
    
    public fun get_staker_info<T>(farm: &Farm, staker_address: address): (u256, u256, u64, u64) {
        let pool_type = type_name::get<T>();
        
        if (!table::contains(&farm.pools, pool_type)) {
            return (0, 0, 0, 0)
        };
        
        let pool = table::borrow(&farm.pools, pool_type);
        
        if (!table::contains(&pool.stakers, staker_address)) {
            return (0, 0, 0, 0)
        };
        
        let staker = table::borrow(&pool.stakers, staker_address);
        
        (
            staker.amount,
            staker.rewards_claimed,
            staker.last_stake_timestamp,
            staker.last_claim_timestamp
        )
    }
    
    public fun get_farm_info(farm: &Farm): (bool, u256, u256) {
        (
            farm.paused,
            farm.total_lp_allocation_points,
            farm.total_single_allocation_points
        )
    }
    
    public fun get_pool_list(farm: &Farm): &vector<TypeName> {
        &farm.pool_list
    }

    /// Get total Victory tokens distributed by the farm
    public fun get_total_victory_distributed(farm: &Farm): u256 {
        farm.total_victory_distributed
    }
    
    /// Enhanced farm info including total distributed
    public fun get_farm_info_detailed(farm: &Farm): (bool, u256, u256, u256) {
        (
            farm.paused,
            farm.total_lp_allocation_points,
            farm.total_single_allocation_points,
            farm.total_victory_distributed
        )
    }
    
    public fun is_lp_type_allowed<T0, T1>(farm: &Farm): bool {
        let lp_type = type_name::get<LPCoin<T0, T1>>();
        table::contains(&farm.allowed_lp_types, lp_type)
    }

    public fun get_all_user_positions(farm: &Farm, user_address: address): vector<ID> {
        let mut all_position_ids = vector::empty<ID>();
        
        if (!table::contains(&farm.user_positions, user_address)) {
            return all_position_ids
        };
        
        let user_table = table::borrow(&farm.user_positions, user_address);
        
        let mut i = 0;
        let len = vector::length(&farm.pool_list);
        
        while (i < len) {
            let token_type = *vector::borrow(&farm.pool_list, i);
            
            if (table::contains(user_table, token_type)) {
                let positions = table::borrow(user_table, token_type);
                let mut j = 0;
                let pos_len = vector::length(positions);
                
                while (j < pos_len) {
                    vector::push_back(&mut all_position_ids, *vector::borrow(positions, j));
                    j = j + 1;
                };
            };
            
            i = i + 1;
        };
        
        all_position_ids
    }

    public fun get_vault_id_for_position(farm: &Farm, position_id: ID): ID {
        *table::borrow(&farm.position_to_vault, position_id)
    }

    public fun get_user_token_positions(farm: &Farm, user_address: address, token_type: TypeName): vector<ID> {
        if (!table::contains(&farm.user_positions, user_address)) {
            return vector::empty<ID>()
        };
        
        let user_table = table::borrow(&farm.user_positions, user_address);
        
        if (!table::contains(user_table, token_type)) {
            return vector::empty<ID>()
        };
        
        *table::borrow(user_table, token_type)
    }

    public fun get_user_position_summaries(farm: &Farm, user_address: address): vector<PositionSummary> {
        let mut summaries = vector::empty<PositionSummary>();
        
        if (!table::contains(&farm.user_positions, user_address)) {
            return summaries
        };
        
        let user_table = table::borrow(&farm.user_positions, user_address);
        
        let mut i = 0;
        let len = vector::length(&farm.pool_list);
        
        while (i < len) {
            let token_type = *vector::borrow(&farm.pool_list, i);
            
            if (table::contains(user_table, token_type)) {
                let positions = table::borrow(user_table, token_type);
                let mut j = 0;
                let pos_len = vector::length(positions);
                
                while (j < pos_len) {
                    let position_id = *vector::borrow(positions, j);
                    
                    if (table::contains(&farm.position_to_vault, position_id)) {
                        let pool = table::borrow(&farm.pools, token_type);
                        
                        if (table::contains(&pool.stakers, user_address)) {
                            let staker = table::borrow(&pool.stakers, user_address);
                            
                            let summary = PositionSummary {
                                id: position_id,
                                token_type: token_type,
                                amount: staker.amount
                            };
                            
                            vector::push_back(&mut summaries, summary);
                        };
                    };
                    
                    j = j + 1;
                };
            };
            
            i = i + 1;
        };
        
        summaries
    }

    public struct UserTokenStake has copy, drop {
        token_type: TypeName,
        total_amount: u256,
        position_count: u64,
        pending_rewards: u256
    }

    public fun get_user_token_stakes(
        farm: &Farm, 
        user_address: address, 
        global_config: &GlobalEmissionConfig,
        clock: &Clock, 
        ctx: &mut TxContext
    ): vector<UserTokenStake> {
        let mut token_stakes = vector::empty<UserTokenStake>();
        
        let mut i = 0;
        let len = vector::length(&farm.pool_list);
        
        while (i < len) {
            let token_type = *vector::borrow(&farm.pool_list, i);
            
            if (table::contains(&farm.pools, token_type)) {
                let pool = table::borrow(&farm.pools, token_type);
                
                if (table::contains(&pool.stakers, user_address)) {
                    let staker = table::borrow(&pool.stakers, user_address);
                    
                    let mut position_count = 0;
                    if (table::contains(&farm.user_positions, user_address)) {
                        let user_table = table::borrow(&farm.user_positions, user_address);
                        if (table::contains(user_table, token_type)) {
                            let positions = table::borrow(user_table, token_type);
                            position_count = vector::length(positions);
                        };
                    };
                    
                    let mut pending_rewards = 0;
                    if (staker.amount > 0) {
                        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
                        if (is_initialized && is_active && !is_paused) {
                            let current_time = clock::timestamp_ms(clock) / 1000;
                            let start_time = pool.last_reward_time;                    // CHANGED: field name
                            
                            if (current_time > start_time && pool.total_staked > 0) {
                                let additional_rewards = calculate_farm_rewards_multi_week(
                                    pool.is_lp_token,
                                    pool.allocation_points,
                                    farm.total_lp_allocation_points,
                                    farm.total_single_allocation_points,
                                    global_config,
                                    start_time,
                                    current_time
                                );
                                
                                if (additional_rewards > 0) {
                                    let reward_per_share_delta = safe_mul_div_u256(   // CHANGED: MasterChef calculation
                                        additional_rewards,
                                        PRECISION_FACTOR,
                                        pool.total_staked
                                    );
                                    
                                    let current_acc_reward_per_share = pool.acc_reward_per_share + reward_per_share_delta; // CHANGED: field name
                                    
                                    // CHANGED: MasterChef pending calculation
                                    pending_rewards = calculate_pending_rewards_masterchef(
                                        staker.amount,
                                        staker.reward_debt,
                                        current_acc_reward_per_share
                                    );
                                };
                            };
                        };
                    };
                    
                    let token_stake = UserTokenStake {
                        token_type: token_type,
                        total_amount: staker.amount,
                        position_count: position_count,
                        pending_rewards: pending_rewards
                    };
                    
                    vector::push_back(&mut token_stakes, token_stake);
                };
            };
            
            i = i + 1;
        };
        
        token_stakes
    }

    /// Safe multiplication and division using u256
    fun safe_mul_div_u256(a: u256, b: u256, c: u256): u256 {
        if (c == 0) return 0;
        if (a == 0 || b == 0) return 0;
        (a * b) / c
    }

    /// Calculate emission week from timestamp
    fun calculate_emission_week_from_timestamp(config: &GlobalEmissionConfig, timestamp: u64): u64 {
        let (emission_start, _) = global_emission_controller::get_config_info(config);
        
        if (timestamp < emission_start || emission_start == 0) {
            return 0
        };
        
        let elapsed_seconds = timestamp - emission_start;
        let weeks_elapsed = elapsed_seconds / (SECONDS_PER_DAY * 7);
        let week_number = weeks_elapsed + 1;
        
        if (week_number > 156) 156 else week_number
    }

    /// Calculate emission week start timestamp
    fun calculate_emission_week_start(config: &GlobalEmissionConfig, week: u64): u64 {
        let (emission_start, _) = global_emission_controller::get_config_info(config);
        
        if (week <= 1) {
            emission_start
        } else if (week > 156) {
            emission_start + (156 * SECONDS_PER_DAY * 7)
        } else {
            emission_start + ((week - 1) * SECONDS_PER_DAY * 7)
        }
    }
    
    /// Calculate farm rewards across multiple weeks
    fun calculate_farm_rewards_multi_week(
        is_lp_token: bool,
        allocation_points: u256,
        total_lp_allocation_points: u256,
        total_single_allocation_points: u256,
        global_config: &GlobalEmissionConfig,
        start_time: u64,
        end_time: u64
    ): u256 {
        if (start_time >= end_time) return 0;
        
        let (emission_start_timestamp, is_paused) = global_emission_controller::get_config_info(global_config);
        if (emission_start_timestamp == 0 || is_paused) return 0;
        
        // ADD: Strict emission end capping using controller
        let emission_end = get_emission_end_timestamp(global_config);
        let capped_end_time = if (end_time > emission_end) emission_end else end_time;
        
        // ADD: If start is after emission end, no rewards
        if (start_time >= emission_end) return 0;
        
        let first_week = calculate_emission_week_from_timestamp_farm(global_config, start_time);
        let last_week = calculate_emission_week_from_timestamp_farm(global_config, capped_end_time);
        
        // ADD: Strict week bounds checking using controller
        let emission_end_week = get_emission_end_week();
        if (first_week == 0 || last_week == 0 || first_week > emission_end_week || last_week > emission_end_week) {
            return 0
        };
        
        let mut total_rewards = 0u256;
        let mut week_number = first_week;
        
        // ADD: Hard cap at emission end week (from controller)
        while (week_number <= last_week && week_number <= emission_end_week) {
            let week_start_ts = emission_start_timestamp + ((week_number - 1) * SECONDS_PER_WEEK);
            let week_end_ts = week_start_ts + SECONDS_PER_WEEK;
            
            let overlap_start = if (start_time > week_start_ts) start_time else week_start_ts;
            let overlap_end = if (capped_end_time < week_end_ts) capped_end_time else week_end_ts;
            
            if (overlap_start < overlap_end) {
                let seconds_in_week = overlap_end - overlap_start;
                
                let week_rewards = calculate_week_farm_rewards(
                    is_lp_token,
                    allocation_points,
                    total_lp_allocation_points,
                    total_single_allocation_points,
                    global_config,
                    week_number,
                    seconds_in_week
                );
                
                total_rewards = total_rewards + week_rewards;
            };
            
            week_number = week_number + 1;
        };
        
        total_rewards
    }

    /// Calculate rewards for specific emission week
    fun calculate_week_farm_rewards(
        is_lp_token: bool,
        allocation_points: u256,
        total_lp_allocation_points: u256,
        total_single_allocation_points: u256,
        global_config: &GlobalEmissionConfig,
        week: u64,
        seconds_in_week: u64
    ): u256 {
        let (lp_allocation_per_sec, single_allocation_per_sec, _, _, _) = 
            global_emission_controller::preview_week_allocations(week);
        
        let (pool_allocation_per_sec, total_allocation_points) = if (is_lp_token) {
            (lp_allocation_per_sec, total_lp_allocation_points)
        } else {
            (single_allocation_per_sec, total_single_allocation_points)
        };
        
        if (pool_allocation_per_sec == 0 || total_allocation_points == 0) {
            return 0
        };
        
        let pool_share_per_sec = safe_mul_div_u256(
            pool_allocation_per_sec,
            allocation_points,
            total_allocation_points
        );
        
        pool_share_per_sec * (seconds_in_week as u256)
    }

    /// Calculate pending rewards with MasterChef precision
    fun calculate_pending_rewards_masterchef(
        user_amount: u256,
        user_debt: u256,
        current_acc_reward_per_share: u256
    ): u256 {
        if (user_amount == 0) return 0;
        
        let accumulated_rewards = safe_mul_div_u256(
            user_amount,
            current_acc_reward_per_share,
            PRECISION_FACTOR
        );
        
        if (accumulated_rewards <= user_debt) return 0;
        
        accumulated_rewards - user_debt
    }

    /// Update single pool accumulator
    fun update_pool_accumulator_masterchef(
        pool: &mut Pool,
        total_lp_allocation_points: u256,
        total_single_allocation_points: u256,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        current_time: u64,
    ) {
        if (pool.total_staked == 0 || current_time <= pool.last_reward_time) {
            pool.last_reward_time = current_time;
            return
        };
        
        // ADD: Overflow protection check
        if (!is_accumulator_safe(pool.acc_reward_per_share)) {
            event::emit(EmissionWarning {
                message: string::utf8(b"CRITICAL: Pool accumulator overflow risk detected"),
                pool_type: option::some(pool.pool_type),
                timestamp: current_time,
            });
            return // Stop processing to prevent overflow
        };
        
        let start_time = pool.last_reward_time;
        let end_time = current_time;
        
        let reward_amount = calculate_farm_rewards_multi_week(
            pool.is_lp_token,
            pool.allocation_points,
            total_lp_allocation_points,
            total_single_allocation_points,
            global_config,
            start_time,
            end_time
        );
        
        if (reward_amount > 0) {
            let reward_per_share_delta = safe_mul_div_u256(
                reward_amount,
                PRECISION_FACTOR,
                pool.total_staked
            );
            
            let new_accumulator_value = pool.acc_reward_per_share + reward_per_share_delta;
            
            // ADD: Pre-addition overflow check
            if (!is_accumulator_safe(new_accumulator_value)) {
                event::emit(EmissionWarning {
                    message: string::utf8(b"CRITICAL: Accumulator overflow prevented"),
                    pool_type: option::some(pool.pool_type),
                    timestamp: current_time,
                });
                return // Prevent overflow
            };
            
            pool.acc_reward_per_share = new_accumulator_value;
        };
        
        pool.last_reward_time = current_time;
    }

    // ============================================================================
    // 5. NEW UTILITY HELPER FUNCTIONS
    // ============================================================================

    /// Get emission end week from controller (don't hard-code)
    fun get_emission_end_week(): u64 {
        let (_, _, _, total_weeks) = global_emission_controller::get_emission_phase_parameters();
        total_weeks // This is 156 from controller
    }

    fun calculate_emission_week_from_timestamp_farm(global_config: &GlobalEmissionConfig, timestamp: u64): u64 {
        global_emission_controller::get_canonical_week_for_timestamp(global_config, timestamp)
    }

    fun get_current_emission_week(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): u64 {
        // Use the canonical function directly instead of the helper
        global_emission_controller::get_current_emission_week(global_config, clock)
    }

    /// Check if emission has ended (use controller logic)
    fun is_emission_ended(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): bool {
        // ✅ Use controller's built-in logic instead of hard-coding week 156
        !global_emission_controller::is_emissions_active(global_config, clock)
    }

    /// Get emission end timestamp from controller
    fun get_emission_end_timestamp(global_config: &GlobalEmissionConfig): u64 {
        let (emission_start, _) = global_emission_controller::get_config_info(global_config);
        let total_weeks = get_emission_end_week(); // Get from controller
        emission_start + (total_weeks * SECONDS_PER_WEEK)
    }

    /// Safe accumulator bounds checking
    fun is_accumulator_safe(accumulator_value: u256): bool {
        accumulator_value <= MAX_SAFE_ACCUMULATOR
    }

    // ============================================================================
    // 6. NEW TRACKING HELPER FUNCTIONS
    // ============================================================================

    /// Track Victory distribution (helper for distribute functions)
    fun track_victory_distribution(
        farm: &mut Farm,
        amount: u256,
        is_lp_victory_reward: bool,
        clock: &Clock
    ) {
        if (is_lp_victory_reward) {
            farm.total_lp_victory_distributed = farm.total_lp_victory_distributed + amount;
        } else {
            farm.total_single_victory_distributed = farm.total_single_victory_distributed + amount;
        };
        
        let total_victory_distributed = farm.total_lp_victory_distributed + farm.total_single_victory_distributed;
        
        event::emit(FarmVictoryDistributionTracking {
            lp_victory_distributed: farm.total_lp_victory_distributed,
            single_victory_distributed: farm.total_single_victory_distributed,
            total_victory_distributed,
            timestamp: clock::timestamp_ms(clock) / 1000,
        });
    }

    /// Validate new stake (strict - block after emission ends)
    fun validate_new_stake_allowed(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ) {
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        
        assert!(is_initialized, ERROR_EMISSIONS_NOT_INITIALIZED);
        assert!(is_active, ERROR_EMISSIONS_ENDED);  // ❌ STRICT: Block new stakes after emission ends
        assert!(!is_paused, ERROR_EMISSIONS_PAUSED);
    }

    // ADD this handler (similar to validate_claim_allowed)
    fun validate_unstake_allowed(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ) {
        let (is_initialized, _, is_paused) = validate_emission_state(global_config, clock);
        
        assert!(is_initialized, ERROR_EMISSIONS_NOT_INITIALIZED);
        assert!(!is_paused, ERROR_EMISSIONS_PAUSED);
        // Note: No is_active check - allow unstaking after emission ends
    }

    /// Validate claim/unstake (permissive - allow after emission ends)
    fun validate_claim_allowed(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ) {
        let (is_initialized, _, is_paused) = validate_emission_state(global_config, clock);
        
        assert!(is_initialized, ERROR_EMISSIONS_NOT_INITIALIZED);
        assert!(!is_paused, ERROR_EMISSIONS_PAUSED);
        // ✅ Note: No is_active check - allow claims after emission ends
    }

    // ============================================================================
    // 7. NEW ADMIN FUNCTIONS
    // ============================================================================

    /// Initialize farm tracking after deployment
    public entry fun initialize_farm_tracking(
        farm: &mut Farm,
        global_config: &GlobalEmissionConfig,
        _admin: &AdminCap,
        clock: &Clock
    ) {
        let current_time = clock::timestamp_ms(clock) / 1000;
        let (emission_start, _) = global_emission_controller::get_config_info(global_config);
        
        // Initialize Victory distribution tracking fields
        farm.emission_start_timestamp = emission_start;
        farm.emission_end_timestamp = get_emission_end_timestamp(global_config); // Use controller
        farm.total_lp_victory_distributed = 0;
        farm.total_single_victory_distributed = 0;
        
        event::emit(FarmVictoryDistributionTracking {
            lp_victory_distributed: 0,
            single_victory_distributed: 0,
            total_victory_distributed: 0,
            timestamp: current_time,
        });
    }

    // ============================================================================
    // 8. NEW VIEW FUNCTIONS
    // ============================================================================

    /// Get farm Victory distribution statistics
    public fun get_farm_victory_distribution_stats(farm: &Farm): (u256, u256, u256) {
        let total_victory_distributed = farm.total_lp_victory_distributed + farm.total_single_victory_distributed;
        (
            farm.total_lp_victory_distributed,
            farm.total_single_victory_distributed,
            total_victory_distributed
        )
    }

    /// Get farm emission information
    public fun get_farm_emission_info(
        farm: &Farm,
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): (bool, bool, bool, u64, u64, u64, u64) {
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        let current_week = get_current_emission_week(global_config, clock);
        
        (
            is_initialized,
            is_active,
            is_paused,
            current_week,
            farm.emission_start_timestamp,
            farm.emission_end_timestamp,
            clock::timestamp_ms(clock) / 1000
        )
    }

    /// Check if new stakes are allowed (use controller logic)
    public fun can_stake_new_positions(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): (bool, String) {
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        
        if (!is_initialized) {
            return (false, string::utf8(b"Emissions not initialized"))
        };
        
        if (!is_active) {
            return (false, string::utf8(b"Emissions ended")) // Controller handles week 156
        };
        
        if (is_paused) {
            return (false, string::utf8(b"Emissions paused"))
        };
        
        (true, string::utf8(b"Staking allowed"))
    }

    /// Check if claims are allowed
    public fun can_claim_rewards(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): (bool, String) {
        let (is_initialized, _, is_paused) = validate_emission_state(global_config, clock);
        
        if (!is_initialized) {
            return (false, string::utf8(b"Emissions not initialized"))
        };
        
        if (is_paused) {
            return (false, string::utf8(b"Emissions paused"))
        };
        
        // Claims allowed even after emissions end
        (true, string::utf8(b"Claims allowed"))
    }

    /// Get current emission allocations (use controller logic)
    public fun get_current_allocations_with_cap(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): (u256, u256, bool, u64) {
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        let current_week = get_current_emission_week(global_config, clock);
        
        if (!is_initialized || !is_active || is_paused) {
            return (0, 0, false, current_week) // Controller handles week 156 in is_active
        };
        
        let (lp_allocation, single_allocation) = global_emission_controller::get_farm_allocations(global_config, clock);
        
        (lp_allocation, single_allocation, true, current_week)
    }

    /// Get pool accumulator safety status
    public fun get_pool_accumulator_safety<T>(farm: &Farm): (u256, bool, String) {
        let pool_type = type_name::get<T>();
        
        if (!table::contains(&farm.pools, pool_type)) {
            return (0, false, string::utf8(b"Pool not found"))
        };
        
        let pool = table::borrow(&farm.pools, pool_type);
        let is_safe = is_accumulator_safe(pool.acc_reward_per_share);
        
        let status_msg = if (is_safe) {
            string::utf8(b"Accumulator safe")
        } else {
            string::utf8(b"Accumulator near overflow")
        };
        
        (pool.acc_reward_per_share, is_safe, status_msg)
    }

    // 🆕 NEW: Add view function to check when user can unstake
    public fun get_min_unstake_time<T>(
        farm: &Farm,
        user: address
    ): u64 {
        let pool_type = type_name::get<T>();
        
        if (!table::contains(&farm.pools, pool_type)) {
            return 0
        };
        
        let pool = table::borrow(&farm.pools, pool_type);
        
        if (!table::contains(&pool.stakers, user)) {
            return 0
        };
        
        let staker = table::borrow(&pool.stakers, user);
        staker.last_stake_timestamp + MIN_FARM_STAKE_DURATION  // ✅ Calculate from existing field
    }

    // 🆕 NEW: Add view function to check if user can unstake now
    public fun can_unstake_now<T>(
        farm: &Farm,
        user: address,
        clock: &Clock
    ): bool {
        let pool_type = type_name::get<T>();
        
        if (!table::contains(&farm.pools, pool_type)) {
            return true
        };
        
        let pool = table::borrow(&farm.pools, pool_type);
        
        if (!table::contains(&pool.stakers, user)) {
            return true
        };
        
        let staker = table::borrow(&pool.stakers, user);
        let current_time = clock::timestamp_ms(clock) / 1000;
        let min_unstake_time = staker.last_stake_timestamp + MIN_FARM_STAKE_DURATION;
        
        current_time >= min_unstake_time
    }

    public fun get_token_type<T>(): TypeName {
        type_name::get<T>()
    }

    public fun get_pool_accumulator_info<T>(farm: &Farm): (u256, u64) {
        let pool_type = type_name::get<T>();
        if (!table::contains(&farm.pools, pool_type)) {
            return (0, 0)
        };
        let pool = table::borrow(&farm.pools, pool_type);
        (pool.acc_reward_per_share, pool.last_reward_time)
    }

    public fun get_pool_internal_state<T>(farm: &Farm): (u256, u256, u64, bool) {
        let pool_type = type_name::get<T>();
        if (!table::contains(&farm.pools, pool_type)) {
            return (0, 0, 0, false)
        };
        let pool = table::borrow(&farm.pools, pool_type);
        (pool.total_staked, pool.acc_reward_per_share, pool.last_reward_time, pool.active)
    }
    
    #[test_only]
    public fun init_for_testing(ctx: &mut TxContext) {
        init(FARM {}, ctx)
    }
}