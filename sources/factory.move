#[allow(unused_variable)]
module suitrump_dex::factory {
    use sui::object::{Self, UID};
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use sui::event;
    use sui::table::{Self, Table};
    use std::string::String;
    use std::type_name::{Self, TypeName};
    use std::option::{Self, Option};
    use suitrump_dex::pair::{Self, Pair, AdminCap};
    use std::vector;
    use std::ascii;
    use suitrump_dex::fixed_point_math::{Self, FixedPoint};

    // Error codes
    const ERROR_IDENTICAL_TOKENS: u64 = 1;
    const ERROR_PAIR_EXISTS: u64 = 2;
    const ERROR_ZERO_ADDRESS: u64 = 3;
    const ERROR_NOT_ADMIN: u64 = 4;
    const ERROR_INVALID_FEE_SETTING: u64 = 5;
    const ERROR_CALCULATION_OVERFLOW: u64 = 6;
    const ERROR_PAIR_NOT_SORTED: u64 = 7;
    const ERROR_PROTOCOL_PAUSED: u64 = 8;
    const ERROR_NOT_PAUSE_ADMIN: u64 = 9;
    const ERROR_ALREADY_PAUSED: u64 = 10;
    const ERROR_NOT_PAUSED: u64 = 11;

    // Fee constraints
    const PRECISION: u256 = 10000; // Fee precision (basis points)

    public struct TokenPair has store, copy, drop {
        token0: TypeName,
        token1: TypeName
    }

    // Add pause events
    public struct ProtocolPaused has copy, drop {
        admin: address,
        timestamp: u64
    }

    public struct ProtocolUnpaused has copy, drop {
        admin: address, 
        timestamp: u64
    }

    public struct Factory has key {
        id: UID,
        admin: address,
        pairs: Table<TokenPair, address>,
        all_pairs: vector<address>,
        team_1_address: address,     // 40% of team fee
        team_2_address: address,     // 50% of team fee
        dev_address: address,        // 10% of team fee
        locker_address: address,
        buyback_address: address,
        is_paused: bool,
        pause_admin: address,
    }

    public struct PairCreated has copy, drop {
        token0: TypeName,
        token1: TypeName,
        pair: address,
        pair_len: u64
    }

    public struct AdminTransferred has copy, drop {
        old_admin: address,
        new_admin: address,
    }

    public struct PauseAdminTransferred has copy, drop {
        old_pause_admin: address,
        new_pause_admin: address,
    }

    fun init(ctx: &mut TxContext) {
        let sender = tx_context::sender(ctx);
        let factory = Factory {
            id: object::new(ctx),
            admin: sender,
            pairs: table::new(ctx),
            all_pairs: vector::empty(),
            team_1_address: @0x5cf81060260cd6285918d637463433758a89b23268f7da43fc08e3175041acf4,
            team_2_address: @0x11d00b1f0594da0aedc3dab291e619cea33e5cfcd3554738bfc1dd0375b65b56,
            dev_address: @0xc17889dee9255f80462972cd1218165c3a16e37d5242aa4c2070af4f46cebb01,
            locker_address: sender,
            buyback_address: sender,
            is_paused: false,
            pause_admin: sender,
        };
        transfer::share_object(factory);
    }

    public fun sort_tokens<T0, T1>(): TokenPair {
        let token0 = type_name::get<T0>();
        let token1 = type_name::get<T1>();
        assert!(token0 != token1, ERROR_IDENTICAL_TOKENS);
        
        if (compare_type_names_robust(&token0, &token1)) {
            TokenPair { token0, token1 }
        } else {
            abort ERROR_PAIR_NOT_SORTED
        }
    }

    fun compare_type_names_robust(a: &TypeName, b: &TypeName): bool {
        // Method 1: Compare by address first (package address as string)
        let addr_a = type_name::get_address(a);
        let addr_b = type_name::get_address(b);
        
        let addr_a_bytes = ascii::into_bytes(addr_a);
        let addr_b_bytes = ascii::into_bytes(addr_b);
        
        if (addr_a_bytes != addr_b_bytes) {
            return compare_bytes(&addr_a_bytes, &addr_b_bytes)
        };
        
        // Method 2: If same package, compare by module
        let module_a = type_name::get_module(a);
        let module_b = type_name::get_module(b);
        
        let module_a_bytes = ascii::into_bytes(module_a);
        let module_b_bytes = ascii::into_bytes(module_b);
        
        if (module_a_bytes != module_b_bytes) {
            return compare_bytes(&module_a_bytes, &module_b_bytes)
        };
        
        // Method 3: If same package and module, compare full type string
        let full_a = type_name::into_string(*a);
        let full_b = type_name::into_string(*b);
        
        let full_a_bytes = ascii::into_bytes(full_a);
        let full_b_bytes = ascii::into_bytes(full_b);
        
        compare_bytes(&full_a_bytes, &full_b_bytes)
    }

    fun compare_bytes(a: &vector<u8>, b: &vector<u8>): bool {
        let len_a = vector::length(a);
        let len_b = vector::length(b);
        let min_len = if (len_a < len_b) len_a else len_b;
        let mut i = 0;

        while (i < min_len) {
            let byte_a = *vector::borrow(a, i);
            let byte_b = *vector::borrow(b, i);
            if (byte_a != byte_b) {
                return byte_a < byte_b
            };
            i = i + 1;
        };
        len_a < len_b
    }

    public(package) fun create_pair<T0, T1>(
        factory: &mut Factory,
        token0_name: String,
        token1_name: String,
        ctx: &mut TxContext
    ): address {
        let token0 = type_name::get<T0>();
        let token1 = type_name::get<T1>();
        assert!(token0 != token1, ERROR_IDENTICAL_TOKENS);

        let sorted_pair = sort_tokens<T0, T1>();
        assert!(!table::contains(&factory.pairs, sorted_pair), ERROR_PAIR_EXISTS);

        let pair = pair::new<T0, T1>(
            token0_name,
            token1_name,
            factory.team_1_address,
            factory.team_2_address,
            factory.dev_address,
            factory.locker_address,
            factory.buyback_address,
            ctx
        );

        let pair_addr = object::id_address(&pair);
        table::add(&mut factory.pairs, sorted_pair, pair_addr);
        vector::push_back(&mut factory.all_pairs, pair_addr);

        pair::share_pair(pair);

        event::emit(PairCreated {
            token0,
            token1,
            pair: pair_addr,
            pair_len: vector::length(&factory.all_pairs)
        });

        pair_addr
    }

    public fun all_pairs_length(factory: &Factory): u64 {
        vector::length(&factory.all_pairs)
    }

    public fun set_addresses(
        factory: &mut Factory,
        team_1: address,
        team_2: address,
        dev: address,
        locker: address,
        buyback: address,
        ctx: &TxContext
    ) {
        assert!(tx_context::sender(ctx) == factory.admin, ERROR_NOT_ADMIN);
        assert!(
            team_1 != @0x0 && team_2 != @0x0 && dev != @0x0 && 
            locker != @0x0 && buyback != @0x0, 
            ERROR_ZERO_ADDRESS
        );
        
        factory.team_1_address = team_1;
        factory.team_2_address = team_2;
        factory.dev_address = dev;
        factory.locker_address = locker;
        factory.buyback_address = buyback;
    }

    // Pause admin functions
    public entry fun emergency_pause(
        factory: &mut Factory,
        ctx: &TxContext
    ) {
        assert!(tx_context::sender(ctx) == factory.pause_admin, ERROR_NOT_PAUSE_ADMIN);
        assert!(!factory.is_paused, ERROR_ALREADY_PAUSED);
        
        factory.is_paused = true;
        
        event::emit(ProtocolPaused {
            admin: tx_context::sender(ctx),
            timestamp: 0 // Add clock if needed
        });
    }

    public entry fun unpause(
        factory: &mut Factory,
        ctx: &TxContext  
    ) {
        assert!(tx_context::sender(ctx) == factory.pause_admin, ERROR_NOT_PAUSE_ADMIN);
        assert!(factory.is_paused, ERROR_NOT_PAUSED);
        
        factory.is_paused = false;
        
        event::emit(ProtocolUnpaused {
            admin: tx_context::sender(ctx),
            timestamp: 0 // Add clock if needed
        });
    }

    public entry fun transfer_admin(
        factory: &mut Factory,
        new_admin: address,
        ctx: &TxContext
    ) {
        assert!(tx_context::sender(ctx) == factory.admin, ERROR_NOT_ADMIN);
        assert!(new_admin != @0x0, ERROR_ZERO_ADDRESS);
        
        let old_admin = factory.admin;
        factory.admin = new_admin;
        
        event::emit(AdminTransferred {
            old_admin,
            new_admin,
        });
    }

    public entry fun transfer_pause_admin(
        factory: &mut Factory,
        new_pause_admin: address,
        ctx: &TxContext
    ) {
        assert!(tx_context::sender(ctx) == factory.pause_admin, ERROR_NOT_PAUSE_ADMIN);
        assert!(new_pause_admin != @0x0, ERROR_ZERO_ADDRESS);
        
        let old_pause_admin = factory.pause_admin;
        factory.pause_admin = new_pause_admin;
        
        event::emit(PauseAdminTransferred {
            old_pause_admin,
            new_pause_admin,
        });
    }

    public entry fun update_pair_fee_addresses<T0, T1>(
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        team_1: address,
        team_2: address,
        dev: address,
        locker: address,
        buyback: address,
        ctx: &TxContext
    ) {
        assert!(tx_context::sender(ctx) == factory.admin, ERROR_NOT_ADMIN);
        pair::update_fee_addresses(pair, team_1, team_2, dev, locker, buyback);
    }

    // Pause check modifier
    public fun assert_not_paused(factory: &Factory) {
        assert!(!factory.is_paused, ERROR_PROTOCOL_PAUSED);
    }

    // Getter functions
    public fun is_paused(factory: &Factory): bool {
        factory.is_paused
    }

    public fun get_pause_admin(factory: &Factory): address {
        factory.pause_admin
    }

    public fun get_team_addresses(factory: &Factory): (address, address, address, address, address) {
        (factory.team_1_address, factory.team_2_address, factory.dev_address, factory.locker_address, factory.buyback_address)
    }

    public fun get_pair<T0, T1>(factory: &Factory): Option<address> {
        let sorted_pair = sort_tokens<T0, T1>();
        if (table::contains(&factory.pairs, sorted_pair)) {
            option::some(*table::borrow(&factory.pairs, sorted_pair))
        } else {
            option::none()
        }
    }

    public fun is_token0<T>(token_pair: &TokenPair): bool {
        type_name::get<T>() == token_pair.token0
    }

    public fun get_all_pairs(factory: &Factory): &vector<address> {
        &factory.all_pairs
    }

    #[test_only]
    public fun init_for_testing(ctx: &mut TxContext) {
        init(ctx)
    }
}