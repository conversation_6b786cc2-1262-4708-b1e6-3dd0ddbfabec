#[allow(unused_variable)]
module suitrump_dex::pair {
    use std::ascii;
    use std::string::{Self, String};
    use std::type_name::{Self, TypeName};
    use sui::object::{Self, UID, ID};
    use sui::tx_context::{Self, TxContext};
    use sui::balance::{Self, Balance, Supply};
    use sui::coin::{Self, Coin};
    use sui::transfer;
    use sui::event;
    use sui::clock::{Self, Clock};
    use suitrump_dex::fixed_point_math::{Self, FixedPoint};

    // Constants
    const MAX_LIQUIDITY: u256 = 115792089237316195423570985008687907853269984665640564039457584007913129639935 / 2; // Half of max u256
    const PRECISION: u256 = 1000000000000000000; // 1e18
    const MINIMUM_RESERVE_AFTER_SWAP: u256 = 1000; // Minimum reserve that must remain after swap
    const MINIMUM_LIQUIDITY: u256 = 1000;
    const BASIS_POINTS: u256 = 10000;
    const TOTAL_FEE: u256 = 30; // 0.3%
    const LP_FEE: u256 = 15;    // 0.15%
    const TEAM_FEE: u256 = 9;   // 0.09%
    const LOCKER_FEE: u256 = 3; // 0.03%
    const BUYBACK_FEE: u256 = 3;// 0.03%
    const PRECISION_FACTOR: u256 = 10000; // For percentage calculations


    // Error codes
    const ERR_INSUFFICIENT_LIQUIDITY_MINTED: u64 = 102;
    const ERR_INSUFFICIENT_LIQUIDITY_BURNED: u64 = 103;
    const ERR_INSUFFICIENT_OUTPUT_AMOUNT: u64 = 104;
    const ERR_INSUFFICIENT_LIQUIDITY: u64 = 105;
    const ERR_INVALID_K: u64 = 106;
    const ERR_INSUFFICIENT_INPUT_AMOUNT: u64 = 107;
    const ERR_VALUE_TOO_HIGH: u64 = 108;
    const ERR_CALCULATION_OVERFLOW: u64 = 109;
    const ERR_EXCESSIVE_PRICE_IMPACT: u64 = 110;
    const ERR_INSUFFICIENT_FEE_AMOUNT: u64 = 111;
    const ERR_INSUFFICIENT_LIQUIDITY_CONTRIBUTION: u64 = 112;
    const ERROR_INVALID_ADDRESS: u64 = 113;


    public struct Fees has copy, drop {
        team_fee: u256,
        locker_fee: u256,
        buyback_fee: u256,
        remaining_amount: FixedPoint
    }

    public struct LPCoin<phantom T0, phantom T1> has drop {}

    public struct Pair<phantom T0, phantom T1> has key {
        id: UID,
        balance0: Balance<T0>,
        balance1: Balance<T1>,
        reserve0: u256,
        reserve1: u256,
        block_timestamp_last: u64,
        price0_cumulative_last: u256,
        price1_cumulative_last: u256,
        total_supply: u256,
        team_1_address: address,    // 40%
        team_2_address: address,    // 50%
        dev_address: address,       // 10%
        locker_address: address,
        buyback_address: address,
        name: String,
        symbol: String,
        lp_supply: Supply<LPCoin<T0, T1>>
    }

    public struct AdminCap has key {
        id: UID
    }

    /// Event emitted when LP tokens are minted
    public struct LPMint<phantom T0, phantom T1> has copy, drop {
        sender: address,               // LP provider address
        lp_coin_id: ID,               // ID of the LP coin minted
        token0_type: TypeName,        // Type of token0
        token1_type: TypeName,        // Type of token1
        amount0: u256,                // Amount of token0 added
        amount1: u256,                // Amount of token1 added
        liquidity: u256,              // Amount of LP tokens minted
        total_supply: u256            // Total supply after mint
    }

    /// Event emitted when LP tokens are burned
    public struct LPBurn<phantom T0, phantom T1> has copy, drop {
        sender: address,               // LP provider address
        lp_coin_id: ID,               // ID of the LP coin burned
        token0_type: TypeName,        // Type of token0
        token1_type: TypeName,        // Type of token1
        amount0: u256,                // Amount of token0 removed
        amount1: u256,                // Amount of token1 removed
        liquidity: u256,              // Amount of LP tokens burned
        total_supply: u256            // Total supply after burn
    }

    public struct Swap<phantom T0, phantom T1> has copy, drop {
        sender: address,
        amount0_in: u256,
        amount1_in: u256,
        amount0_out: u256,
        amount1_out: u256
    }

    public struct Sync<phantom T0, phantom T1> has copy, drop {
        reserve0: u256,
        reserve1: u256
    }

    fun init(ctx: &mut TxContext) {
        transfer::transfer(AdminCap {
            id: object::new(ctx)
        }, tx_context::sender(ctx));
    }

    fun verify_k(balance0: u256, balance1: u256, new_balance0: u256, new_balance1: u256) {
        // Direct cross-product comparison (no overflow risk with u64-based coin amounts)
        let k_old = balance0 * balance1;
        let k_new = new_balance0 * new_balance1;
        assert!(k_new >= k_old, ERR_INVALID_K);
    }

    fun calculate_fees(amount: u256): Fees {
        // Calculate fees using basis points directly first, then convert to fixed point
        let total_fee = (amount * TOTAL_FEE) / BASIS_POINTS;
        let team_fee = (total_fee * TEAM_FEE) / TOTAL_FEE;
        let locker_fee = (total_fee * LOCKER_FEE) / TOTAL_FEE;
        let buyback_fee = (total_fee * BUYBACK_FEE) / TOTAL_FEE;

        // Convert remaining amount to fixed point for K calculations
        let remaining_amount = fixed_point_math::new(
            amount - team_fee - locker_fee - buyback_fee
        );

        Fees {
            team_fee,
            locker_fee,
            buyback_fee,
            remaining_amount
        }
    }

    fun transfer_fees<T0, T1>(
        pair: &mut Pair<T0, T1>,
        is_token0: bool,
        fees: Fees,
        ctx: &mut TxContext
    ) {
        if (is_token0) {
            if (fees.team_fee > 0) {
                // Calculate individual team fee shares
                let team_1_fee = (fees.team_fee * 4000) / 10000;  // 40% exact
                let team_2_fee = (fees.team_fee * 5000) / 10000;  // 50% exact  
                let dev_fee = fees.team_fee - team_1_fee - team_2_fee;
                
                // Transfer to each address using safe transfer
                if (team_1_fee > 0) {
                    safe_transfer(&mut pair.balance0, team_1_fee, pair.team_1_address, ctx);
                };
                
                if (team_2_fee > 0) {
                    safe_transfer(&mut pair.balance0, team_2_fee, pair.team_2_address, ctx);
                };
                
                if (dev_fee > 0) {
                    safe_transfer(&mut pair.balance0, dev_fee, pair.dev_address, ctx);
                };
            };
            
            // Rest of the fee transfers using safe transfer
            if (fees.locker_fee > 0) {
                safe_transfer(&mut pair.balance0, fees.locker_fee, pair.locker_address, ctx);
            };
            
            if (fees.buyback_fee > 0) {
                safe_transfer(&mut pair.balance0, fees.buyback_fee, pair.buyback_address, ctx);
            };
        } else {
            // Same logic for token1 using safe transfer
            if (fees.team_fee > 0) {
                // Calculate individual team fee shares
                let team_1_fee = (fees.team_fee * 4000) / 10000;  // 40% exact
                let team_2_fee = (fees.team_fee * 5000) / 10000;  // 50% exact  
                let dev_fee = fees.team_fee - team_1_fee - team_2_fee;
                
                // Transfer to each address using safe transfer
                if (team_1_fee > 0) {
                    safe_transfer(&mut pair.balance1, team_1_fee, pair.team_1_address, ctx);
                };
                
                if (team_2_fee > 0) {
                    safe_transfer(&mut pair.balance1, team_2_fee, pair.team_2_address, ctx);
                };
                
                if (dev_fee > 0) {
                    safe_transfer(&mut pair.balance1, dev_fee, pair.dev_address, ctx);
                };
            };
            
            // Rest of the fee transfers using safe transfer
            if (fees.locker_fee > 0) {
                safe_transfer(&mut pair.balance1, fees.locker_fee, pair.locker_address, ctx);
            };
            
            if (fees.buyback_fee > 0) {
                safe_transfer(&mut pair.balance1, fees.buyback_fee, pair.buyback_address, ctx);
            };
        }
    }

    /// IMPORTANT: Price accumulators use wrapping addition to prevent overflow.
    /// Off-chain integrators computing TWAP must handle wrap-around arithmetic:
    /// delta = (current_accumulator - previous_accumulator) % (2^256)
    /// Always compute deltas with proper modular arithmetic for long time windows.
    fun update_price_accumulators<T0, T1>(pair: &mut Pair<T0, T1>, timestamp: u64) {
        if (pair.block_timestamp_last != 0 && pair.reserve0 > 0 && pair.reserve1 > 0) {
            let time_elapsed = fixed_point_math::new((timestamp - pair.block_timestamp_last as u256));
            let price0_fp = fixed_point_math::div(
                fixed_point_math::new(pair.reserve1),
                fixed_point_math::new(pair.reserve0)
            );
            let price1_fp = fixed_point_math::div(
                fixed_point_math::new(pair.reserve0),
                fixed_point_math::new(pair.reserve1)
            );
            
            // Use add_wrapping_for_price_accumulator instead of add to prevent abort on overflow
            // Use wrapping addition for price accumulators to handle overflow
            // Off-chain consumers must compute deltas with wrap-around: (new - old) % 2^256
            pair.price0_cumulative_last = fixed_point_math::get_raw_value(
                fixed_point_math::add_wrapping_for_price_accumulator(
                    fixed_point_math::new(pair.price0_cumulative_last),
                    fixed_point_math::mul(price0_fp, time_elapsed)
                )
            );
            pair.price1_cumulative_last = fixed_point_math::get_raw_value(
                fixed_point_math::add_wrapping_for_price_accumulator(
                    fixed_point_math::new(pair.price1_cumulative_last),
                    fixed_point_math::mul(price1_fp, time_elapsed)
                )
            );
        }
    }

    fun min_amount(a: u256, b: u256): u256 {
        fixed_point_math::get_raw_value(
            fixed_point_math::min(
                fixed_point_math::new(a),
                fixed_point_math::new(b)
            )
        )
    }

    public(package) fun new<T0, T1>(
        token0_name: String,
        token1_name: String,
        team_1: address,
        team_2: address,
        dev: address,
        locker: address,
        buyback: address,
        ctx: &mut TxContext
    ): Pair<T0, T1> {
        let mut name = string::utf8(b"Suitrump V2 ");
        string::append(&mut name, token0_name);
        string::append_utf8(&mut name, b"/");
        string::append(&mut name, token1_name);

        Pair {
            id: object::new(ctx),
            balance0: balance::zero(),
            balance1: balance::zero(),
            reserve0: 0,
            reserve1: 0,
            block_timestamp_last: 0,
            price0_cumulative_last: 0,
            price1_cumulative_last: 0,
            total_supply: 0,
            team_1_address: team_1,
            team_2_address: team_2,
            dev_address: dev,
            locker_address: locker,
            buyback_address: buyback,
            name,
            symbol: string::utf8(b"SUIT-V2"),
            lp_supply: balance::create_supply<LPCoin<T0, T1>>(LPCoin<T0, T1> {})
        }
    }

    fun update<T0, T1>(
        pair: &mut Pair<T0, T1>,
        new_balance0: u256,
        new_balance1: u256,
        clock: &Clock,
        ctx: &TxContext
    ) {
        let current_time = clock::timestamp_ms(clock) / 1000;
        update_price_accumulators(pair, current_time);

        pair.reserve0 = new_balance0;
        pair.reserve1 = new_balance1;
        pair.block_timestamp_last = current_time;

        event::emit(Sync<T0, T1> {
            reserve0: new_balance0,
            reserve1: new_balance1
        });
    }

    public(package) fun mint<T0, T1>(
        pair: &mut Pair<T0, T1>,
        coin0: Coin<T0>,
        coin1: Coin<T1>,
        clock: &Clock,
        ctx: &mut TxContext
    ): Coin<LPCoin<T0, T1>> {

        let amount0_desired = (coin::value(&coin0) as u256);
        let amount1_desired = (coin::value(&coin1) as u256);

        assert!(fixed_point_math::is_safe_value(amount0_desired), ERR_VALUE_TOO_HIGH);
        assert!(fixed_point_math::is_safe_value(amount1_desired), ERR_VALUE_TOO_HIGH);

        balance::join(&mut pair.balance0, coin::into_balance(coin0));
        balance::join(&mut pair.balance1, coin::into_balance(coin1));

        let liquidity = if (pair.total_supply == 0) {
            let initial_fp = fixed_point_math::mul(
                fixed_point_math::new(amount0_desired),
                fixed_point_math::new(amount1_desired)
            );
            let initial = fixed_point_math::sqrt(initial_fp);
            let initial_value = fixed_point_math::get_raw_value(initial);
            assert!(initial_value > MINIMUM_LIQUIDITY, ERR_INSUFFICIENT_LIQUIDITY_MINTED);
            initial_value - MINIMUM_LIQUIDITY
        } else {
            // Use safe_proportion for subsequent liquidity additions
            // This ensures consistency with burn function calculations
            let liquidity0 = fixed_point_math::safe_proportion(amount0_desired, pair.total_supply, pair.reserve0);
            let liquidity1 = fixed_point_math::safe_proportion(amount1_desired, pair.total_supply, pair.reserve1);

            assert!(liquidity0 > 0, ERR_INSUFFICIENT_LIQUIDITY_CONTRIBUTION);
            assert!(liquidity1 > 0, ERR_INSUFFICIENT_LIQUIDITY_CONTRIBUTION);

            min_amount(liquidity0, liquidity1)
        };

        assert!(liquidity > 0, ERR_INSUFFICIENT_LIQUIDITY_MINTED);
        assert!(liquidity <= MAX_LIQUIDITY, ERR_VALUE_TOO_HIGH);

        if (pair.total_supply == 0) {
            // For initial liquidity: total_supply includes burned minimum liquidity
            pair.total_supply = fixed_point_math::get_raw_value(
                fixed_point_math::add(
                    fixed_point_math::new(liquidity),
                    fixed_point_math::new(MINIMUM_LIQUIDITY)
                )
            );
        } else {
            // For subsequent liquidity: add normally
            pair.total_supply = fixed_point_math::get_raw_value(
                fixed_point_math::add(
                    fixed_point_math::new(pair.total_supply),
                    fixed_point_math::new(liquidity)
                )
            );
        };

        let new_balance0 = fixed_point_math::get_raw_value(
            fixed_point_math::add(
                fixed_point_math::new(pair.reserve0),
                fixed_point_math::new(amount0_desired)
            )
        );
        let new_balance1 = fixed_point_math::get_raw_value(
            fixed_point_math::add(
                fixed_point_math::new(pair.reserve1),
                fixed_point_math::new(amount1_desired)
            )
        );

        update(pair, new_balance0, new_balance1, clock, ctx);

        // FIXED: Use safe supply increase for large liquidity amounts
        let lp_coin = safe_increase_supply(&mut pair.lp_supply, liquidity, ctx);
        let lp_coin_id = object::id(&lp_coin);

        event::emit(LPMint<T0, T1> {
            sender: tx_context::sender(ctx),
            lp_coin_id,
            token0_type: type_name::get<T0>(),
            token1_type: type_name::get<T1>(),
            amount0: amount0_desired,
            amount1: amount1_desired,
            liquidity,
            total_supply: pair.total_supply
        });

        lp_coin
    }

    public(package) fun burn<T0, T1>(
        pair: &mut Pair<T0, T1>,
        lp_token: Coin<LPCoin<T0, T1>>,
        clock: &Clock,
        ctx: &mut TxContext
    ): (Coin<T0>, Coin<T1>) {

        let liquidity = (coin::value(&lp_token) as u256);
        assert!(fixed_point_math::is_safe_value(liquidity), ERR_VALUE_TOO_HIGH);

        let lp_coin_id = object::id(&lp_token);

        let current_balance0 = (balance::value(&pair.balance0) as u256);
        let current_balance1 = (balance::value(&pair.balance1) as u256);
        
        // Use safe_proportion for liquidity calculations
        // This prevents precision loss while maintaining overflow protection
        let amount0 = fixed_point_math::safe_proportion(current_balance0, liquidity, pair.total_supply);
        let amount1 = fixed_point_math::safe_proportion(current_balance1, liquidity, pair.total_supply);

        assert!(amount0 > 0 && amount1 > 0, ERR_INSUFFICIENT_LIQUIDITY_BURNED);

        balance::decrease_supply(&mut pair.lp_supply, coin::into_balance(lp_token));
        
        pair.total_supply = pair.total_supply - liquidity;

        // Direct arithmetic for balance updates
        let new_balance0 = current_balance0 - amount0;
        let new_balance1 = current_balance1 - amount1;

        update(pair, new_balance0, new_balance1, clock, ctx);

        event::emit(LPBurn<T0, T1> {
            sender: tx_context::sender(ctx),
            lp_coin_id,
            token0_type: type_name::get<T0>(),
            token1_type: type_name::get<T1>(),
            amount0,
            amount1,
            liquidity,
            total_supply: pair.total_supply
        });

        // Use safe transfer for potentially large withdrawal amounts
        let coin0 = safe_withdraw(&mut pair.balance0, amount0, ctx);
        let coin1 = safe_withdraw(&mut pair.balance1, amount1, ctx);

        (coin0, coin1)
    }

    public(package) fun swap<T0, T1>(
        pair: &mut Pair<T0, T1>,
        mut coin0_in: Option<Coin<T0>>,
        mut coin1_in: Option<Coin<T1>>,
        amount0_out: u256,
        amount1_out: u256,
        clock: &Clock,
        ctx: &mut TxContext
    ): (Option<Coin<T0>>, Option<Coin<T1>>) {
        assert!(amount0_out > 0 || amount1_out > 0, ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        assert!(amount0_out < pair.reserve0 && amount1_out < pair.reserve1, ERR_INSUFFICIENT_LIQUIDITY);

        let initial_reserve0 = pair.reserve0;
        let initial_reserve1 = pair.reserve1;

        let mut amount0_in = if (std::option::is_some(&coin0_in)) {
            let coin = std::option::extract(&mut coin0_in);
            let amount = (coin::value(&coin) as u256);
            assert!(fixed_point_math::is_safe_value(amount), ERR_VALUE_TOO_HIGH);
            balance::join(&mut pair.balance0, coin::into_balance(coin));
            amount
        } else {
            0
        };
        std::option::destroy_none(coin0_in);

        let mut amount1_in = if (std::option::is_some(&coin1_in)) {
            let coin = std::option::extract(&mut coin1_in);
            let amount = (coin::value(&coin) as u256);
            assert!(fixed_point_math::is_safe_value(amount), ERR_VALUE_TOO_HIGH);
            balance::join(&mut pair.balance1, coin::into_balance(coin));
            amount
        } else {
            0
        };
        std::option::destroy_none(coin1_in);

        assert!(amount0_in > 0 || amount1_in > 0, ERR_INSUFFICIENT_INPUT_AMOUNT);

        let coin0_out = if (amount0_out > 0) {
            std::option::some(safe_withdraw(&mut pair.balance0, amount0_out, ctx))
        } else {
            std::option::none()
        };

        let coin1_out = if (amount1_out > 0) {
            std::option::some(safe_withdraw(&mut pair.balance1, amount1_out, ctx))
        } else {
            std::option::none()
        };

        let original_amount0_in = amount0_in;
        let original_amount1_in = amount1_in;

        if (amount0_in > 0) {
            let fees = calculate_fees(amount0_in);
            amount0_in = fixed_point_math::get_raw_value(fees.remaining_amount);
            transfer_fees(pair, true, fees, ctx);
        };

        if (amount1_in > 0) {
            let fees = calculate_fees(amount1_in);
            amount1_in = fixed_point_math::get_raw_value(fees.remaining_amount);
            transfer_fees(pair, false, fees, ctx);
        };

        let final_balance0 = (balance::value(&pair.balance0) as u256);
        let final_balance1 = (balance::value(&pair.balance1) as u256);

        verify_k(initial_reserve0, initial_reserve1, final_balance0, final_balance1);

        assert!(final_balance0 >= MINIMUM_RESERVE_AFTER_SWAP, ERR_INSUFFICIENT_LIQUIDITY);
        assert!(final_balance1 >= MINIMUM_RESERVE_AFTER_SWAP, ERR_INSUFFICIENT_LIQUIDITY);

        update(pair, final_balance0, final_balance1, clock, ctx);

        event::emit(Swap<T0, T1> {
            sender: tx_context::sender(ctx),
            amount0_in: original_amount0_in,
            amount1_in: original_amount1_in,
            amount0_out,
            amount1_out
        });

        (coin0_out, coin1_out)
    }

    public fun sync<T0, T1>(pair: &mut Pair<T0, T1>, clock: &Clock, ctx: &TxContext) {
        let new_balance0 = (balance::value(&pair.balance0) as u256);
        let new_balance1 = (balance::value(&pair.balance1) as u256);
        update(pair, new_balance0, new_balance1, clock, ctx);
    }

    // Utility functions
    public fun get_name<T0, T1>(pair: &Pair<T0, T1>): String {
        pair.name
    }

    public fun get_symbol<T0, T1>(pair: &Pair<T0, T1>): String {
        pair.symbol
    }

    public fun get_reserves<T0, T1>(pair: &Pair<T0, T1>): (u256, u256, u64) {
        (pair.reserve0, pair.reserve1, pair.block_timestamp_last)
    }

    public fun share_pair<T0, T1>(pair: Pair<T0, T1>) {
        transfer::share_object(pair)
    }

    public fun get_price_cumulative_last<T0, T1>(pair: &Pair<T0, T1>): (u256, u256) {
        (pair.price0_cumulative_last, pair.price1_cumulative_last)
    }

    public fun total_supply<T0, T1>(pair: &Pair<T0, T1>): u256 {
        pair.total_supply
    }

    public(package) fun update_fee_addresses<T0, T1>(
        pair: &mut Pair<T0, T1>,
        team_1: address,
        team_2: address,
        dev: address,
        locker: address,
        buyback: address,
    ) {
        // Add zero address validations
        assert!(team_1 != @0x0, ERROR_INVALID_ADDRESS);
        assert!(team_2 != @0x0, ERROR_INVALID_ADDRESS);
        assert!(dev != @0x0, ERROR_INVALID_ADDRESS);
        assert!(locker != @0x0, ERROR_INVALID_ADDRESS);
        assert!(buyback != @0x0, ERROR_INVALID_ADDRESS);
        
        pair.team_1_address = team_1;
        pair.team_2_address = team_2;
        pair.dev_address = dev;
        pair.locker_address = locker;
        pair.buyback_address = buyback; 
    }

    /// Helper function to safely transfer large u256 amounts by breaking them into u64 chunks
    /// This prevents arithmetic overflow when transferring amounts larger than u64::MAX
    fun safe_transfer<T>(
        balance: &mut Balance<T>, 
        total_amount: u256, 
        recipient: address, 
        ctx: &mut TxContext
    ) {
        if (total_amount == 0) return;
        
        let u64_max = 18446744073709551615u128; // u64::MAX as u128 for safe comparison
        let mut remaining_amount = (total_amount as u128);

        while (remaining_amount > 0) {
            let amount_to_transfer_u64 = if (remaining_amount > u64_max) {
                (u64_max as u64)
            } else {
                (remaining_amount as u64)
            };
            
            transfer::public_transfer(
                coin::take(balance, amount_to_transfer_u64, ctx), 
                recipient
            );
            
            remaining_amount = remaining_amount - (amount_to_transfer_u64 as u128);
        };
    }

    /// Helper function to safely create coins from supply with chunked approach
    /// Used for LP token creation when liquidity amount exceeds u64::MAX
    fun safe_increase_supply<T>(
        supply: &mut Supply<T>,
        total_amount: u256,
        ctx: &mut TxContext
    ): Coin<T> {
        if (total_amount == 0) {
            return coin::from_balance(balance::zero<T>(), ctx)
        };
        
        let u64_max = 18446744073709551615u128; // u64::MAX as u128
        
        // If amount fits in u64, use direct approach
        if (total_amount <= (u64_max as u256)) {
            return coin::from_balance(
                balance::increase_supply(supply, (total_amount as u64)), 
                ctx
            )
        };
        
        // For large amounts, create chunks and merge them
        let mut result_coin = coin::from_balance(balance::zero<T>(), ctx);
        let mut remaining_amount = (total_amount as u128);

        while (remaining_amount > 0) {
            let chunk_size = if (remaining_amount > u64_max) {
                (u64_max as u64)
            } else {
                (remaining_amount as u64)
            };
            
            let chunk_coin = coin::from_balance(
                balance::increase_supply(supply, chunk_size), 
                ctx
            );
            
            coin::join(&mut result_coin, chunk_coin);
            remaining_amount = remaining_amount - (chunk_size as u128);
        };
        
        result_coin
    }

    

    /// Helper function to safely withdraw large amounts from balance
    fun safe_withdraw<T>(
        balance: &mut Balance<T>,
        total_amount: u256,
        ctx: &mut TxContext
    ): Coin<T> {
        if (total_amount == 0) {
            return coin::from_balance(balance::zero<T>(), ctx)
        };
        
        let u64_max = 18446744073709551615u128; // u64::MAX as u128
        
        // If amount fits in u64, use direct approach
        if (total_amount <= (u64_max as u256)) {
            return coin::take(balance, (total_amount as u64), ctx)
        };
        
        // For large amounts, withdraw in chunks and merge them
        let mut result_coin = coin::from_balance(balance::zero<T>(), ctx);
        let mut remaining_amount = (total_amount as u128);

        while (remaining_amount > 0) {
            let chunk_size = if (remaining_amount > u64_max) {
                (u64_max as u64)
            } else {
                (remaining_amount as u64)
            };
            
            let chunk_coin = coin::take(balance, chunk_size, ctx);
            coin::join(&mut result_coin, chunk_coin);
            remaining_amount = remaining_amount - (chunk_size as u128);
        };
        
        result_coin
    }

    #[test_only]
    public fun init_for_testing(ctx: &mut TxContext) {
        init(ctx)
    }

    #[test_only]
    public fun mint_for_testing<T0, T1>(
        pair: &mut Pair<T0, T1>,
        coin0: Coin<T0>,
        coin1: Coin<T1>,
        ctx: &mut TxContext
    ): Coin<LPCoin<T0, T1>> {
        // Create a dummy clock for testing
        let clock = sui::clock::create_for_testing(ctx);
        let result = mint(pair, coin0, coin1, &clock, ctx);
        sui::clock::destroy_for_testing(clock);
        result
    }

    #[test_only]
    public fun burn_for_testing<T0, T1>(
        pair: &mut Pair<T0, T1>,
        lp_token: Coin<LPCoin<T0, T1>>,
        ctx: &mut TxContext
    ): (Coin<T0>, Coin<T1>) {
        let clock = sui::clock::create_for_testing(ctx);
        let (coin0, coin1) = burn(pair, lp_token, &clock, ctx);
        sui::clock::destroy_for_testing(clock);
        (coin0, coin1)
    }

    #[test_only]
    public fun swap_for_testing<T0, T1>(
        pair: &mut Pair<T0, T1>,
        coin0_in: Option<Coin<T0>>,
        coin1_in: Option<Coin<T1>>,
        amount0_out: u256,
        amount1_out: u256,
        ctx: &mut TxContext
    ): (Option<Coin<T0>>, Option<Coin<T1>>) {
        let clock = sui::clock::create_for_testing(ctx);
        let (coin0_out, coin1_out) = swap(pair, coin0_in, coin1_in, amount0_out, amount1_out, &clock, ctx);
        sui::clock::destroy_for_testing(clock);
        (coin0_out, coin1_out)
    }

    #[test_only]
    public fun sync_for_testing<T0, T1>(
        pair: &mut Pair<T0, T1>,
        ctx: &mut TxContext
    ) {
        let clock = sui::clock::create_for_testing(ctx);
        sync(pair, &clock, ctx);
        sui::clock::destroy_for_testing(clock);
    }
}