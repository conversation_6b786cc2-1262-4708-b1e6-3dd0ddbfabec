#[allow(unused_variable)]
module suitrump_dex::router {
    use sui::object::{Self, UID};
    use sui::tx_context::{Self, TxContext};
    use sui::transfer;
    use sui::coin::{Self, Coin};
    use std::option::{Self, Option};
    use suitrump_dex::factory::{Self, Factory};
    use suitrump_dex::pair::{Self, Pair, LPCoin};
    use suitrump_dex::library;
    use std::string::String;
    use sui::event;
    use sui::clock::{Self, Clock};
    use suitrump_dex::fixed_point_math::{Self, FixedPoint};

    // Error codes
    const ERR_EXPIRED: u64 = 301;
    const ERR_INSUFFICIENT_A_AMOUNT: u64 = 302;
    const ERR_INSUFFICIENT_B_AMOUNT: u64 = 303;
    const ERR_INSUFFICIENT_LIQUIDITY: u64 = 304;
    const ERR_INSUFFICIENT_OUTPUT_AMOUNT: u64 = 305;
    const ERR_EXCESSIVE_INPUT_AMOUNT: u64 = 306;
    const ERR_INVALID_PATH: u64 = 307;
    const ERR_PAIR_EXISTS: u64 = 308;
    const ERR_INVALID_INPUT: u64 = 310;
    const ERR_CALCULATION_OVERFLOW: u64 = 311;
    const ERR_SLIPPAGE_EXCEEDED: u64 = 312;
    const ERR_PAIR_NOT_EXISTS: u64 = 313;
    const ERR_INSUFFICIENT_INPUT_AMOUNT: u64 = 314;
    const ERR_EXTREME_RATIO: u64 = 315;
    const ERR_INSUFFICIENT_AMOUNT: u64 = 316;
    const ERR_INSUFFICIENT_COIN_FOR_EXACT_OUTPUT: u64 = 317;

    const ERR_INSUFFICIENT_COIN_A: u64 = 318;
    const ERR_INSUFFICIENT_COIN_B: u64 = 319;
    const ERR_INSUFFICIENT_INPUT_COIN: u64 = 320;
    const ERR_INSUFFICIENT_LP_TOKENS: u64 = 321;
    const ERR_AMOUNT_TOO_LARGE: u64 = 322;
    const ERR_ZERO_AMOUNT: u64 = 323;
    const ERR_INVALID_MIN_AMOUNT: u64 = 324;
    const ERR_NO_LP_TOKENS: u64 = 325;
    const ERR_REMAINDER_TOO_LARGE: u64 = 326;
    const ERR_ZERO_INPUT_AMOUNT: u64 = 327;
    const ERR_ZERO_MIN_OUTPUT: u64 = 328;

    // Constants
    const MINIMUM_LIQUIDITY: u256 = 10000;
    const BASIS_POINTS: u256 = 10000;
    const MAX_RATIO_SKEW: u256 = 100000000000000000000;
    const MIN_INITIAL_LIQUIDITY: u256 = 1000;
    
    /// Router struct to manage liquidity operations
    public struct Router has key {
        id: UID
    }

    /// Struct to hold reserve values
    public struct Reserves has store, copy, drop {
        reserve0: u256,
        reserve1: u256
    }

    fun init(ctx: &mut TxContext) {
        transfer::share_object(Router {
            id: object::new(ctx)
        });
    }

    public entry fun create_pair<T0, T1>(
        _router: &Router,
        factory: &mut Factory,
        token0_name: String,
        token1_name: String,
        ctx: &mut TxContext
    ) {

        factory::assert_not_paused(factory);
        let pair_opt = factory::get_pair<T0, T1>(factory);
        assert!(std::option::is_none(&pair_opt), ERR_PAIR_EXISTS);

        let pair_addr = factory::create_pair<T0, T1>(
            factory,
            token0_name,
            token1_name,
            ctx
        );
    }

    fun add_liquidity_internal(
        amount_a_desired: u256,
        amount_b_desired: u256,
        amount_a_min: u256,
        amount_b_min: u256,
        actual_coin_a: u256,
        actual_coin_b: u256,
        pair_exists: bool,
        reserves: Option<Reserves>
    ): (u256, u256) {
        assert!(fixed_point_math::is_safe_value(amount_a_desired), ERR_CALCULATION_OVERFLOW);
        assert!(fixed_point_math::is_safe_value(amount_b_desired), ERR_CALCULATION_OVERFLOW);

        if (!pair_exists) {
            validate_initial_ratio(amount_a_desired, amount_b_desired);
            return (amount_a_desired, amount_b_desired)
        };
        
        let (reserve_a, reserve_b) = if (std::option::is_some(&reserves)) {
            let r = std::option::destroy_some(reserves);
            (r.reserve0, r.reserve1)
        } else {
            (0, 0)
        };

        if (reserve_a == 0 && reserve_b == 0) {
            validate_initial_ratio(amount_a_desired, amount_b_desired);
            (amount_a_desired, amount_b_desired)
        } else {            
            // Limit desired amounts by what user actually has
            let max_a = min(amount_a_desired, actual_coin_a);
            let max_b = min(amount_b_desired, actual_coin_b);
            
            // Try using max_a, calculate required amount_b
            let amount_b_optimal = library::quote(max_a, reserve_a, reserve_b);
            
            if (amount_b_optimal <= max_b) {
                // max_a is the limiting factor
                assert!(amount_b_optimal >= amount_b_min, ERR_INSUFFICIENT_B_AMOUNT);
                (max_a, amount_b_optimal)
            } else {
                // max_b is the limiting factor
                let amount_a_optimal = library::quote(max_b, reserve_b, reserve_a);
                assert!(amount_a_optimal <= max_a, ERR_EXCESSIVE_INPUT_AMOUNT);
                assert!(amount_a_optimal >= amount_a_min, ERR_INSUFFICIENT_A_AMOUNT);
                (amount_a_optimal, max_b)
            }
        }
    }

    public entry fun add_liquidity<T0, T1>(
        _router: &Router,
        factory: &mut Factory,
        pair: &mut Pair<T0, T1>,
        mut coin_a: Coin<T0>,
        mut coin_b: Coin<T1>,
        amount_a_desired: u256,
        amount_b_desired: u256,
        amount_a_min: u256,
        amount_b_min: u256,
        token0_name: String,
        token1_name: String,
        deadline: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        factory::assert_not_paused(factory);
        let current_time = clock::timestamp_ms(clock) / 1000;
        let sender = tx_context::sender(ctx);
        
        // ✅ BATCH VALIDATION
        validate_liquidity_input(amount_a_desired, amount_b_desired, amount_a_min, amount_b_min, deadline, current_time);
        validate_coin_sufficiency(&coin_a, amount_a_desired, b"liquidity");
        validate_coin_sufficiency(&coin_b, amount_b_desired, b"liquidity");

        let pair_opt = factory::get_pair<T0, T1>(factory);
        let mut pair_exists = std::option::is_some(&pair_opt);
        assert!(pair_exists, ERR_PAIR_NOT_EXISTS);

        let mut reserves = std::option::none();
        if (pair_exists) {
            let (reserve0, reserve1, _) = pair::get_reserves(pair);
            reserves = std::option::some(Reserves { reserve0, reserve1 });
        };

        let (amount_a, amount_b) = add_liquidity_internal(
            amount_a_desired,
            amount_b_desired,
            amount_a_min,
            amount_b_min,
            (coin::value(&coin_a) as u256),
            (coin::value(&coin_b) as u256),
            pair_exists,
            reserves
        );

        // ✅ Safe splitting with validation
        let value_a = (coin::value(&coin_a) as u256);
        if (value_a > amount_a) {
            let remainder_amount = value_a - amount_a;
            assert!(remainder_amount <= (18446744073709551615 as u256), ERR_REMAINDER_TOO_LARGE);
            let remainder_a = coin::split(&mut coin_a, (remainder_amount as u64), ctx);
            transfer::public_transfer(remainder_a, sender);
        };

        let value_b = (coin::value(&coin_b) as u256);
        if (value_b > amount_b) {
            let remainder_amount = value_b - amount_b;
            assert!(remainder_amount <= (18446744073709551615 as u256), ERR_REMAINDER_TOO_LARGE);
            let remainder_b = coin::split(&mut coin_b, (remainder_amount as u64), ctx);
            transfer::public_transfer(remainder_b, sender);
        };

        let lp_tokens = pair::mint(pair, coin_a, coin_b, clock, ctx);
        transfer::public_transfer(lp_tokens, sender);
    }

    fun remove_liquidity_internal(
        total_supply: u256,
        lp_amount: u256,
        reserve0: u256,
        reserve1: u256,
        amount_a_min: u256,
        amount_b_min: u256
    ): (u256, u256) {
        assert!(lp_amount > 0 && total_supply > 0, ERR_INSUFFICIENT_LIQUIDITY);
        assert!(fixed_point_math::is_safe_value(lp_amount), ERR_CALCULATION_OVERFLOW);
        assert!(fixed_point_math::is_safe_value(total_supply), ERR_CALCULATION_OVERFLOW);
        
        // Use safe_proportion for liquidity calculations
        // This ensures consistency with pair.move and prevents precision loss
        let amount0 = fixed_point_math::safe_proportion(reserve0, lp_amount, total_supply);
        let amount1 = fixed_point_math::safe_proportion(reserve1, lp_amount, total_supply);
        
        // Validation remains the same
        assert!(amount0 >= amount_a_min, ERR_INSUFFICIENT_A_AMOUNT);
        assert!(amount1 >= amount_b_min, ERR_INSUFFICIENT_B_AMOUNT);
        
        (amount0, amount1)
    }

    public entry fun remove_liquidity<T0, T1>(
        _router: &Router,
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        mut lp_coins: vector<Coin<LPCoin<T0, T1>>>,
        amount_to_burn: u256,
        amount_a_min: u256,
        amount_b_min: u256,
        deadline: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        factory::assert_not_paused(factory);
        let current_time = clock::timestamp_ms(clock) / 1000;
        let sender = tx_context::sender(ctx);
        
        // ✅ BATCH VALIDATION
        validate_remove_liquidity_input(amount_to_burn, amount_a_min, amount_b_min, deadline, current_time);
        assert!(!vector::is_empty(&lp_coins), ERR_NO_LP_TOKENS);
        
        // Calculate total LP tokens provided
        let mut total_lp_provided = 0u256;
        let mut i = 0;
        while (i < vector::length(&lp_coins)) {
            let coin_value = coin::value(vector::borrow(&lp_coins, i));
            total_lp_provided = total_lp_provided + (coin_value as u256);
            i = i + 1;
        };
        
        // ✅ Validate user has enough LP tokens
        assert!(total_lp_provided >= amount_to_burn, ERR_INSUFFICIENT_LP_TOKENS);
        
        // Merge LP coins
        let mut merged_coin = vector::pop_back(&mut lp_coins);
        while (!vector::is_empty(&lp_coins)) {
            coin::join(&mut merged_coin, vector::pop_back(&mut lp_coins));
        };
        vector::destroy_empty(lp_coins);
        
        let total_lp = (coin::value(&merged_coin) as u256);
        assert!(total_lp >= amount_to_burn, ERR_INSUFFICIENT_LIQUIDITY);

        let burn_coin = if (total_lp == amount_to_burn) {
            merged_coin
        } else {
            let remaining_amount = total_lp - amount_to_burn;
            assert!(remaining_amount <= (18446744073709551615 as u256), ERR_REMAINDER_TOO_LARGE);
            let remaining = (remaining_amount as u64);
            let remaining_coin = coin::split(&mut merged_coin, remaining, ctx);
            transfer::public_transfer(remaining_coin, sender);
            merged_coin
        };

        let (reserve0, reserve1, _) = pair::get_reserves(pair);
        let total_supply = pair::total_supply(pair);

        let (amount0, amount1) = remove_liquidity_internal(
            total_supply,
            amount_to_burn,
            reserve0,
            reserve1,
            amount_a_min,
            amount_b_min 
        );

        let (coin0, coin1) = pair::burn(pair, burn_coin, clock, ctx);
        
        transfer::public_transfer(coin0, sender);
        transfer::public_transfer(coin1, sender);
    }

    fun exact_tokens0_swap<T0, T1>(
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        mut coin_in: Coin<T0>,
        desired_amount_in: u256,
        amount_out_min: u256,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        let coin_value = (coin::value(&coin_in) as u256);
        
        // ✅ CRITICAL: Validate coin amount
        assert!(coin_value >= desired_amount_in, ERR_INSUFFICIENT_INPUT_AMOUNT);
        assert!(desired_amount_in > 0, ERR_ZERO_AMOUNT);
        assert!(desired_amount_in <= (18446744073709551615 as u256), ERR_AMOUNT_TOO_LARGE);
        assert!(fixed_point_math::is_safe_value(desired_amount_in), ERR_CALCULATION_OVERFLOW);
        
        // ✅ Safe splitting with validation
        if (coin_value > desired_amount_in) {
            let remainder_amount = coin_value - desired_amount_in;
            assert!(remainder_amount <= (18446744073709551615 as u256), ERR_REMAINDER_TOO_LARGE);
            let remainder = coin::split(&mut coin_in, (remainder_amount as u64), ctx);
            transfer::public_transfer(remainder, sender);
        };

        let is_t0_token0 = get_token_position<T0, T1>(factory);
        let amount_out = library::get_amounts_out(factory, desired_amount_in, pair, is_t0_token0);
        assert!(amount_out >= amount_out_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);

        let (mut coin0_out, mut coin1_out) = pair::swap(
            pair,
            option::some(coin_in),
            option::none(),
            0,
            amount_out,
            clock,
            ctx
        );

        if (option::is_some(&coin0_out)) {
            transfer::public_transfer(option::extract(&mut coin0_out), sender);
        };
        option::destroy_none(coin0_out);

        if (option::is_some(&coin1_out)) {
            transfer::public_transfer(option::extract(&mut coin1_out), sender);
        };
        option::destroy_none(coin1_out);
    }

    fun exact_tokens1_swap<T0, T1>(
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        mut coin_in: Coin<T1>,
        desired_amount_in: u256,
        amount_out_min: u256,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        let coin_value = (coin::value(&coin_in) as u256);
        
        // ✅ CRITICAL: Validate coin amount
        assert!(coin_value >= desired_amount_in, ERR_INSUFFICIENT_INPUT_AMOUNT);
        assert!(desired_amount_in > 0, ERR_ZERO_AMOUNT);
        assert!(desired_amount_in <= (18446744073709551615 as u256), ERR_AMOUNT_TOO_LARGE);
        assert!(fixed_point_math::is_safe_value(desired_amount_in), ERR_CALCULATION_OVERFLOW);
        
        // ✅ Safe splitting with validation
        if (coin_value > desired_amount_in) {
            let remainder_amount = coin_value - desired_amount_in;
            assert!(remainder_amount <= (18446744073709551615 as u256), ERR_REMAINDER_TOO_LARGE);
            let remainder = coin::split(&mut coin_in, (remainder_amount as u64), ctx);
            transfer::public_transfer(remainder, sender);
        };

        let is_t0_token0 = get_token_position<T0, T1>(factory);
        let is_t1_input = !is_t0_token0;
        let amount_out = library::get_amounts_out(factory, desired_amount_in, pair, is_t1_input);
        assert!(amount_out >= amount_out_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);

        let (mut coin0_out, mut coin1_out) = pair::swap(
            pair,
            option::none(),
            option::some(coin_in),
            amount_out,
            0,
            clock,
            ctx
        );

        if (option::is_some(&coin0_out)) {
            transfer::public_transfer(option::extract(&mut coin0_out), sender);
        };
        option::destroy_none(coin0_out);

        if (option::is_some(&coin1_out)) {
            transfer::public_transfer(option::extract(&mut coin1_out), sender);
        };
        option::destroy_none(coin1_out);
    }

    // ===== FINAL EXACT OUTPUT FUNCTIONS WITH CORRECT DYNAMIC LOGIC =====
    
    fun exact_output_tokens0_swap<T0, T1>(
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        mut coin_in: Coin<T0>,
        amount_out: u256,
        amount_in_max: u256,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        let is_t0_token0 = get_token_position<T0, T1>(factory);
        let is_output_token0 = !is_t0_token0;
        let amount_in_required = library::get_amounts_in(factory, amount_out, pair, is_output_token0);
        assert!(amount_in_required <= amount_in_max, ERR_EXCESSIVE_INPUT_AMOUNT);

        let coin_value = (coin::value(&coin_in) as u256);
        assert!(coin_value >= amount_in_required, ERR_INSUFFICIENT_COIN_FOR_EXACT_OUTPUT);
        
        if (coin_value > amount_in_required) {
            let remainder_amount = coin_value - amount_in_required;
            assert!(remainder_amount <= (18446744073709551615 as u256), ERR_REMAINDER_TOO_LARGE);
            let remainder = coin::split(&mut coin_in, (remainder_amount as u64), ctx);
            transfer::public_transfer(remainder, sender);
        };

        let (mut coin0_out, mut coin1_out) = pair::swap(
            pair,
            option::some(coin_in),
            option::none(),
            0,
            amount_out,
            clock,
            ctx
        );

        if (option::is_some(&coin0_out)) {
            transfer::public_transfer(option::extract(&mut coin0_out), sender);
        };
        option::destroy_none(coin0_out);

        if (option::is_some(&coin1_out)) {
            transfer::public_transfer(option::extract(&mut coin1_out), sender);
        };
        option::destroy_none(coin1_out);
    }

    fun exact_output_tokens1_swap<T0, T1>(
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        mut coin_in: Coin<T1>,
        amount_out: u256,
        amount_in_max: u256,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);

        let is_t0_token0 = get_token_position<T0, T1>(factory);
        let amount_in_required = library::get_amounts_in(factory, amount_out, pair, is_t0_token0);
        assert!(amount_in_required <= amount_in_max, ERR_EXCESSIVE_INPUT_AMOUNT);

        let coin_value = (coin::value(&coin_in) as u256);
        assert!(coin_value >= amount_in_required, ERR_INSUFFICIENT_COIN_FOR_EXACT_OUTPUT);
        
        if (coin_value > amount_in_required) {
            let remainder_amount = coin_value - amount_in_required;
            assert!(remainder_amount <= (18446744073709551615 as u256), ERR_REMAINDER_TOO_LARGE);
            let remainder = coin::split(&mut coin_in, (remainder_amount as u64), ctx);
            transfer::public_transfer(remainder, sender);
        };

        let (mut coin0_out, mut coin1_out) = pair::swap(
            pair,
            option::none(),
            option::some(coin_in),
            amount_out,
            0,
            clock,
            ctx
        );

        if (option::is_some(&coin0_out)) {
            transfer::public_transfer(option::extract(&mut coin0_out), sender);
        };
        option::destroy_none(coin0_out);

        if (option::is_some(&coin1_out)) {
            transfer::public_transfer(option::extract(&mut coin1_out), sender);
        };
        option::destroy_none(coin1_out);
    }

    // ===== HANDLER 1: Input token is token0 in first pair, Output token is token0 in second pair =====
    // First hop: Input token (T0) -> Middle token (TMid)
    // Second hop: Middle token (TMid) -> Output token (T1)
    // First pair: Pair<T0, TMid>
    // Second pair: Pair<T1, TMid>
    public entry fun swap_exact_token0_to_mid_then_mid_to_token0<T0, TMid, T1>(
        _router: &Router,
        factory: &Factory,
        pair_first: &mut Pair<T0, TMid>,
        pair_second: &mut Pair<T1, TMid>,
        coin_in: Coin<T0>,
        amount_out_min: u256,
        mid_amount_min: u256,
        deadline: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        factory::assert_not_paused(factory);
        let current_time = clock::timestamp_ms(clock) / 1000;
        let sender = tx_context::sender(ctx);
        
        let amount_in = (coin::value(&coin_in) as u256);
        
        // ✅ BATCH VALIDATION
        validate_multihop_input(amount_in, amount_out_min, mid_amount_min, deadline, current_time);

        // First hop validation and execution: T0 -> TMid
        let is_t0_token0 = get_token_position<T0, TMid>(factory);
        let mid_amount_out = library::get_amounts_out(factory, amount_in, pair_first, is_t0_token0);
        
        assert!(mid_amount_out >= mid_amount_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        
        let (mut coin0_out, mut coin1_out) = pair::swap(
            pair_first,
            option::some(coin_in),
            option::none(),
            0,
            mid_amount_out,
            clock,
            ctx
        );
        
        assert!(option::is_some(&coin1_out), ERR_INSUFFICIENT_LIQUIDITY);
        let mid_coin = option::extract(&mut coin1_out);
        
        option::destroy_none(coin0_out);
        option::destroy_none(coin1_out);
        
        let actual_mid_amount = (coin::value(&mid_coin) as u256);
        assert!(actual_mid_amount >= mid_amount_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        
        // Second hop validation and execution: TMid -> T1
        let is_t1_token0_second = get_token_position<T1, TMid>(factory);
        let is_tmid_input = !is_t1_token0_second;
        let final_amount_out = library::get_amounts_out(factory, actual_mid_amount, pair_second, is_tmid_input);
        
        assert!(final_amount_out >= amount_out_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        
        let (mut coin0_final, mut coin1_final) = pair::swap(
            pair_second,
            option::none(),
            option::some(mid_coin),
            final_amount_out,
            0,
            clock,
            ctx
        );
        
        assert!(option::is_some(&coin0_final), ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        let final_coin = option::extract(&mut coin0_final);
        
        option::destroy_none(coin0_final);
        option::destroy_none(coin1_final);
        
        transfer::public_transfer(final_coin, sender);
    }

    // ===== HANDLER 2: Input token is token0 in first pair, Middle token is token0 in second pair =====
    // First hop: Input token (T0) -> Middle token (TMid)
    // Second hop: Middle token (TMid) -> Output token (T2)
    // First pair: Pair<T0, TMid>
    // Second pair: Pair<TMid, T2>
    public entry fun swap_exact_token0_to_mid_then_mid_to_token1<T0, TMid, T2>(
        _router: &Router,
        factory: &Factory,
        pair_first: &mut Pair<T0, TMid>,
        pair_second: &mut Pair<TMid, T2>,
        coin_in: Coin<T0>,
        amount_out_min: u256,
        mid_amount_min: u256,
        deadline: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        factory::assert_not_paused(factory);
        let current_time = clock::timestamp_ms(clock) / 1000;
        let sender = tx_context::sender(ctx);
        
        let amount_in = (coin::value(&coin_in) as u256);
        
        // ✅ BATCH VALIDATION
        validate_multihop_input(amount_in, amount_out_min, mid_amount_min, deadline, current_time);

        // First hop validation and execution: T0 -> TMid
        let is_t0_token0 = get_token_position<T0, TMid>(factory);
        let mid_amount_out = library::get_amounts_out(factory, amount_in, pair_first, is_t0_token0);
        
        assert!(mid_amount_out >= mid_amount_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        
        let (mut coin0_mid, mut coin1_mid) = pair::swap(
            pair_first,
            option::some(coin_in),
            option::none(),
            0,
            mid_amount_out,
            clock,
            ctx
        );
        
        assert!(option::is_some(&coin1_mid), ERR_INSUFFICIENT_LIQUIDITY);
        let mid_coin = option::extract(&mut coin1_mid);
        
        option::destroy_none(coin0_mid);
        option::destroy_none(coin1_mid);
        
        let actual_mid_amount = (coin::value(&mid_coin) as u256);
        assert!(actual_mid_amount >= mid_amount_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        
        // Second hop validation and execution: TMid -> T2
        let mid_amount = (coin::value(&mid_coin) as u256);
        assert!(mid_amount > 0, ERR_ZERO_INPUT_AMOUNT);
        
        let is_tmid_token0 = get_token_position<TMid, T2>(factory);
        let final_amount_out = library::get_amounts_out(factory, mid_amount, pair_second, is_tmid_token0);
        
        assert!(final_amount_out >= amount_out_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        
        let (mut coin0_final, mut coin1_final) = pair::swap(
            pair_second,
            option::some(mid_coin),
            option::none(),
            0,
            final_amount_out,
            clock,
            ctx
        );
        
        assert!(option::is_some(&coin1_final), ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        let final_coin = option::extract(&mut coin1_final);
        
        option::destroy_none(coin0_final);
        option::destroy_none(coin1_final);
        
        transfer::public_transfer(final_coin, sender);
    }

    // ===== HANDLER 3: Input token is token1 in first pair, Output token is token0 in second pair =====
    // First hop: Input token (T1) -> Middle token (TMid)
    // Second hop: Middle token (TMid) -> Output token (T0)
    // First pair: Pair<TMid, T1>
    // Second pair: Pair<T0, TMid>
    public entry fun swap_exact_token1_to_mid_then_mid_to_token0<T0, TMid, T1>(
        _router: &Router,
        factory: &Factory,
        pair_first: &mut Pair<TMid, T1>,
        pair_second: &mut Pair<T0, TMid>,
        coin_in: Coin<T1>,
        amount_out_min: u256,
        mid_amount_min: u256,
        deadline: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        factory::assert_not_paused(factory);
        let current_time = clock::timestamp_ms(clock) / 1000;
        let sender = tx_context::sender(ctx);
        
        let amount_in = (coin::value(&coin_in) as u256);
        
        // ✅ BATCH VALIDATION
        validate_multihop_input(amount_in, amount_out_min, mid_amount_min, deadline, current_time);

        // First hop: T1 -> TMid
        let is_tmid_token0_first = get_token_position<TMid, T1>(factory);
        let is_t1_input = !is_tmid_token0_first;
        let mid_amount_out = library::get_amounts_out(factory, amount_in, pair_first, is_t1_input);
        
        assert!(mid_amount_out >= mid_amount_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        
        let (mut coin0_mid, mut coin1_mid) = pair::swap(
            pair_first,
            option::none(),
            option::some(coin_in),
            mid_amount_out,
            0,
            clock,
            ctx
        );
        
        assert!(option::is_some(&coin0_mid), ERR_INSUFFICIENT_LIQUIDITY);
        let mid_coin = option::extract(&mut coin0_mid);
        
        option::destroy_none(coin0_mid);
        option::destroy_none(coin1_mid);
        
        let actual_mid_amount = (coin::value(&mid_coin) as u256);
        assert!(actual_mid_amount >= mid_amount_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        
        // Second hop: TMid -> T0
        let mid_amount = (coin::value(&mid_coin) as u256);
        assert!(mid_amount > 0, ERR_ZERO_INPUT_AMOUNT);
        
        let is_t0_token0_second = get_token_position<T0, TMid>(factory);
        let is_tmid_input = !is_t0_token0_second;
        let final_amount_out = library::get_amounts_out(factory, mid_amount, pair_second, is_tmid_input);
        
        assert!(final_amount_out >= amount_out_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        
        let (mut coin0_final, mut coin1_final) = pair::swap(
            pair_second,
            option::none(),
            option::some(mid_coin),
            final_amount_out,
            0,
            clock,
            ctx
        );
        
        assert!(option::is_some(&coin0_final), ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        let final_coin = option::extract(&mut coin0_final);
        
        option::destroy_none(coin0_final);
        option::destroy_none(coin1_final);
        
        transfer::public_transfer(final_coin, sender);
    }

    // ===== HANDLER 4: Input token is token1 in first pair, Middle token is token0 in second pair =====
    // First hop: Input token (T1) -> Middle token (TMid)
    // Second hop: Middle token (TMid) -> Output token (T2)
    // First pair: Pair<TMid, T1>
    // Second pair: Pair<TMid, T2>
    public entry fun swap_exact_token1_to_mid_then_mid_to_token1<TMid, T1, T2>(
        _router: &Router,
        factory: &Factory,
        pair_first: &mut Pair<TMid, T1>,
        pair_second: &mut Pair<TMid, T2>,
        coin_in: Coin<T1>,
        amount_out_min: u256,
        mid_amount_min: u256,
        deadline: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        factory::assert_not_paused(factory);
        let current_time = clock::timestamp_ms(clock) / 1000;
        let sender = tx_context::sender(ctx);
        
        let amount_in = (coin::value(&coin_in) as u256);
        
        // ✅ BATCH VALIDATION
        validate_multihop_input(amount_in, amount_out_min, mid_amount_min, deadline, current_time);

        // First hop: T1 -> TMid
        let is_tmid_token0_first = get_token_position<TMid, T1>(factory);
        let is_t1_input = !is_tmid_token0_first;
        let mid_amount_out = library::get_amounts_out(factory, amount_in, pair_first, is_t1_input);
        
        assert!(mid_amount_out >= mid_amount_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        
        let (mut coin0_mid, mut coin1_mid) = pair::swap(
            pair_first,
            option::none(),
            option::some(coin_in),
            mid_amount_out,
            0,
            clock,
            ctx
        );
        
        assert!(option::is_some(&coin0_mid), ERR_INSUFFICIENT_LIQUIDITY);
        let mid_coin = option::extract(&mut coin0_mid);
        
        option::destroy_none(coin0_mid);
        option::destroy_none(coin1_mid);
        
        let actual_mid_amount = (coin::value(&mid_coin) as u256);
        assert!(actual_mid_amount >= mid_amount_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        
        // Second hop: TMid -> T2
        let mid_amount = (coin::value(&mid_coin) as u256);
        assert!(mid_amount > 0, ERR_ZERO_INPUT_AMOUNT);
        
        let is_tmid_token0_second = get_token_position<TMid, T2>(factory);
        let final_amount_out = library::get_amounts_out(factory, mid_amount, pair_second, is_tmid_token0_second);
        
        assert!(final_amount_out >= amount_out_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        
        let (mut coin0_final, mut coin1_final) = pair::swap(
            pair_second,
            option::some(mid_coin),
            option::none(),
            0,
            final_amount_out,
            clock,
            ctx
        );
        
        assert!(option::is_some(&coin1_final), ERR_INSUFFICIENT_OUTPUT_AMOUNT);
        let final_coin = option::extract(&mut coin1_final);
        
        option::destroy_none(coin0_final);
        option::destroy_none(coin1_final);
        
        transfer::public_transfer(final_coin, sender);
    }

    // Entry points
    public entry fun swap_exact_tokens0_for_tokens1<T0, T1>(
        _router: &Router,
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        coin_in: Coin<T0>,
        desired_amount_in: u256,
        amount_out_min: u256,
        deadline: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        factory::assert_not_paused(factory);
        let current_time = clock::timestamp_ms(clock) / 1000;
        
        // ✅ Batch validation
        validate_swap_input(desired_amount_in, amount_out_min, deadline, current_time);
        validate_coin_sufficiency(&coin_in, desired_amount_in, b"swap");
        
        exact_tokens0_swap(factory, pair, coin_in, desired_amount_in, amount_out_min, clock, ctx);
    }

    public entry fun swap_exact_tokens1_for_tokens0<T0, T1>(
        _router: &Router,
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        coin_in: Coin<T1>,
        desired_amount_in: u256,
        amount_out_min: u256,
        deadline: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        factory::assert_not_paused(factory);
        let current_time = clock::timestamp_ms(clock) / 1000;
        
        // ✅ Batch validation
        validate_swap_input(desired_amount_in, amount_out_min, deadline, current_time);
        validate_coin_sufficiency(&coin_in, desired_amount_in, b"swap");
        
        exact_tokens1_swap(factory, pair, coin_in, desired_amount_in, amount_out_min, clock, ctx);
    }

    public entry fun swap_tokens0_for_exact_tokens1<T0, T1>(
        _router: &Router,
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        coin_in: Coin<T0>,
        amount_out: u256,
        amount_in_max: u256,
        deadline: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        factory::assert_not_paused(factory);
        let current_time = clock::timestamp_ms(clock) / 1000;
        
        // ✅ Batch validation
        validate_exact_output_input(amount_out, amount_in_max, deadline, current_time);
        validate_coin_sufficiency(&coin_in, amount_in_max, b"swap");
        
        exact_output_tokens0_swap(factory, pair, coin_in, amount_out, amount_in_max, clock, ctx);
    }

    public entry fun swap_tokens1_for_exact_tokens0<T0, T1>(
        _router: &Router,
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        coin_in: Coin<T1>,
        amount_out: u256,
        amount_in_max: u256,
        deadline: u64,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        factory::assert_not_paused(factory);
        let current_time = clock::timestamp_ms(clock) / 1000;
        
        // ✅ Batch validation
        validate_exact_output_input(amount_out, amount_in_max, deadline, current_time);
        validate_coin_sufficiency(&coin_in, amount_in_max, b"swap");
        
        exact_output_tokens1_swap(factory, pair, coin_in, amount_out, amount_in_max, clock, ctx);
    }

    // Quote and utility functions
    public fun quote_liquidity_amount<T0, T1>(
        factory: &Factory,
        pair: &Pair<T0, T1>,
        amount0_desired: u256,
        amount1_desired: u256
    ): u256 {
        let (reserve0, reserve1) = library::get_reserves(factory, pair);
        let supply = pair::total_supply(pair);
        
        if (reserve0 == 0 && reserve1 == 0) {
            // ✅ KEEP: Initial liquidity calculation needs sqrt (complex math)
            let liquidity_fp = fixed_point_math::sqrt(
                fixed_point_math::mul(
                    fixed_point_math::new(amount0_desired),
                    fixed_point_math::new(amount1_desired)
                )
            );
            let initial_liquidity = fixed_point_math::get_raw_value(liquidity_fp);
            assert!(initial_liquidity > MINIMUM_LIQUIDITY, ERR_INSUFFICIENT_LIQUIDITY);
            initial_liquidity - MINIMUM_LIQUIDITY
        } else {
            // Use safe_proportion for liquidity quote calculations
            let liquidity0 = fixed_point_math::safe_proportion(amount0_desired, supply, reserve0);
            let liquidity1 = fixed_point_math::safe_proportion(amount1_desired, supply, reserve1);
            
            min(liquidity0, liquidity1)
        }
    }

    fun min(a: u256, b: u256): u256 {
        if (a < b) { a } else { b }
    }

    public fun get_reserves_ratio<T0, T1>(
        pair: &Pair<T0, T1>
    ): FixedPoint {
        let (reserve0, reserve1, _) = pair::get_reserves(pair);
        assert!(reserve1 != 0, ERR_INSUFFICIENT_LIQUIDITY);
        
        fixed_point_math::div(
            fixed_point_math::new(reserve0),
            fixed_point_math::new(reserve1)
        )
    }

    /// Determines if input token is token0 in the pair
    public fun get_token_position<TInput, TOther>(
        factory: &Factory
    ): bool {
        let sorted_pair = factory::sort_tokens<TInput, TOther>();
        factory::is_token0<TInput>(&sorted_pair)
    }


    fun validate_initial_ratio(amount_a: u256, amount_b: u256) {
        assert!(amount_a >= MIN_INITIAL_LIQUIDITY, ERR_INSUFFICIENT_AMOUNT);
        assert!(amount_b >= MIN_INITIAL_LIQUIDITY, ERR_INSUFFICIENT_AMOUNT);
        
        // Prevent extreme ratios (max 10000:1 in either direction)
        assert!(amount_a <= amount_b * MAX_RATIO_SKEW, ERR_EXTREME_RATIO);
        assert!(amount_b <= amount_a * MAX_RATIO_SKEW, ERR_EXTREME_RATIO);
    }

    // Batch validation for swap inputs
    fun validate_swap_input(
        amount_in: u256,
        amount_out_min: u256,
        deadline: u64,
        current_time: u64
    ) {
        assert!(amount_in > 0, ERR_ZERO_AMOUNT);
        assert!(amount_out_min > 0, ERR_ZERO_MIN_OUTPUT);
        assert!(deadline >= current_time, ERR_EXPIRED);
        assert!(amount_in <= (18446744073709551615 as u256), ERR_AMOUNT_TOO_LARGE);
        assert!(amount_out_min <= (18446744073709551615 as u256), ERR_AMOUNT_TOO_LARGE);
        assert!(fixed_point_math::is_safe_value(amount_in), ERR_CALCULATION_OVERFLOW);
        assert!(fixed_point_math::is_safe_value(amount_out_min), ERR_CALCULATION_OVERFLOW);
    }

    // Batch validation for exact output swaps
    fun validate_exact_output_input(
        amount_out: u256,
        amount_in_max: u256,
        deadline: u64,
        current_time: u64
    ) {
        assert!(amount_out > 0, ERR_ZERO_AMOUNT);
        assert!(amount_in_max > 0, ERR_ZERO_AMOUNT);
        assert!(deadline >= current_time, ERR_EXPIRED);
        assert!(amount_out <= (18446744073709551615 as u256), ERR_AMOUNT_TOO_LARGE);
        assert!(amount_in_max <= (18446744073709551615 as u256), ERR_AMOUNT_TOO_LARGE);
        assert!(fixed_point_math::is_safe_value(amount_out), ERR_CALCULATION_OVERFLOW);
        assert!(fixed_point_math::is_safe_value(amount_in_max), ERR_CALCULATION_OVERFLOW);
    }

    // Batch validation for multi-hop swaps
    fun validate_multihop_input(
        amount_in: u256,
        amount_out_min: u256,
        mid_amount_min: u256,
        deadline: u64,
        current_time: u64
    ) {
        assert!(amount_in > 0, ERR_ZERO_INPUT_AMOUNT);
        assert!(amount_out_min > 0, ERR_ZERO_MIN_OUTPUT);
        assert!(mid_amount_min > 0, ERR_ZERO_MIN_OUTPUT);
        assert!(deadline >= current_time, ERR_EXPIRED);
        assert!(amount_in <= (18446744073709551615 as u256), ERR_AMOUNT_TOO_LARGE);
        assert!(amount_out_min <= (18446744073709551615 as u256), ERR_AMOUNT_TOO_LARGE);
        assert!(mid_amount_min <= (18446744073709551615 as u256), ERR_AMOUNT_TOO_LARGE);
        assert!(fixed_point_math::is_safe_value(amount_in), ERR_CALCULATION_OVERFLOW);
    }

    // Validate coin has sufficient balance
    fun validate_coin_sufficiency<T>(
        coin: &Coin<T>,
        required_amount: u256,
        operation_type: vector<u8> // b"swap" or b"liquidity"
    ) {
        let actual_amount = (coin::value(coin) as u256);
        assert!(actual_amount >= required_amount, ERR_INSUFFICIENT_INPUT_COIN);
        
        // Additional checks based on operation
        if (operation_type == b"liquidity") {
            assert!(required_amount >= 1000, ERR_INSUFFICIENT_AMOUNT); // Min liquidity
        };
    }

    // Batch validation for liquidity operations
    fun validate_liquidity_input(
        amount_a_desired: u256,
        amount_b_desired: u256,
        amount_a_min: u256,
        amount_b_min: u256,
        deadline: u64,
        current_time: u64
    ) {
        assert!(amount_a_desired > 0, ERR_ZERO_AMOUNT);
        assert!(amount_b_desired > 0, ERR_ZERO_AMOUNT);
        assert!(amount_a_min <= amount_a_desired, ERR_INVALID_MIN_AMOUNT);
        assert!(amount_b_min <= amount_b_desired, ERR_INVALID_MIN_AMOUNT);
        assert!(deadline >= current_time, ERR_EXPIRED);
        assert!(amount_a_desired <= (18446744073709551615 as u256), ERR_AMOUNT_TOO_LARGE);
        assert!(amount_b_desired <= (18446744073709551615 as u256), ERR_AMOUNT_TOO_LARGE);
        assert!(fixed_point_math::is_safe_value(amount_a_desired), ERR_CALCULATION_OVERFLOW);
        assert!(fixed_point_math::is_safe_value(amount_b_desired), ERR_CALCULATION_OVERFLOW);
    }

    // Batch validation for remove liquidity
    fun validate_remove_liquidity_input(
        amount_to_burn: u256,
        amount_a_min: u256,
        amount_b_min: u256,
        deadline: u64,
        current_time: u64
    ) {
        assert!(amount_to_burn > 0, ERR_ZERO_AMOUNT);
        assert!(deadline >= current_time, ERR_EXPIRED);
        assert!(amount_to_burn <= (18446744073709551615 as u256), ERR_AMOUNT_TOO_LARGE);
        assert!(fixed_point_math::is_safe_value(amount_to_burn), ERR_CALCULATION_OVERFLOW);
    }


    #[test_only]
    public fun init_for_testing(ctx: &mut TxContext) {
        init(ctx)
    }

    #[test_only]
    public fun create_for_testing(ctx: &mut TxContext): Router {
        Router {
            id: object::new(ctx)
        }
    }

    #[test_only]
    public fun add_liquidity_for_testing<T0, T1>(
        router: &Router,
        factory: &mut Factory,
        pair: &mut Pair<T0, T1>,
        coin_a: Coin<T0>,
        coin_b: Coin<T1>,
        amount_a_desired: u256,
        amount_b_desired: u256,
        amount_a_min: u256,
        amount_b_min: u256,
        token0_name: String,
        token1_name: String,
        deadline: u64,
        ctx: &mut TxContext
    ) {
        let clock = sui::clock::create_for_testing(ctx);
        add_liquidity(
            router,
            factory,
            pair,
            coin_a,
            coin_b,
            amount_a_desired,
            amount_b_desired,
            amount_a_min,
            amount_b_min,
            token0_name,
            token1_name,
            deadline,
            &clock,
            ctx
        );
        sui::clock::destroy_for_testing(clock);
    }

    #[test_only]
    public fun remove_liquidity_for_testing<T0, T1>(
        router: &Router,
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        lp_coins: vector<Coin<LPCoin<T0, T1>>>,
        amount_to_burn: u256,
        amount_a_min: u256,
        amount_b_min: u256,
        deadline: u64,
        ctx: &mut TxContext
    ) {
        let clock = sui::clock::create_for_testing(ctx);
        remove_liquidity(
            router,
            factory,
            pair,
            lp_coins,
            amount_to_burn,
            amount_a_min,
            amount_b_min,
            deadline,
            &clock,
            ctx
        );
        sui::clock::destroy_for_testing(clock);
    }

    #[test_only]
    public fun swap_exact_tokens0_for_tokens1_for_testing<T0, T1>(
        router: &Router,
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        coin_in: Coin<T0>,
        desired_amount_in: u256,
        amount_out_min: u256,
        deadline: u64,
        ctx: &mut TxContext
    ) {
        let clock = sui::clock::create_for_testing(ctx);
        swap_exact_tokens0_for_tokens1(
            router,
            factory,
            pair,
            coin_in,
            desired_amount_in,
            amount_out_min,
            deadline,
            &clock,
            ctx
        );
        sui::clock::destroy_for_testing(clock);
    }

    #[test_only]
    public fun swap_exact_tokens1_for_tokens0_for_testing<T0, T1>(
        router: &Router,
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        coin_in: Coin<T1>,
        desired_amount_in: u256,            
        amount_out_min: u256,
        deadline: u64,
        ctx: &mut TxContext
    ) {
        let clock = sui::clock::create_for_testing(ctx);
        swap_exact_tokens1_for_tokens0(
            router,
            factory,
            pair,
            coin_in,
            desired_amount_in,            
            amount_out_min,
            deadline,
            &clock,
            ctx
        );
        sui::clock::destroy_for_testing(clock);
    }

    #[test_only]
    public fun swap_tokens0_for_exact_tokens1_for_testing<T0, T1>(
        router: &Router,
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        coin_in: Coin<T0>,
        amount_out: u256,
        amount_in_max: u256,
        deadline: u64,
        ctx: &mut TxContext
    ) {
        let clock = sui::clock::create_for_testing(ctx);
        swap_tokens0_for_exact_tokens1(
            router,
            factory,
            pair,
            coin_in,
            amount_out,
            amount_in_max,
            deadline,
            &clock,
            ctx
        );
        sui::clock::destroy_for_testing(clock);
    }

    #[test_only]
    public fun swap_tokens1_for_exact_tokens0_for_testing<T0, T1>(
        router: &Router,
        factory: &Factory,
        pair: &mut Pair<T0, T1>,
        coin_in: Coin<T1>,
        amount_out: u256,
        amount_in_max: u256,
        deadline: u64,
        ctx: &mut TxContext
    ) {
        let clock = sui::clock::create_for_testing(ctx);
        swap_tokens1_for_exact_tokens0(
            router,
            factory,
            pair,
            coin_in,
            amount_out,
            amount_in_max,
            deadline,
            &clock,
            ctx
        );
        sui::clock::destroy_for_testing(clock);
    }
}