#[allow(unused_variable)]
module suitrump_dex::victory_token_locker {
    use sui::object::{Self, ID, UID};
    use sui::transfer;
    use sui::tx_context::{Self, TxContext};
    use sui::coin::{Self, Coin};
    use sui::balance::{Self, Balance};
    use sui::clock::{Self, Clock};
    use suitrump_dex::victory_token::{VICTORY_TOKEN};
    use sui::sui::SUI;
    use sui::event;
    use sui::table::{Self, Table};
    use std::vector;
    use std::string::{Self, String};
    use std::option::{Self, Option};
    use suitrump_dex::global_emission_controller::{Self, GlobalEmissionConfig};

    // Error codes
    const ENO_LOCK_PERIOD_MATCH: u64 = 1;
    const ELOCK_NOT_EXPIRED: u64 = 2;
    const EZERO_AMOUNT: u64 = 3;
    const ELOCK_NOT_FOUND: u64 = 4;
    const E_THREE_YEAR_LOCK_UNAVAILABLE: u64 = 5;
    const E_NOT_AUTHORIZED: u64 = 6;
    const E_INSUFFICIENT_REWARDS: u64 = 7;
    const E_INVALID_LOCK_PERIOD: u64 = 8;
    const EALREADY_CLAIMED: u64 = 9;
    const EWEEK_NOT_FINISHED: u64 = 10;
    const ECLAIMING_DISABLED: u64 = 11;
    const EEPOCH_NOT_FOUND: u64 = 12;
    const ESTAKED_AFTER_WEEK_START: u64 = 13;
    const ELOCK_EXPIRED_DURING_WEEK: u64 = 14;
    const ESTAKED_DURING_WEEK_NOT_ELIGIBLE: u64 = 15;
    const ENO_SUI_REWARDS: u64 = 16;
    const ENO_VICTORY_REWARDS: u64 = 17;
    const ENO_TIME_ELAPSED: u64 = 18;
    const E_CLAIM_TOO_FREQUENT: u64 = 19;
    const EVICTORY_ALLOCATION_NOT_100_PERCENT: u64 = 20;
    const ESUI_ALLOCATION_NOT_100_PERCENT: u64 = 21;
    // New emission-related error codes
    const ERROR_EMISSIONS_NOT_INITIALIZED: u64 = 22;
    const ERROR_EMISSIONS_ENDED: u64 = 23;
    const ERROR_EMISSIONS_PAUSED: u64 = 24;
    // New balance management error codes
    const E_INSUFFICIENT_LOCKED_BALANCE: u64 = 25;
    const E_BALANCE_TRACKING_ERROR: u64 = 26;
    const E_VAULT_BALANCE_MISMATCH: u64 = 27;
    // Production error codes
    const E_EPOCH_ALREADY_FINALIZED: u64 = 28;
    const E_ALLOCATIONS_NOT_FINALIZED: u64 = 29;
    const E_ZERO_ADDRESS: u64 = 30; 
    const E_INVALID_BATCH_DATA: u64 = 31;
    const E_INSUFFICIENT_TOKEN_BALANCE: u64 = 32;
    const E_INSUFFICIENT_LOCK_AMOUNT: u64 = 33;
    const ELOCK_EXPIRED_CANNOT_CLAIM: u64 = 34;
    const E_LOCK_AMOUNT_TOO_LARGE: u64 = 35;
    const E_ACCUMULATOR_OVERFLOW_RISK: u64 = 36;
    const E_EPOCH_TOO_LARGE: u64 = 37;
    const E_LOCK_ID_TOO_LARGE: u64 = 38;
    const E_REVENUE_ALREADY_ADDED: u64 = 39;
    const E_INVALID_TOTAL_ALLOCATION: u64 = 40;
    const E_ALLOCATION_MATH_ERROR: u64 = 41;
    const E_PERCENTAGE_TOO_LARGE: u64 = 42;
    const E_ALLOCATION_TOO_LARGE: u64 = 43;
    const E_VAULT_BALANCE_OVERFLOW: u64 = 44;
    const E_VAULT_TRACKING_OVERFLOW: u64 = 45;
    const E_INSUFFICIENT_POOL_BALANCE: u64 = 46;
    const E_INSUFFICIENT_TOTAL_STAKE: u64 = 47;
    const E_WEEK_NOT_FINISHED: u64 = 48;


    // Constants for lock periods (in days)
    const WEEK_LOCK: u64 = 7;
    const THREE_MONTH_LOCK: u64 = 90;
    const YEAR_LOCK: u64 = 365;
    const THREE_YEAR_LOCK: u64 = 1095;
    
    const SECONDS_PER_DAY: u64 = 86400;
    const BASIS_POINTS: u64 = 10000;

    const MIN_LOCK_AMOUNT_WEEK: u64 = 1000000;      // 1 Victory token
    const MIN_LOCK_AMOUNT_3_MONTH: u64 = 5000000;   // 5 Victory tokens  
    const MIN_LOCK_AMOUNT_1_YEAR: u64 = 10000000;   // 10 Victory tokens
    const MIN_LOCK_AMOUNT_3_YEAR: u64 = 25000000;   // 25 Victory tokens
    const MIN_TOTAL_STAKE_FOR_FUNDING: u64 = 100000000; // 100 Victory tokens
    const MAX_SINGLE_LOCK_AMOUNT: u64 = 100_000_000_000_000;
    const PRECISION_FACTOR: u256 = 1000000000000000000u256; // 1e18
    const MIN_CLAIM_INTERVAL: u64 = 3600;
    // Events
    public struct TokensLocked has copy, drop {
        user: address,
        lock_id: u64,
        amount: u64,
        lock_period: u64,
        lock_end: u64,
    }
    
    public struct TokensUnlocked has copy, drop {
        user: address,
        lock_id: u64,
        amount: u64,
        victory_rewards: u64,
        sui_rewards: u64,
        timestamp: u64,
    }
    
    public struct VictoryRewardsClaimed has copy, drop {
        user: address,
        lock_id: u64,
        amount: u64,
        timestamp: u64,
        total_claimed_for_lock: u64,
    }
    
    public struct PoolSUIClaimed has copy, drop {
        user: address,
        epoch_id: u64,
        lock_id: u64,
        lock_period: u64,
        pool_type: u8,
        amount_staked: u64,
        sui_claimed: u64,
        timestamp: u64,
    }
    
    public struct WeeklyRevenueAdded has copy, drop {
        epoch_id: u64,
        amount: u64,
        total_week_revenue: u64,
        week_pool_sui: u64,
        three_month_pool_sui: u64,
        year_pool_sui: u64,
        three_year_pool_sui: u64,
        dynamic_allocations_used: bool,
        timestamp: u64,
    }
    
    public struct VictoryAllocationsUpdated has copy, drop {
        week_allocation: u64,
        three_month_allocation: u64,
        year_allocation: u64,
        three_year_allocation: u64,
        total_check: u64,
        timestamp: u64,
    }
    
    public struct SUIAllocationsUpdated has copy, drop {
        week_allocation: u64,
        three_month_allocation: u64,
        year_allocation: u64,
        three_year_allocation: u64,
        total_check: u64,
        timestamp: u64,
    }
    
    public struct VaultDeposit has copy, drop {
        vault_type: String,
        amount: u64,
        total_balance: u64,
        timestamp: u64,
    }


    // Event for SUI auto-claim transparency
    public struct SUIAutoClaimSummary has copy, drop {
        user: address,
        lock_id: u64,
        total_sui_claimed: u64,
        timestamp: u64,
    }

    // New emission-related events
    public struct EmissionWarning has copy, drop {
        message: String,
        lock_id: Option<u64>,
        timestamp: u64,
    }

    public struct EpochCreated has copy, drop {
        epoch_id: u64,
        week_start: u64,
        week_end: u64,
        timestamp: u64,
    }

    public struct AdminPresaleLockCreated has copy, drop {
        admin: address,
        user: address,
        lock_id: u64,
        amount: u64,
        lock_period: u64,
        lock_end: u64,
        timestamp: u64,
    }

    public struct BatchClaimCompleted has copy, drop {
        user: address,
        lock_id: u64,
        epochs_claimed: u64,
        total_sui_claimed: u64,
        timestamp: u64,
    }

    public struct UltimateBatchClaimCompleted has copy, drop {
        user: address,
        locks_processed: u64,
        total_epochs_claimed: u64,
        total_sui_claimed: u64,
        timestamp: u64,
    }

    public struct SmartClaimResult has copy, drop {
        user: address,
        strategy_used: String,
        locks_processed: u64,
        epochs_claimed: u64,
        sui_claimed: u64,
        timestamp: u64,
    }
    
    // Admin capability
    public struct AdminCap has key { 
        id: UID 
    }
    
    // 🔒 Dedicated vault for users' locked tokens (NEVER touched for rewards)
    public struct LockedTokenVault has key {
        id: UID,
        locked_balance: Balance<VICTORY_TOKEN>,  // Only stores locked tokens
        total_locked_amount: u64,                // Tracking total locked
        total_unlocked_amount: u64,              // Tracking total unlocked
        lock_count: u64,                         // Number of active locks
        unlock_count: u64,                       // Number of completed unlocks
    }
    
    // 🎁 Victory rewards vault (admin-funded rewards only)
    public struct VictoryRewardVault has key {
        id: UID,
        victory_balance: Balance<VICTORY_TOKEN>,
        total_deposited: u64,                    // Admin deposits tracking
        total_distributed: u64,                  // Reward distributions tracking
    }
    
    // 💰 SUI rewards vault (epoch-based distribution)
    public struct SUIRewardVault has key {
        id: UID,
        sui_balance: Balance<SUI>,
        total_deposited: u64,                    // SUI revenue tracking
        total_distributed: u64,                  // SUI distributions tracking
    }
    
    // Weekly pool allocation structure
    public struct WeeklyPoolAllocations has store, drop, copy {
        epoch_id: u64,
        week_pool_allocation: u64,
        three_month_pool_allocation: u64,
        year_pool_allocation: u64,
        three_year_pool_allocation: u64,
        week_pool_sui: u64,
        three_month_pool_sui: u64,
        year_pool_sui: u64,
        three_year_pool_sui: u64,
        week_pool_total_staked: u64,
        three_month_pool_total_staked: u64,
        year_pool_total_staked: u64,
        three_year_pool_total_staked: u64,
    }
    
    // Weekly revenue epoch structure
    public struct WeeklyRevenueEpoch has store, drop {
        epoch_id: u64,
        week_start_timestamp: u64,
        week_end_timestamp: u64,
        total_sui_revenue: u64,
        pool_allocations: WeeklyPoolAllocations,
        week_pool_claimed: u64,
        three_month_pool_claimed: u64,
        year_pool_claimed: u64,
        three_year_pool_claimed: u64,
        is_claimable: bool,
        allocations_finalized: bool,
    }
    
    // MasterChef Victory pool accumulator
    public struct VictoryPoolAccumulator has store {
        acc_victory_per_share: u256,        // Accumulated rewards per share (×1e12)
        last_reward_time: u64,              // Last update timestamp
        total_staked: u64,                  // Current pool total
        total_victory_distributed: u64,     // Total Victory distributed
        lock_period: u64,                   // Pool identifier
    }

    // MasterChef user position
    public struct VictoryUserPosition has store {
        stake_amount: u64,                  // Amount staked
        lock_start_time: u64,               // When locked
        lock_end_time: u64,                 // When expires
        lock_period: u64,                   // Lock duration
        victory_reward_debt: u256,          // MasterChef debt (×1e12)
        total_victory_claimed: u64,         // Total claimed
        last_victory_claim_time: u64,       // Last claim time
        is_active: bool,                    // Position status
        position_id: u64,                   // Lock ID
    }

    // Main locker structure 
    public struct TokenLocker has key {
        id: UID,
        
        // Lock period pools
        week_locks: Table<address, vector<Lock>>,
        three_month_locks: Table<address, vector<Lock>>,
        year_locks: Table<address, vector<Lock>>,
        three_year_locks: Table<address, vector<Lock>>,
        
        // Pool totals
        week_total_locked: u64,
        three_month_total_locked: u64,
        year_total_locked: u64,
        three_year_total_locked: u64,
        total_locked: u64,
        
        // Balance tracking (NEW)
        total_locked_tokens: u64,        // Should match LockedTokenVault
        total_reward_tokens: u64,        // Should match VictoryRewardVault deposits
        
        // Dynamic Victory allocations (admin configurable)
        victory_week_allocation: u64,
        victory_three_month_allocation: u64,
        victory_year_allocation: u64,
        victory_three_year_allocation: u64,
        
        // Dynamic SUI allocations (admin configurable)
        sui_week_allocation: u64,
        sui_three_month_allocation: u64,
        sui_year_allocation: u64,
        sui_three_year_allocation: u64,
        
        // SUI epoch system
        weekly_epochs: Table<u64, WeeklyRevenueEpoch>,
        current_epoch_id: u64,
        current_week_start: u64,
        
        // Anti-double claim tracking
        user_epoch_claims: Table<address, Table<u128, PoolClaimRecord>>,
        user_victory_claims: Table<address, Table<u64, VictoryClaimRecord>>,
        
        next_lock_id: u64,

        // MasterChef Victory accumulators
        victory_week_accumulator: VictoryPoolAccumulator,
        victory_three_month_accumulator: VictoryPoolAccumulator, 
        victory_year_accumulator: VictoryPoolAccumulator,
        victory_three_year_accumulator: VictoryPoolAccumulator,

        // User position tracking
        victory_user_positions: Table<address, Table<u64, VictoryUserPosition>>,
    }
    
    // Individual lock structure
    public struct Lock has store, copy, drop {
        id: u64,
        amount: u64,
        lock_period: u64,
        lock_end: u64,
        stake_timestamp: u64,
        last_victory_claim_timestamp: u64,
        total_victory_claimed: u64,
        last_sui_epoch_claimed: u64,
        claimed_sui_epochs: vector<u64>,
    }
    
    // SUI claim record for anti-double claiming
    public struct PoolClaimRecord has store {
        epoch_id: u64,
        lock_id: u64,
        lock_period: u64,
        pool_type: u8,
        amount_staked: u64,
        sui_claimed: u64,
        claim_timestamp: u64,
    }
    
    // Victory claim record for anti-double claiming
    public struct VictoryClaimRecord has store {
        lock_id: u64,
        last_claim_timestamp: u64,
        total_claimed: u64,
        last_claim_amount: u64,
    }
    
    // Initialize the contract
    fun init(ctx: &mut TxContext) {
        // Create and transfer AdminCap to deployer
        transfer::transfer(AdminCap {
            id: object::new(ctx),
        }, tx_context::sender(ctx));
        
        // Create TokenLocker with default allocations (NO victory_balance)
        let locker = TokenLocker {
            id: object::new(ctx),
            
            week_locks: table::new(ctx),
            three_month_locks: table::new(ctx),
            year_locks: table::new(ctx),
            three_year_locks: table::new(ctx),
            
            week_total_locked: 0,
            three_month_total_locked: 0,
            year_total_locked: 0,
            three_year_total_locked: 0,
            total_locked: 0,
            
            // Balance tracking
            total_locked_tokens: 0,
            total_reward_tokens: 0,
            
            // Default Victory allocations (100% = 10000 basis points)
            victory_week_allocation: 200,        // 2%
            victory_three_month_allocation: 800, // 8%
            victory_year_allocation: 2500,       // 25%
            victory_three_year_allocation: 6500, // 65%
            
            // Default SUI allocations (100% = 10000 basis points)
            sui_week_allocation: 1000,           // 10%
            sui_three_month_allocation: 2000,    // 20%
            sui_year_allocation: 3000,           // 30%
            sui_three_year_allocation: 4000,     // 40%
            
            weekly_epochs: table::new(ctx),
            current_epoch_id: 0,
            current_week_start: 0,
            
            user_epoch_claims: table::new(ctx),
            user_victory_claims: table::new(ctx),
            
            next_lock_id: 0,

            // 🔄 UPDATE: Add to existing TokenLocker creation in init()
            victory_week_accumulator: VictoryPoolAccumulator {
                acc_victory_per_share: 0,
                last_reward_time: 0,
                total_staked: 0,
                total_victory_distributed: 0,
                lock_period: WEEK_LOCK,
            },
            victory_three_month_accumulator: VictoryPoolAccumulator {
                acc_victory_per_share: 0,
                last_reward_time: 0,
                total_staked: 0,
                total_victory_distributed: 0,
                lock_period: THREE_MONTH_LOCK,
            },
            victory_year_accumulator: VictoryPoolAccumulator {
                acc_victory_per_share: 0,
                last_reward_time: 0,
                total_staked: 0,
                total_victory_distributed: 0,
                lock_period: YEAR_LOCK,
            },
            victory_three_year_accumulator: VictoryPoolAccumulator {
                acc_victory_per_share: 0,
                last_reward_time: 0,
                total_staked: 0,
                total_victory_distributed: 0,
                lock_period: THREE_YEAR_LOCK,
            },
            victory_user_positions: table::new(ctx),
        };
        
        transfer::share_object(locker);
    }
    
    // === SAFE ARITHMETIC HELPERS ===
    
    /// Safe multiplication using u128 to prevent overflow
    fun safe_mul_div(a: u64, b: u64, c: u64): u64 {
        if (c == 0) return 0;
        if (a == 0 || b == 0) return 0;
        
        let a_u128 = (a as u128);
        let b_u128 = (b as u128);
        let c_u128 = (c as u128);
        
        // Check for multiplication overflow before operation
        let max_u128 = 340282366920938463463374607431768211455u128;
        if (a_u128 > max_u128 / b_u128) {
            // Use alternative calculation to prevent overflow
            let result = (a_u128 / c_u128) * b_u128 + ((a_u128 % c_u128) * b_u128) / c_u128;
            // Cast to u64 and check bounds
            if (result > 18446744073709551615u128) {
                18446744073709551615u64
            } else {
                (result as u64)
            }
        } else {
            let result_u128 = (a_u128 * b_u128) / c_u128;
            if (result_u128 > 18446744073709551615u128) {
                18446744073709551615u64
            } else {
                (result_u128 as u64)
            }
        }
    }
    
    /// Safe percentage calculation using u128
    fun safe_percentage(amount: u64, percentage_bp: u64): u64 {
        safe_mul_div(amount, percentage_bp, BASIS_POINTS)
    }

    /// Safe percentage calculation with overflow checking
    fun safe_percentage_checked(amount: u64, percentage_bp: u64): u64 {
        assert!(percentage_bp <= BASIS_POINTS, E_PERCENTAGE_TOO_LARGE);
        safe_mul_div(amount, percentage_bp, BASIS_POINTS)
    }

    /// Validate emission state safely
    fun validate_emission_state(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): (bool, bool, bool) {
        let (start_timestamp, is_paused) = global_emission_controller::get_config_info(global_config);
        let is_initialized = start_timestamp > 0;
        let is_active = global_emission_controller::is_emissions_active(global_config, clock);
        
        (is_initialized, is_active, is_paused)
    }

    /// Get Victory allocation safely - returns 0 if any emission issue
    fun get_victory_allocation_safe(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): u256 {
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        
        if (!is_initialized || !is_active || is_paused) {
            return 0 // No allocation if any issue
        };
        
        // Safe to call Global Controller now
        global_emission_controller::get_victory_allocation(global_config, clock)
    }

    /// Check if locked vault has sufficient balance for unlock
    fun check_unlock_balance(
        locked_vault: &LockedTokenVault,
        amount: u64
    ): bool {
        let available = balance::value(&locked_vault.locked_balance);
        available >= amount
    }

    /// Validate new lock operations (strict - blocks after emission ends)
    fun validate_new_lock_allowed(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ) {
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        
        assert!(is_initialized, ERROR_EMISSIONS_NOT_INITIALIZED);
        assert!(is_active, ERROR_EMISSIONS_ENDED);        // Block new locks after emission ends
        assert!(!is_paused, ERROR_EMISSIONS_PAUSED);
    }

    /// Validate unlock operations (permissive - allows after emission ends)
    fun validate_unlock_allowed(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ) {
        let (is_initialized, _, is_paused) = validate_emission_state(global_config, clock);
        
        assert!(is_initialized, ERROR_EMISSIONS_NOT_INITIALIZED);
        assert!(!is_paused, ERROR_EMISSIONS_PAUSED);
        // Note: No is_active check - allow unlocking after emission ends
    }

    /// Validate claim operations (permissive - allows after emission ends)
    fun validate_claim_allowed(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ) {
        let (is_initialized, _, is_paused) = validate_emission_state(global_config, clock);
        
        assert!(is_initialized, ERROR_EMISSIONS_NOT_INITIALIZED);
        assert!(!is_paused, ERROR_EMISSIONS_PAUSED);
        // Note: No is_active check - allow claims after emission ends
    }

    // === EPOCH MANAGEMENT SYSTEM ===
    
    /// Get current active epoch ID safely
    /// Returns 0 if no epochs have been created yet
    fun get_current_epoch_id(locker: &TokenLocker): u64 {
        locker.current_epoch_id
    }
    
    /// Check if epoch exists
    fun epoch_exists(locker: &TokenLocker, epoch_id: u64): bool {
        table::contains(&locker.weekly_epochs, epoch_id)
    }
    
    /// Get epoch safely with proper error handling
    fun get_epoch(locker: &TokenLocker, epoch_id: u64): &WeeklyRevenueEpoch {
        assert!(epoch_exists(locker, epoch_id), EEPOCH_NOT_FOUND);
        table::borrow(&locker.weekly_epochs, epoch_id)
    }
    
    /// Get mutable epoch safely with proper error handling
    fun get_epoch_mut(locker: &mut TokenLocker, epoch_id: u64): &mut WeeklyRevenueEpoch {
        assert!(epoch_exists(locker, epoch_id), EEPOCH_NOT_FOUND);
        table::borrow_mut(&mut locker.weekly_epochs, epoch_id)
    }
    
    /// Check if current week needs a new epoch
    fun needs_new_epoch(locker: &TokenLocker, current_time: u64): bool {
        let week_duration = 7 * SECONDS_PER_DAY;
        
        // First epoch case
        if (locker.current_week_start == 0) {
            return true
        };
        
        // Check if current week has expired
        current_time >= locker.current_week_start + week_duration
    }
    
    /// Create new epoch with proper ID management
    fun create_new_epoch(locker: &mut TokenLocker, week_start: u64): u64 {
        let week_duration = 7 * SECONDS_PER_DAY;
        
        // Calculate new epoch ID (don't update state yet)
        let new_epoch_id = locker.current_epoch_id + 1;
        
        // Create epoch object
        let new_epoch = WeeklyRevenueEpoch {
            epoch_id: new_epoch_id,
            week_start_timestamp: week_start,
            week_end_timestamp: week_start + week_duration,
            total_sui_revenue: 0,
            pool_allocations: WeeklyPoolAllocations {
                epoch_id: new_epoch_id,
                week_pool_allocation: 0,
                three_month_pool_allocation: 0,
                year_pool_allocation: 0,
                three_year_pool_allocation: 0,
                week_pool_sui: 0,
                three_month_pool_sui: 0,
                year_pool_sui: 0,
                three_year_pool_sui: 0,
                week_pool_total_staked: 0,
                three_month_pool_total_staked: 0,
                year_pool_total_staked: 0,
                three_year_pool_total_staked: 0,
            },
            week_pool_claimed: 0,
            three_month_pool_claimed: 0,
            year_pool_claimed: 0,
            three_year_pool_claimed: 0,
            is_claimable: false,
            allocations_finalized: false,
        };
        
        // Store epoch (this can fail safely)
        table::add(&mut locker.weekly_epochs, new_epoch_id, new_epoch);
        
        // Only update state AFTER successful storage
        locker.current_epoch_id = new_epoch_id;
        locker.current_week_start = week_start;
        
        new_epoch_id
    }
    
    /// Ensure current week epoch exists with proper error handling
    fun ensure_current_week_epoch(locker: &mut TokenLocker, current_time: u64) {
        let week_duration = 7 * SECONDS_PER_DAY;
        
        // Atomic check and create in single operation
        if (locker.current_week_start == 0) {
            // First epoch - create immediately
            let new_epoch_id = create_new_epoch(locker, current_time);
            
            // Emit epoch creation event for monitoring
            event::emit(EpochCreated {
                epoch_id: new_epoch_id,
                week_start: current_time,
                week_end: current_time + week_duration,
                timestamp: current_time,
            });
            return
        };
        
        // Check if current epoch has expired
        if (current_time >= locker.current_week_start + week_duration) {
            let new_week_start = locker.current_week_start + week_duration;
            let new_epoch_id = create_new_epoch(locker, new_week_start);
            
            // Emit epoch creation event for monitoring
            event::emit(EpochCreated {
                epoch_id: new_epoch_id,
                week_start: new_week_start,
                week_end: new_week_start + week_duration,
                timestamp: current_time,
            });
        };
        // If not expired, do nothing (no race condition possible)
    }
    
    // === VAULT MANAGEMENT FUNCTIONS ===
    
    /// 🔒 Create locked token vault (for users' locked tokens)
    public entry fun create_locked_token_vault(
        _admin: &AdminCap,
        ctx: &mut TxContext
    ) {
        let vault = LockedTokenVault {
            id: object::new(ctx),
            locked_balance: balance::zero<VICTORY_TOKEN>(),
            total_locked_amount: 0,
            total_unlocked_amount: 0,
            lock_count: 0,
            unlock_count: 0,
        };
        transfer::share_object(vault);
    }
    
    /// 🎁 Create Victory reward vault (for admin-funded rewards)
    public entry fun create_victory_reward_vault(
        _admin: &AdminCap,
        ctx: &mut TxContext
    ) {
        let vault = VictoryRewardVault {
            id: object::new(ctx),
            victory_balance: balance::zero<VICTORY_TOKEN>(),
            total_deposited: 0,
            total_distributed: 0,
        };
        transfer::share_object(vault);
    }
    
    /// 💰 Create SUI reward vault
    public entry fun create_sui_reward_vault(
        _admin: &AdminCap,
        ctx: &mut TxContext
    ) {
        let vault = SUIRewardVault {
            id: object::new(ctx),
            sui_balance: balance::zero<SUI>(),
            total_deposited: 0,
            total_distributed: 0,
        };
        transfer::share_object(vault);
    }
    
    /// Deposit Victory tokens into reward vault (for distribution)
    public entry fun deposit_victory_tokens(
        vault: &mut VictoryRewardVault,
        locker: &mut TokenLocker,
        tokens: Coin<VICTORY_TOKEN>,
        _admin: &AdminCap,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let amount = coin::value(&tokens);
        assert!(amount > 0, EZERO_AMOUNT);

        // Check vault balance won't overflow
        let current_balance = balance::value(&vault.victory_balance);
        assert!(current_balance + amount >= current_balance, E_VAULT_BALANCE_OVERFLOW);
        
        // Check tracking won't overflow  
        assert!(vault.total_deposited + amount >= vault.total_deposited, E_VAULT_TRACKING_OVERFLOW);
        
        balance::join(&mut vault.victory_balance, coin::into_balance(tokens));
        vault.total_deposited = vault.total_deposited + amount;
        locker.total_reward_tokens = locker.total_reward_tokens + amount;
        
        event::emit(VaultDeposit {
            vault_type: string::utf8(b"Victory Rewards"),
            amount: (amount as u64),
            total_balance: balance::value(&vault.victory_balance) as u64,
            timestamp: clock::timestamp_ms(clock) / 1000,
        });
    }
    
    /// Distribute Victory rewards from vault
    fun distribute_victory_from_vault(
        vault: &mut VictoryRewardVault,
        amount: u64,
        recipient: address,
        ctx: &mut TxContext
    ) {
        let available = balance::value(&vault.victory_balance);
        assert!(available >= amount, E_INSUFFICIENT_REWARDS);
        
        let reward_balance = balance::split(&mut vault.victory_balance, amount);
        let reward_coin = coin::from_balance(reward_balance, ctx);
        transfer::public_transfer(reward_coin, recipient);
        
        vault.total_distributed = vault.total_distributed + amount;
    }
    
    /// Distribute SUI rewards from vault
    fun distribute_sui_from_vault(
        vault: &mut SUIRewardVault,
        amount: u64,
        recipient: address,
        ctx: &mut TxContext
    ) {
        let available = balance::value(&vault.sui_balance);
        assert!(available >= amount, E_INSUFFICIENT_REWARDS);
        
        let reward_balance = balance::split(&mut vault.sui_balance, amount);
        let reward_coin = coin::from_balance(reward_balance, ctx);
        transfer::public_transfer(reward_coin, recipient);
        
        vault.total_distributed = vault.total_distributed + amount;
    }
    
    // === ADMIN CONFIGURATION FUNCTIONS ===
 
    public entry fun configure_victory_allocations(
        locker: &mut TokenLocker,
        week_allocation: u64,
        three_month_allocation: u64,
        year_allocation: u64,
        three_year_allocation: u64,
        _admin: &AdminCap,
        clock: &Clock
    ) {

        assert!(week_allocation <= BASIS_POINTS, E_ALLOCATION_TOO_LARGE);
        assert!(three_month_allocation <= BASIS_POINTS, E_ALLOCATION_TOO_LARGE);
        assert!(year_allocation <= BASIS_POINTS, E_ALLOCATION_TOO_LARGE);
        assert!(three_year_allocation <= BASIS_POINTS, E_ALLOCATION_TOO_LARGE);
        let total = week_allocation + three_month_allocation + year_allocation + three_year_allocation;
        assert!(total == BASIS_POINTS, EVICTORY_ALLOCATION_NOT_100_PERCENT);

        locker.victory_week_allocation = week_allocation;
        locker.victory_three_month_allocation = three_month_allocation;
        locker.victory_year_allocation = year_allocation;
        locker.victory_three_year_allocation = three_year_allocation;
        
        event::emit(VictoryAllocationsUpdated {
            week_allocation,
            three_month_allocation,
            year_allocation,
            three_year_allocation,
            total_check: total,
            timestamp: clock::timestamp_ms(clock) / 1000,
        });
    }
    
    /// Configure SUI pool allocations (must sum to 100%)
    public entry fun configure_sui_allocations(
        locker: &mut TokenLocker,
        week_allocation: u64,
        three_month_allocation: u64,
        year_allocation: u64,
        three_year_allocation: u64,
        _admin: &AdminCap,
        clock: &Clock
    ) {
        assert!(week_allocation <= BASIS_POINTS, E_ALLOCATION_TOO_LARGE);
        assert!(three_month_allocation <= BASIS_POINTS, E_ALLOCATION_TOO_LARGE);
        assert!(year_allocation <= BASIS_POINTS, E_ALLOCATION_TOO_LARGE);
        assert!(three_year_allocation <= BASIS_POINTS, E_ALLOCATION_TOO_LARGE);
        
        let total = week_allocation + three_month_allocation + year_allocation + three_year_allocation;
        assert!(total == BASIS_POINTS, ESUI_ALLOCATION_NOT_100_PERCENT);
        
        locker.sui_week_allocation = week_allocation;
        locker.sui_three_month_allocation = three_month_allocation;
        locker.sui_year_allocation = year_allocation;
        locker.sui_three_year_allocation = three_year_allocation;
        
        event::emit(SUIAllocationsUpdated {
            week_allocation,
            three_month_allocation,
            year_allocation,
            three_year_allocation,
            total_check: total,
            timestamp: clock::timestamp_ms(clock) / 1000,
        });
    }
    
    /// Add weekly SUI revenue with bulletproof epoch management
    /// Add weekly SUI revenue with bulletproof epoch management
    public entry fun add_weekly_sui_revenue(
        locker: &mut TokenLocker,
        vault: &mut SUIRewardVault,
        sui_tokens: Coin<SUI>,
        _admin: &AdminCap,
        clock: &Clock,
        ctx: &mut TxContext
    ) { 
        let current_time = clock::timestamp_ms(clock) / 1000;
        let sui_amount = coin::value(&sui_tokens);
        
        // ✅ Basic validation (existing)
        assert!(sui_amount > 0, EZERO_AMOUNT);
        
        // ✅ NEW: SUI Epoch Fairness Fix - Only allow funding after week ends
        let current_epoch_id = get_current_epoch_id(locker);
        if (current_epoch_id > 0 && epoch_exists(locker, current_epoch_id)) {
            let current_epoch = get_epoch(locker, current_epoch_id);
            assert!(current_time >= current_epoch.week_end_timestamp, E_WEEK_NOT_FINISHED);
        };
        
        // Check total stake across all pools (existing logic)
        let total_staked = locker.week_total_locked + 
                        locker.three_month_total_locked + 
                        locker.year_total_locked + 
                        locker.three_year_total_locked;
        
        // ✅ UPDATED: Revert instead of defer if insufficient stake
        assert!(total_staked >= MIN_TOTAL_STAKE_FOR_FUNDING, E_INSUFFICIENT_TOTAL_STAKE);
            
        // Ensure current epoch exists (existing logic)
        ensure_current_week_epoch(locker, current_time);
        
        // Get current epoch ID (existing logic)
        let current_epoch_id = get_current_epoch_id(locker);
        
        // ✅ CRITICAL SAFETY CHECK (existing)
        let current_epoch = get_epoch(locker, current_epoch_id);
        assert!(!current_epoch.allocations_finalized, E_EPOCH_ALREADY_FINALIZED);
        assert!(current_epoch.total_sui_revenue == 0, E_REVENUE_ALREADY_ADDED);
        
        // ✅ SAFE ORDER: Store SUI FIRST (most likely to fail)
        balance::join(&mut vault.sui_balance, coin::into_balance(sui_tokens));
        vault.total_deposited = vault.total_deposited + sui_amount;
        
        // Extract allocation values BEFORE getting mutable epoch reference (existing logic)
        let sui_week_allocation = locker.sui_week_allocation;
        let sui_three_month_allocation = locker.sui_three_month_allocation;
        let sui_year_allocation = locker.sui_year_allocation;
        let sui_three_year_allocation = locker.sui_three_year_allocation;
        
        // ✅ VALIDATE ALLOCATIONS SUM TO 100% (existing)
        let total_allocation = sui_week_allocation + sui_three_month_allocation + 
                            sui_year_allocation + sui_three_year_allocation;
        assert!(total_allocation == BASIS_POINTS, E_INVALID_TOTAL_ALLOCATION);
        
        // Extract staking totals BEFORE getting mutable epoch reference (existing logic)
        let week_total_locked = locker.week_total_locked;
        let three_month_total_locked = locker.three_month_total_locked;
        let year_total_locked = locker.year_total_locked;
        let three_year_total_locked = locker.three_year_total_locked;
        
        // Get mutable epoch reference (existing logic)
        let current_epoch = get_epoch_mut(locker, current_epoch_id);
        
        // ✅ DOUBLE-CHECK epoch state (existing safety)
        assert!(!current_epoch.allocations_finalized, E_EPOCH_ALREADY_FINALIZED);
        
        // Add SUI to current week (existing logic)
        current_epoch.total_sui_revenue = current_epoch.total_sui_revenue + sui_amount;
        
        // Calculate pool SUI amounts using safe arithmetic (existing logic with safety)
        let week_sui = safe_percentage_checked(current_epoch.total_sui_revenue, sui_week_allocation);
        let three_month_sui = safe_percentage_checked(current_epoch.total_sui_revenue, sui_three_month_allocation);
        let year_sui = safe_percentage_checked(current_epoch.total_sui_revenue, sui_year_allocation);
        let three_year_sui = current_epoch.total_sui_revenue - week_sui - three_month_sui - year_sui;
        
        // ✅ VALIDATE MATH (existing safety check)
        assert!(
            week_sui + three_month_sui + year_sui + three_year_sui == current_epoch.total_sui_revenue,
            E_ALLOCATION_MATH_ERROR
        );
        
        // Update pool allocations (existing logic)
        current_epoch.pool_allocations = WeeklyPoolAllocations {
            epoch_id: current_epoch_id,
            week_pool_allocation: sui_week_allocation,
            three_month_pool_allocation: sui_three_month_allocation,
            year_pool_allocation: sui_year_allocation,
            three_year_pool_allocation: sui_three_year_allocation,
            week_pool_sui: week_sui,
            three_month_pool_sui: three_month_sui,
            year_pool_sui: year_sui,
            three_year_pool_sui: three_year_sui,
            week_pool_total_staked: week_total_locked,
            three_month_pool_total_staked: three_month_total_locked,
            year_pool_total_staked: year_total_locked,
            three_year_pool_total_staked: three_year_total_locked,
        };
        
        // ✅ ENABLE CLAIMING LAST (after SUI is safely stored)
        current_epoch.allocations_finalized = true;
        current_epoch.is_claimable = true;
        
        // Store final values for event emission (existing logic)
        let final_total_revenue = current_epoch.total_sui_revenue;
        
        // Emit comprehensive event (existing logic)
        event::emit(WeeklyRevenueAdded {
            epoch_id: current_epoch_id,
            amount: sui_amount,
            total_week_revenue: final_total_revenue,
            week_pool_sui: week_sui,
            three_month_pool_sui: three_month_sui,
            year_pool_sui: year_sui,
            three_year_pool_sui: three_year_sui,
            dynamic_allocations_used: true,
            timestamp: current_time,
        });
    }

    // === USER FUNCTIONS ===
    
    /// 🔒 : Lock Victory tokens for specified period (now uses LockedTokenVault)
    public entry fun lock_tokens(
        locker: &mut TokenLocker,
        locked_vault: &mut LockedTokenVault,
        tokens: Coin<VICTORY_TOKEN>,
        lock_period: u64,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
        ) {
        let amount = coin::value(&tokens);
        assert!(amount > 0, EZERO_AMOUNT);
        validate_new_lock_allowed(global_config, clock);
        assert!(amount <= MAX_SINGLE_LOCK_AMOUNT, E_LOCK_AMOUNT_TOO_LARGE);

        let min_required = if (lock_period == WEEK_LOCK) MIN_LOCK_AMOUNT_WEEK
        else if (lock_period == THREE_MONTH_LOCK) MIN_LOCK_AMOUNT_3_MONTH
        else if (lock_period == YEAR_LOCK) MIN_LOCK_AMOUNT_1_YEAR
        else MIN_LOCK_AMOUNT_3_YEAR;

        assert!(amount >= min_required, E_INSUFFICIENT_LOCK_AMOUNT);
        
        assert!(
            lock_period == WEEK_LOCK || 
            lock_period == THREE_MONTH_LOCK || 
            lock_period == YEAR_LOCK || 
            lock_period == THREE_YEAR_LOCK, 
            E_INVALID_LOCK_PERIOD
        );
        
        let current_time = clock::timestamp_ms(clock) / 1000;
        let sender = tx_context::sender(ctx);
        let lock_id = locker.next_lock_id;
        
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        assert!(is_initialized, ERROR_EMISSIONS_NOT_INITIALIZED);
        assert!(is_active, ERROR_EMISSIONS_ENDED);
        assert!(!is_paused, ERROR_EMISSIONS_PAUSED);
        
        let lock_end = calculate_intelligent_lock_end(
            global_config,
            current_time,
            lock_period
        );

        let new_lock = Lock {
            id: lock_id,
            amount,
            lock_period,
            lock_end,
            stake_timestamp: current_time,
            last_victory_claim_timestamp: current_time,
            total_victory_claimed: 0,
            last_sui_epoch_claimed: 0,
            claimed_sui_epochs: vector::empty(),
        };
        
        add_lock_to_pool(locker, sender, new_lock, lock_period, ctx);
        
        update_pool_totals(locker, lock_period, amount, true);
        locker.next_lock_id = locker.next_lock_id + 1;
        locker.total_locked = locker.total_locked + amount;
        
        let token_balance = coin::into_balance(tokens);
        balance::join(&mut locked_vault.locked_balance, token_balance);
        locked_vault.total_locked_amount = locked_vault.total_locked_amount + amount;
        locked_vault.lock_count = locked_vault.lock_count + 1;
        locker.total_locked_tokens = locker.total_locked_tokens + amount;

        
        create_victory_position(
            locker, sender, lock_id, amount, lock_period,
            lock_end, current_time, global_config, clock, ctx
        );

        event::emit(TokensLocked {
            user: sender,
            lock_id,
            amount,
            lock_period,
            lock_end,
        });
        }

    public entry fun claim_victory_rewards(
        locker: &mut TokenLocker,
        vault: &mut VictoryRewardVault,
        global_config: &GlobalEmissionConfig,
        lock_id: u64,
        lock_period: u64,
        clock: &Clock,
        ctx: &mut TxContext
        ) {
        let sender = tx_context::sender(ctx);
        let current_time = clock::timestamp_ms(clock) / 1000;
        validate_claim_allowed(global_config, clock);
        
        let (user_owns_lock, verified_period) = find_user_lock_any_pool_safe(locker, sender, lock_id);
        assert!(user_owns_lock, ELOCK_NOT_FOUND);
        
        let (user_lock, _) = find_user_lock_any_pool(locker, sender, lock_id);
        
        assert!(current_time <= user_lock.lock_end, ELOCK_EXPIRED_CANNOT_CLAIM);
        
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        assert!(is_initialized, ERROR_EMISSIONS_NOT_INITIALIZED);
        assert!(!is_paused, ERROR_EMISSIONS_PAUSED);
        
        assert!(has_victory_position(locker, sender, lock_id), ELOCK_NOT_FOUND);
        
        let (position_lock_period, last_claim_time, is_position_active) = {
            let user_position = get_victory_position(locker, sender, lock_id);
            (user_position.lock_period, user_position.last_victory_claim_time, user_position.is_active)
        };

        assert!(
            current_time >= last_claim_time + MIN_CLAIM_INTERVAL,
            E_CLAIM_TOO_FREQUENT
        );
        
        assert!(is_position_active, ELOCK_NOT_FOUND);
        assert!(position_lock_period == lock_period, ELOCK_NOT_FOUND);
        
        if (is_active) {
            update_victory_accumulator_for_period(locker, lock_period, global_config, clock, current_time);
        };
        
        let victory_rewards = {
            let user_position = get_victory_position(locker, sender, lock_id);
            let accumulator = get_victory_accumulator(locker, lock_period);
            
            calculate_masterchef_pending_victory(
                user_position, accumulator, locker, global_config, clock, current_time
            )
        };
        
        assert!(victory_rewards > 0, ENO_VICTORY_REWARDS);
        
        let acc_victory_per_share = {
            let accumulator = get_victory_accumulator(locker, lock_period);
            accumulator.acc_victory_per_share
        };
        
        {
            let user_position_mut = get_victory_position_mut(locker, sender, lock_id);
            
            user_position_mut.victory_reward_debt = safe_mul_div_u256(
                (user_position_mut.stake_amount as u256),
                acc_victory_per_share,
                PRECISION_FACTOR
            );
            
            user_position_mut.last_victory_claim_time = current_time;
            user_position_mut.total_victory_claimed = user_position_mut.total_victory_claimed + victory_rewards;
        };
        
        update_global_victory_claim_record(locker, sender, lock_id, victory_rewards, current_time, ctx);
        
        distribute_victory_from_vault(vault, victory_rewards, sender, ctx);
        
        let total_claimed_for_lock = {
            let user_position = get_victory_position(locker, sender, lock_id);
            user_position.total_victory_claimed
        };
        
        event::emit(VictoryRewardsClaimed {
            user: sender,
            lock_id,
            amount: victory_rewards,
            timestamp: current_time,
            total_claimed_for_lock,
        });
        }

        

    // === BATCH CLAIM EXECUTION FUNCTIONS ===

    /// Claim ALL eligible epochs for a specific lock (builds on auto_claim_all_eligible_sui_rewards)
    public entry fun claim_all_epochs_for_lock(
        locker: &mut TokenLocker,
        sui_vault: &mut SUIRewardVault,
        lock_id: u64,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        let current_time = clock::timestamp_ms(clock) / 1000;
        
        validate_claim_allowed(global_config, clock);
        // Validate user owns this lock
        let (user_owns_lock, lock_period) = find_user_lock_any_pool_safe(locker, sender, lock_id);
        assert!(user_owns_lock, ELOCK_NOT_FOUND);
        
        // Get preview to validate there's something to claim
        let (can_claim, total_epochs, total_sui, lock_amount, claimable_epochs, claimable_amounts) = 
            preview_claim_for_lock(locker, sender, lock_id, clock);
        assert!(can_claim, ENO_SUI_REWARDS);
        assert!(total_epochs > 0, ENO_SUI_REWARDS);
        
        // Get lock details - copy the lock to avoid borrowing issues
        let (user_lock_ref, _) = find_user_lock_any_pool(locker, sender, lock_id);
        let user_lock_copy = *user_lock_ref; // Copy the lock data
        
        // Claim each eligible epoch individually (reusing existing validation logic)
        let mut total_claimed = 0;
        let mut epochs_claimed = 0;
        let mut i = 0;
        
        while (i < vector::length(&claimable_epochs)) {
            let epoch_id = *vector::borrow(&claimable_epochs, i);
            let expected_amount = *vector::borrow(&claimable_amounts, i);
            
            // Double-check this epoch is still claimable (prevent race conditions)
            if (!has_user_claimed_pool_epoch(locker, sender, epoch_id, lock_id)) {
                let epoch = get_epoch(locker, epoch_id);
                
                // Final validation
                if (epoch.is_claimable && 
                    current_time >= epoch.week_end_timestamp &&
                    user_lock_copy.stake_timestamp < epoch.week_start_timestamp &&
                    user_lock_copy.lock_end >= epoch.week_end_timestamp) {
                    
                    // Calculate SUI rewards
                    let sui_rewards = calculate_pool_based_sui_rewards_with_allocations(
                        &epoch.pool_allocations, 
                        &user_lock_copy, 
                        lock_period
                    );
                    
                    if (sui_rewards > 0) {
                        // Mark as claimed
                        mark_pool_epoch_claimed(
                            locker, sender, epoch_id, lock_id, 
                            lock_period, sui_rewards, current_time, ctx
                        );
                        
                        // Update epoch tracking
                        update_epoch_pool_claimed(locker, epoch_id, lock_period, sui_rewards);
                        
                        // Distribute SUI
                        distribute_sui_from_vault(sui_vault, sui_rewards, sender, ctx);
                        
                        total_claimed = total_claimed + sui_rewards;
                        epochs_claimed = epochs_claimed + 1;
                        
                        // Emit individual claim event
                        event::emit(PoolSUIClaimed {
                            user: sender,
                            epoch_id,
                            lock_id,
                            lock_period,
                            pool_type: get_pool_type_id(lock_period),
                            amount_staked: user_lock_copy.amount,
                            sui_claimed: sui_rewards,
                            timestamp: current_time,
                        });
                    };
                };
            };
            i = i + 1;
        };
        
        // Update lock's claim tracking
        if (epochs_claimed > 0) {
            let user_lock_mut = get_user_lock_mut(locker, sender, lock_id, lock_period);
            if (vector::length(&claimable_epochs) > 0) {
                let last_claimed_epoch = *vector::borrow(&claimable_epochs, vector::length(&claimable_epochs) - 1);
                user_lock_mut.last_sui_epoch_claimed = last_claimed_epoch;
                
                // Add all claimed epochs to tracking
                let mut j = 0;
                while (j < vector::length(&claimable_epochs)) {
                    let epoch_id = *vector::borrow(&claimable_epochs, j);
                    if (!vector::contains(&user_lock_mut.claimed_sui_epochs, &epoch_id)) {
                        vector::push_back(&mut user_lock_mut.claimed_sui_epochs, epoch_id);
                    };
                    j = j + 1;
                };
            };
        };
        
        // Emit batch claim summary
        event::emit(BatchClaimCompleted {
            user: sender,
            lock_id,
            epochs_claimed,
            total_sui_claimed: total_claimed,
            timestamp: current_time,
        });
    }

    /// Claim ALL eligible epochs for ALL user's locks (ultimate batch claim)
    public entry fun claim_all_epochs_for_all_locks(
        locker: &mut TokenLocker,
        sui_vault: &mut SUIRewardVault,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        let current_time = clock::timestamp_ms(clock) / 1000;
        validate_claim_allowed(global_config, clock);
        
        // Get preview to validate there's something to claim
        let (can_claim, locks_with_rewards, total_epochs, total_sui, lock_ids, epoch_counts, sui_amounts) = 
            preview_claim_for_all_user_locks(locker, sender, clock);
        assert!(can_claim, ENO_SUI_REWARDS);
        assert!(locks_with_rewards > 0, ENO_SUI_REWARDS);
        
        let mut total_claimed_all_locks = 0;
        let mut total_epochs_claimed_all_locks = 0;
        let mut locks_processed = 0;
        
        // Process each lock with claimable rewards
        let mut i = 0;
        while (i < vector::length(&lock_ids)) {
            let lock_id = *vector::borrow(&lock_ids, i);
            let expected_epochs = *vector::borrow(&epoch_counts, i);
            let expected_sui = *vector::borrow(&sui_amounts, i);
            
            // Validate user still owns this lock
            let (user_owns_lock, lock_period) = find_user_lock_any_pool_safe(locker, sender, lock_id);
            if (user_owns_lock && expected_sui > 0) {
                // Get claimable epochs for this specific lock
                let (claimable_epochs, claimable_amounts, _) = 
                    get_claimable_epochs_for_lock(locker, sender, lock_id, clock);
                
                if (vector::length(&claimable_epochs) > 0) {
                    let (user_lock_ref, _) = find_user_lock_any_pool(locker, sender, lock_id);
                    let user_lock_copy = *user_lock_ref; // Copy the lock data
                    let mut lock_total_claimed = 0;
                    let mut lock_epochs_claimed = 0;
                    
                    // Claim each epoch for this lock
                    let mut j = 0;
                    while (j < vector::length(&claimable_epochs)) {
                        let epoch_id = *vector::borrow(&claimable_epochs, j);
                        
                        // Final validation before claiming
                        if (!has_user_claimed_pool_epoch(locker, sender, epoch_id, lock_id)) {
                            let epoch = get_epoch(locker, epoch_id);
                            
                            if (epoch.is_claimable && 
                                current_time >= epoch.week_end_timestamp &&
                                user_lock_copy.stake_timestamp < epoch.week_start_timestamp &&
                                user_lock_copy.lock_end >= epoch.week_end_timestamp) {
                                
                                let sui_rewards = calculate_pool_based_sui_rewards_with_allocations(
                                    &epoch.pool_allocations, 
                                    &user_lock_copy, 
                                    lock_period
                                );
                                
                                if (sui_rewards > 0) {
                                    // Mark as claimed
                                    mark_pool_epoch_claimed(
                                        locker, sender, epoch_id, lock_id, 
                                        lock_period, sui_rewards, current_time, ctx
                                    );
                                    
                                    // Update epoch tracking
                                    update_epoch_pool_claimed(locker, epoch_id, lock_period, sui_rewards);
                                    
                                    // Distribute SUI
                                    distribute_sui_from_vault(sui_vault, sui_rewards, sender, ctx);
                                    
                                    lock_total_claimed = lock_total_claimed + sui_rewards;
                                    lock_epochs_claimed = lock_epochs_claimed + 1;
                                    
                                    // Emit individual claim event
                                    event::emit(PoolSUIClaimed {
                                        user: sender,
                                        epoch_id,
                                        lock_id,
                                        lock_period,
                                        pool_type: get_pool_type_id(lock_period),
                                        amount_staked: user_lock_copy.amount,
                                        sui_claimed: sui_rewards,
                                        timestamp: current_time,
                                    });
                                };
                            };
                        };
                        j = j + 1;
                    };
                    
                    // Update lock's claim tracking
                    if (lock_epochs_claimed > 0) {
                        let user_lock_mut = get_user_lock_mut(locker, sender, lock_id, lock_period);
                        if (vector::length(&claimable_epochs) > 0) {
                            let last_claimed_epoch = *vector::borrow(&claimable_epochs, vector::length(&claimable_epochs) - 1);
                            user_lock_mut.last_sui_epoch_claimed = last_claimed_epoch;
                            
                            // Add all claimed epochs to tracking
                            let mut k = 0;
                            while (k < vector::length(&claimable_epochs)) {
                                let epoch_id = *vector::borrow(&claimable_epochs, k);
                                if (!vector::contains(&user_lock_mut.claimed_sui_epochs, &epoch_id)) {
                                    vector::push_back(&mut user_lock_mut.claimed_sui_epochs, epoch_id);
                                };
                                k = k + 1;
                            };
                        };
                        
                        total_claimed_all_locks = total_claimed_all_locks + lock_total_claimed;
                        total_epochs_claimed_all_locks = total_epochs_claimed_all_locks + lock_epochs_claimed;
                        locks_processed = locks_processed + 1;
                    };
                };
            };
            i = i + 1;
        };
        
        // Emit ultimate batch claim summary
        event::emit(UltimateBatchClaimCompleted {
            user: sender,
            locks_processed,
            total_epochs_claimed: total_epochs_claimed_all_locks,
            total_sui_claimed: total_claimed_all_locks,
            timestamp: current_time,
        });
    }

    
    /// 🎯 PRODUCTION-READY: Claim SUI rewards with enhanced validation
    public entry fun claim_pool_sui_rewards(
        locker: &mut TokenLocker,
        vault: &mut SUIRewardVault,
        epoch_id: u64,
        lock_id: u64,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let sender = tx_context::sender(ctx);
        let current_time = clock::timestamp_ms(clock) / 1000;
        validate_claim_allowed(global_config, clock);

        // Validate epoch exists and is claimable
        let epoch = get_epoch(locker, epoch_id);
        assert!(current_time >= epoch.week_end_timestamp, EWEEK_NOT_FINISHED);
        assert!(epoch.is_claimable, ECLAIMING_DISABLED);
        assert!(epoch.allocations_finalized, E_ALLOCATIONS_NOT_FINALIZED);
        
        // Extract epoch data to avoid borrowing conflicts
        let (week_start, week_end, pool_allocations_copy) = {
            (epoch.week_start_timestamp, epoch.week_end_timestamp, epoch.pool_allocations)
        };
        
        // Check user hasn't claimed THIS SPECIFIC (epoch, lock) combination
        assert!(!has_user_claimed_pool_epoch(locker, sender, epoch_id, lock_id), EALREADY_CLAIMED);
        
        // 🔒 SECURITY: Validate user owns this lock before proceeding
        let (user_owns_lock, lock_period) = find_user_lock_any_pool_safe(locker, sender, lock_id);
        assert!(user_owns_lock, ELOCK_NOT_FOUND); // User doesn't own this lock
        
        // Find user's lock details (we know it exists now)
        let (user_lock_copy, verified_period) = {
            let (user_lock, period) = find_user_lock_any_pool(locker, sender, lock_id);
            assert!(period == lock_period, ELOCK_NOT_FOUND); // Double-check consistency
            (*user_lock, period)
        };
        
        // Validate full week staking eligibility
        validate_full_week_staking_with_timestamps(&user_lock_copy, week_start, week_end);
        
        // Calculate SUI rewards using pool allocations
        let sui_rewards = calculate_pool_based_sui_rewards_with_allocations(
            &pool_allocations_copy, 
            &user_lock_copy, 
            lock_period
        );
        assert!(sui_rewards > 0, ENO_SUI_REWARDS);
        
        // Mark SPECIFIC (epoch, lock) combination as claimed
        mark_pool_epoch_claimed(locker, sender, epoch_id, lock_id, lock_period, sui_rewards, current_time, ctx);
        
        // Update lock's claim tracking
        {
            let user_lock_mut = get_user_lock_mut(locker, sender, lock_id, lock_period);
            user_lock_mut.last_sui_epoch_claimed = epoch_id;
            vector::push_back(&mut user_lock_mut.claimed_sui_epochs, epoch_id);
        };
        
        // Update epoch pool claimed tracking
        update_epoch_pool_claimed(locker, epoch_id, lock_period, sui_rewards);
        
        // Distribute SUI (this should be last to prevent reentrancy)
        distribute_sui_from_vault(vault, sui_rewards, sender, ctx);
        
        event::emit(PoolSUIClaimed {
            user: sender,
            epoch_id,
            lock_id,
            lock_period,
            pool_type: get_pool_type_id(lock_period),
            amount_staked: user_lock_copy.amount,
            sui_claimed: sui_rewards,
            timestamp: current_time,
        });
    }
    
    /// 🔒 : Unlock tokens and claim all pending rewards (now uses LockedTokenVault)
    public entry fun unlock_tokens(
        locker: &mut TokenLocker,
        locked_vault: &mut LockedTokenVault,
        victory_vault: &mut VictoryRewardVault,
        sui_vault: &mut SUIRewardVault,
        global_config: &GlobalEmissionConfig,
        lock_id: u64,
        lock_period: u64,
        clock: &Clock,
        ctx: &mut TxContext
        ) {
        let sender = tx_context::sender(ctx);
        let current_time = clock::timestamp_ms(clock) / 1000;

        validate_unlock_allowed(global_config, clock);
        
        let (lock_to_remove, lock_index) = {
            let (lock, index) = find_and_prepare_unlock(locker, sender, lock_id, lock_period, current_time);
            (lock, index)
        };
        
        assert!(check_unlock_balance(locked_vault, lock_to_remove.amount), E_INSUFFICIENT_LOCKED_BALANCE);
        
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        
        let victory_rewards = if (has_victory_position(locker, sender, lock_to_remove.id)) {
            let pending_rewards = calculate_pending_victory_rewards(
                locker,
                sender,
                lock_to_remove.id,
                lock_period,
                global_config,
                clock
            );
            
            if (is_active) {
                update_victory_accumulator_for_period(locker, lock_period, global_config, clock, current_time);
            };
            
            deactivate_victory_position(locker, sender, lock_to_remove.id);
            
            pending_rewards
        } else { 0 };

        let sui_rewards = auto_claim_all_eligible_sui_rewards(
            locker,
            sui_vault,
            sender,
            &lock_to_remove,
            current_time,
            ctx
        );
        
        remove_lock_from_pool(locker, sender, lock_index, lock_period);
        
        update_pool_totals(locker, lock_period, lock_to_remove.amount, false);
        locker.total_locked = locker.total_locked - lock_to_remove.amount;
        locker.total_locked_tokens = locker.total_locked_tokens - lock_to_remove.amount;

        let token_coin = coin::take(&mut locked_vault.locked_balance, lock_to_remove.amount, ctx);
        transfer::public_transfer(token_coin, sender);

        locked_vault.total_unlocked_amount = locked_vault.total_unlocked_amount + lock_to_remove.amount;
        locked_vault.unlock_count = locked_vault.unlock_count + 1;
        locked_vault.total_locked_amount = locked_vault.total_locked_amount - lock_to_remove.amount;


        let actual_victory = if (victory_rewards > 0) {
            let available_victory = balance::value(&victory_vault.victory_balance);
            let actual = if (victory_rewards > available_victory) available_victory else victory_rewards;
            if (actual > 0) {
                distribute_victory_from_vault(victory_vault, actual, sender, ctx);
            };
            actual
        } else {
            0
        };
        
        event::emit(TokensUnlocked {
            user: sender,
            lock_id: lock_to_remove.id,
            amount: lock_to_remove.amount,
            victory_rewards: actual_victory,
            sui_rewards: sui_rewards,
            timestamp: current_time,
        });

        if (sui_rewards > 0) {
            event::emit(SUIAutoClaimSummary {
                user: sender,
                lock_id: lock_to_remove.id,
                total_sui_claimed: sui_rewards,
                timestamp: current_time,
            });
        };
    }
    
    // === HELPER FUNCTIONS ===
    
    /// Add lock to appropriate pool
    fun add_lock_to_pool(
        locker: &mut TokenLocker,
        user: address,
        lock: Lock,
        lock_period: u64,
        ctx: &mut TxContext
    ) {
        let lock_table = if (lock_period == WEEK_LOCK) {
            &mut locker.week_locks
        } else if (lock_period == THREE_MONTH_LOCK) {
            &mut locker.three_month_locks
        } else if (lock_period == YEAR_LOCK) {
            &mut locker.year_locks
        } else {
            &mut locker.three_year_locks
        };
        
        if (!table::contains(lock_table, user)) {
            table::add(lock_table, user, vector::empty());
        };
        
        let user_locks = table::borrow_mut(lock_table, user);
        vector::push_back(user_locks, lock);
    }
    
    /// Update pool totals
    fun update_pool_totals(locker: &mut TokenLocker, lock_period: u64, amount: u64, is_adding: bool) {
        if (lock_period == WEEK_LOCK) {
            if (is_adding) {
                locker.week_total_locked = locker.week_total_locked + amount;
            } else {
                locker.week_total_locked = locker.week_total_locked - amount;
            };
        } else if (lock_period == THREE_MONTH_LOCK) {
            if (is_adding) {
                locker.three_month_total_locked = locker.three_month_total_locked + amount;
            } else {
                locker.three_month_total_locked = locker.three_month_total_locked - amount;
            };
        } else if (lock_period == YEAR_LOCK) {
            if (is_adding) {
                locker.year_total_locked = locker.year_total_locked + amount;
            } else {
                locker.year_total_locked = locker.year_total_locked - amount;
            };
        } else {
            if (is_adding) {
                locker.three_year_total_locked = locker.three_year_total_locked + amount;
            } else {
                locker.three_year_total_locked = locker.three_year_total_locked - amount;
            };
        };
    }
    
    /// Get mutable reference to user's lock
    fun get_user_lock_mut(locker: &mut TokenLocker, user: address, lock_id: u64, lock_period: u64): &mut Lock {
        let lock_table = if (lock_period == WEEK_LOCK) {
            &mut locker.week_locks
        } else if (lock_period == THREE_MONTH_LOCK) {
            &mut locker.three_month_locks
        } else if (lock_period == YEAR_LOCK) {
            &mut locker.year_locks
        } else {
            &mut locker.three_year_locks
        };
        
        assert!(table::contains(lock_table, user), ELOCK_NOT_FOUND);
        let user_locks = table::borrow_mut(lock_table, user);
        
        let mut i = 0;
        let len = vector::length(user_locks);
        while (i < len) {
            let lock = vector::borrow_mut(user_locks, i);
            if (lock.id == lock_id) {
                return lock
            };
            i = i + 1;
        };
        abort ELOCK_NOT_FOUND
    }
    
    /// Find user's lock in any pool
    fun find_user_lock_any_pool(locker: &TokenLocker, user: address, lock_id: u64): (&Lock, u64) {
        let periods = vector[WEEK_LOCK, THREE_MONTH_LOCK, YEAR_LOCK, THREE_YEAR_LOCK];
        
        let mut i = 0;
        while (i < vector::length(&periods)) {
            let period = *vector::borrow(&periods, i);
            let lock_table = if (period == WEEK_LOCK) {
                &locker.week_locks
            } else if (period == THREE_MONTH_LOCK) {
                &locker.three_month_locks
            } else if (period == YEAR_LOCK) {
                &locker.year_locks
            } else {
                &locker.three_year_locks
            };
            
            if (table::contains(lock_table, user)) {
                let user_locks = table::borrow(lock_table, user);
                let mut j = 0;
                let len = vector::length(user_locks);
                while (j < len) {
                    let lock = vector::borrow(user_locks, j);
                    if (lock.id == lock_id) {
                        return (lock, period)
                    };
                    j = j + 1;
                };
            };
            i = i + 1;
        };
        abort ELOCK_NOT_FOUND
    }
    
    /// Get dynamic Victory allocation
    fun get_dynamic_victory_allocation(locker: &TokenLocker, lock_period: u64): u64 {
        if (lock_period == WEEK_LOCK) locker.victory_week_allocation
        else if (lock_period == THREE_MONTH_LOCK) locker.victory_three_month_allocation
        else if (lock_period == YEAR_LOCK) locker.victory_year_allocation
        else locker.victory_three_year_allocation
    }
    
    /// Get dynamic SUI allocation
    fun get_dynamic_sui_allocation(locker: &TokenLocker, lock_period: u64): u64 {
        if (lock_period == WEEK_LOCK) locker.sui_week_allocation
        else if (lock_period == THREE_MONTH_LOCK) locker.sui_three_month_allocation
        else if (lock_period == YEAR_LOCK) locker.sui_year_allocation
        else locker.sui_three_year_allocation
    }
    
    /// Get pool total locked
    fun get_pool_total_locked(locker: &TokenLocker, lock_period: u64): u64 {
        if (lock_period == WEEK_LOCK) locker.week_total_locked
        else if (lock_period == THREE_MONTH_LOCK) locker.three_month_total_locked
        else if (lock_period == YEAR_LOCK) locker.year_total_locked
        else locker.three_year_total_locked
    }
    
    /// Calculate SUI rewards using pool allocations directly -  for overflow protection
    fun calculate_pool_based_sui_rewards_with_allocations(
        allocations: &WeeklyPoolAllocations,
        user_lock: &Lock,
        lock_period: u64
    ): u64 {
        // Get pool SUI amount and total staked for this lock period
        let (pool_sui, pool_total_staked) = if (lock_period == WEEK_LOCK) {
            (allocations.week_pool_sui, allocations.week_pool_total_staked)
        } else if (lock_period == THREE_MONTH_LOCK) {
            (allocations.three_month_pool_sui, allocations.three_month_pool_total_staked)
        } else if (lock_period == YEAR_LOCK) {
            (allocations.year_pool_sui, allocations.year_pool_total_staked)
        } else {
            (allocations.three_year_pool_sui, allocations.three_year_pool_total_staked)
        };
        
        if (pool_total_staked == 0 || pool_sui == 0) {
            return 0
        };
        
        // 🔧 Use safe multiplication helper to prevent overflow
        // User's share = (User Stake / Pool Total) × Pool SUI
        safe_mul_div(user_lock.amount, pool_sui, pool_total_staked)
    }
    
    /// Validate user staked for full week using timestamps
    fun validate_full_week_staking_with_timestamps(user_lock: &Lock, week_start: u64, week_end: u64) {
        // User's lock must still be active after the week ends
        assert!(
            user_lock.lock_end >= week_end,
            ELOCK_EXPIRED_DURING_WEEK
        );
        
        // User must have staked BEFORE the week started (prevents gaming)
        assert!(
            user_lock.stake_timestamp < week_start,
            ESTAKED_DURING_WEEK_NOT_ELIGIBLE
        );
    }
    
    /// Check if user has claimed specific epoch
    fun has_user_claimed_pool_epoch(locker: &TokenLocker, user: address, epoch_id: u64, lock_id: u64): bool {
        if (!table::contains(&locker.user_epoch_claims, user)) {
            return false
        };
        
        let user_claims = table::borrow(&locker.user_epoch_claims, user);
        let claim_key = create_claim_key(epoch_id, lock_id);
        
        table::contains(user_claims, claim_key)  // Now checks specific (epoch, lock) combination
    }
    
    /// Mark epoch as claimed
    fun mark_pool_epoch_claimed(
        locker: &mut TokenLocker,
        user: address,
        epoch_id: u64,
        lock_id: u64,
        lock_period: u64,
        amount: u64,
        timestamp: u64,
        ctx: &mut TxContext
    ) {
        if (!table::contains(&locker.user_epoch_claims, user)) {
            table::add(&mut locker.user_epoch_claims, user, table::new(ctx));
        };
        
        let user_claims = table::borrow_mut(&mut locker.user_epoch_claims, user);
        let claim_key = create_claim_key(epoch_id, lock_id);
        
        // Ensure this specific (epoch, lock) hasn't been claimed
        assert!(!table::contains(user_claims, claim_key), EALREADY_CLAIMED);
        
        let claim_record = PoolClaimRecord {
            epoch_id,
            lock_id,
            lock_period,
            pool_type: get_pool_type_id(lock_period),
            amount_staked: amount,
            sui_claimed: amount,
            claim_timestamp: timestamp,
        };
        
        // Track specific (epoch, lock) combination
        table::add(user_claims, claim_key, claim_record);
    }
    
    /// Update global Victory claim record
    fun update_global_victory_claim_record(
        locker: &mut TokenLocker,
        user: address,
        lock_id: u64,
        amount: u64,
        timestamp: u64,
        ctx: &mut TxContext
    ) {
        if (!table::contains(&locker.user_victory_claims, user)) {
            table::add(&mut locker.user_victory_claims, user, table::new(ctx));
        };
        
        let user_claims = table::borrow_mut(&mut locker.user_victory_claims, user);
        
        if (!table::contains(user_claims, lock_id)) {
            table::add(user_claims, lock_id, VictoryClaimRecord {
                lock_id,
                last_claim_timestamp: timestamp,
                total_claimed: amount,
                last_claim_amount: amount,
            });
        } else {
            let record = table::borrow_mut(user_claims, lock_id);
            record.last_claim_timestamp = timestamp;
            record.total_claimed = record.total_claimed + amount;
            record.last_claim_amount = amount;
        };
    }
    
    /// Update epoch pool claimed amounts
    fun update_epoch_pool_claimed(locker: &mut TokenLocker, epoch_id: u64, lock_period: u64, amount: u64) {
        let epoch = get_epoch_mut(locker, epoch_id);
        
        if (lock_period == WEEK_LOCK) {
            let remaining = epoch.pool_allocations.week_pool_sui - epoch.week_pool_claimed;
            assert!(amount <= remaining, E_INSUFFICIENT_POOL_BALANCE);
            epoch.week_pool_claimed = epoch.week_pool_claimed + amount;
        } else if (lock_period == THREE_MONTH_LOCK) {
            let remaining = epoch.pool_allocations.three_month_pool_sui - epoch.three_month_pool_claimed;
            assert!(amount <= remaining, E_INSUFFICIENT_POOL_BALANCE);
            epoch.three_month_pool_claimed = epoch.three_month_pool_claimed + amount;
        } else if (lock_period == YEAR_LOCK) {
            let remaining = epoch.pool_allocations.year_pool_sui - epoch.year_pool_claimed;
            assert!(amount <= remaining, E_INSUFFICIENT_POOL_BALANCE);
            epoch.year_pool_claimed = epoch.year_pool_claimed + amount;
        } else {
            let remaining = epoch.pool_allocations.three_year_pool_sui - epoch.three_year_pool_claimed;
            assert!(amount <= remaining, E_INSUFFICIENT_POOL_BALANCE);
            epoch.three_year_pool_claimed = epoch.three_year_pool_claimed + amount;
        };
    }
    
    /// Get pool type ID
    fun get_pool_type_id(lock_period: u64): u8 {
        if (lock_period == WEEK_LOCK) 0
        else if (lock_period == THREE_MONTH_LOCK) 1
        else if (lock_period == YEAR_LOCK) 2
        else 3
    }
    
    /// Find and prepare for unlock
    fun find_and_prepare_unlock(
        locker: &TokenLocker,
        user: address,
        lock_id: u64,
        lock_period: u64,
        current_time: u64
    ): (Lock, u64) {
        let lock_table = if (lock_period == WEEK_LOCK) {
            &locker.week_locks
        } else if (lock_period == THREE_MONTH_LOCK) {
            &locker.three_month_locks
        } else if (lock_period == YEAR_LOCK) {
            &locker.year_locks
        } else {
            &locker.three_year_locks
        };
        
        assert!(table::contains(lock_table, user), ELOCK_NOT_FOUND);
        let user_locks = table::borrow(lock_table, user);
        
        let mut i = 0;
        let len = vector::length(user_locks);
        while (i < len) {
            let lock = vector::borrow(user_locks, i);
            if (lock.id == lock_id) {
                assert!(current_time >= lock.lock_end, ELOCK_NOT_EXPIRED);
                return (*lock, i)
            };
            i = i + 1;
        };
        abort ELOCK_NOT_FOUND
    }
    
    /// Remove lock from pool
    fun remove_lock_from_pool(locker: &mut TokenLocker, user: address, lock_index: u64, lock_period: u64) {
        let lock_table = if (lock_period == WEEK_LOCK) {
            &mut locker.week_locks
        } else if (lock_period == THREE_MONTH_LOCK) {
            &mut locker.three_month_locks
        } else if (lock_period == YEAR_LOCK) {
            &mut locker.year_locks
        } else {
            &mut locker.three_year_locks
        };
        
        let user_locks = table::borrow_mut(lock_table, user);
        vector::remove(user_locks, lock_index);
        
        if (vector::is_empty(user_locks)) {
            table::remove(lock_table, user);
        };
    }
    
    // === VIEW FUNCTIONS ===
    
    /// Get current Victory allocations
    public fun get_victory_allocations(locker: &TokenLocker): (u64, u64, u64, u64, u64) {
        let total = locker.victory_week_allocation + 
                    locker.victory_three_month_allocation + 
                    locker.victory_year_allocation + 
                    locker.victory_three_year_allocation;
                    
        (
            locker.victory_week_allocation,
            locker.victory_three_month_allocation,
            locker.victory_year_allocation,
            locker.victory_three_year_allocation,
            total
        )
    }
    
    /// Get current SUI allocations
    public fun get_sui_allocations(locker: &TokenLocker): (u64, u64, u64, u64, u64) {
        let total = locker.sui_week_allocation + 
                    locker.sui_three_month_allocation + 
                    locker.sui_year_allocation + 
                    locker.sui_three_year_allocation;
                    
        (
            locker.sui_week_allocation,
            locker.sui_three_month_allocation,
            locker.sui_year_allocation,
            locker.sui_three_year_allocation,
            total
        )
    }
    
    /// Get pool statistics
    public fun get_pool_statistics(locker: &TokenLocker): (u64, u64, u64, u64, u64) {
        (
            locker.week_total_locked,
            locker.three_month_total_locked,
            locker.year_total_locked,
            locker.three_year_total_locked,
            locker.total_locked
        )
    }

    /// 🔒: Get locked vault statistics
    public fun get_locked_vault_statistics(
        locked_vault: &LockedTokenVault
    ): (u64, u64, u64, u64, u64) {
        (
            balance::value(&locked_vault.locked_balance),
            locked_vault.total_locked_amount,
            locked_vault.total_unlocked_amount,
            locked_vault.lock_count,
            locked_vault.unlock_count
        )
    }

    /// 🎁 Get reward vault statistics  
    public fun get_reward_vault_statistics(
        reward_vault: &VictoryRewardVault
    ): (u64, u64, u64) {
        (
            balance::value(&reward_vault.victory_balance),
            reward_vault.total_deposited,
            reward_vault.total_distributed
        )
    }

    /// 💰 Get SUI vault statistics
    public fun get_sui_vault_statistics(
        sui_vault: &SUIRewardVault
    ): (u64, u64, u64) {
        (
            balance::value(&sui_vault.sui_balance),
            sui_vault.total_deposited,
            sui_vault.total_distributed
        )
    }

    /// 🔍 Get comprehensive balance overview
    public fun get_balance_overview(
        locker: &TokenLocker,
        locked_vault: &LockedTokenVault,
        reward_vault: &VictoryRewardVault,
        sui_vault: &SUIRewardVault
    ): (u64, u64, u64, u64, u64, u64, u64, u64) {
        (
            // Locked tokens
            balance::value(&locked_vault.locked_balance),
            locker.total_locked,
            
            // Reward tokens
            balance::value(&reward_vault.victory_balance),
            locker.total_reward_tokens,
            
            // SUI
            balance::value(&sui_vault.sui_balance),
            sui_vault.total_deposited,
            
            // Tracking totals
            locked_vault.total_locked_amount,
            locked_vault.total_unlocked_amount
        )
    }
    
    /// Get user's locks for specific period
    public fun get_user_locks_for_period(locker: &TokenLocker, user: address, lock_period: u64): vector<Lock> {
        let lock_table = if (lock_period == WEEK_LOCK) {
            &locker.week_locks
        } else if (lock_period == THREE_MONTH_LOCK) {
            &locker.three_month_locks
        } else if (lock_period == YEAR_LOCK) {
            &locker.year_locks
        } else {
            &locker.three_year_locks
        };
        
        if (!table::contains(lock_table, user)) {
            vector::empty()
        } else {
            *table::borrow(lock_table, user)
        }
    }
    
    /// Get weekly epoch info -
    public fun get_weekly_epoch_info(locker: &TokenLocker, epoch_id: u64): (u64, u64, u64, u64, u64, bool) {
        if (!epoch_exists(locker, epoch_id)) {
            return (0, 0, 0, 0, 0, false)
        };
        
        let epoch = get_epoch(locker, epoch_id);
        (
            epoch.total_sui_revenue,
            epoch.week_start_timestamp,
            epoch.week_end_timestamp,
            epoch.pool_allocations.week_pool_sui + epoch.pool_allocations.three_month_pool_sui + 
            epoch.pool_allocations.year_pool_sui + epoch.pool_allocations.three_year_pool_sui,
            epoch.week_pool_claimed + epoch.three_month_pool_claimed + 
            epoch.year_pool_claimed + epoch.three_year_pool_claimed,
            epoch.is_claimable
        )
    }
    
    /// Validate all allocations
    public fun validate_all_allocations(locker: &TokenLocker): (bool, bool, String) {
        let victory_total = locker.victory_week_allocation + 
                           locker.victory_three_month_allocation + 
                           locker.victory_year_allocation + 
                           locker.victory_three_year_allocation;
                           
        let sui_total = locker.sui_week_allocation + 
                       locker.sui_three_month_allocation + 
                       locker.sui_year_allocation + 
                       locker.sui_three_year_allocation;
        
        let victory_valid = victory_total == BASIS_POINTS;
        let sui_valid = sui_total == BASIS_POINTS;
        
        let status = if (victory_valid && sui_valid) {
            string::utf8(b"All allocations valid (100% each)")
        } else if (!victory_valid && !sui_valid) {
            string::utf8(b"Both Victory and SUI allocations invalid")
        } else if (!victory_valid) {
            string::utf8(b"Victory allocations invalid")
        } else {
            string::utf8(b"SUI allocations invalid")
        };
        
        (victory_valid, sui_valid, status)
    }

    // === EMISSION-RELATED VIEW FUNCTIONS ===
    
    /// Get emission status for Victory locker
    public fun get_emission_status_for_locker(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): (bool, bool, bool, u64, u8) {
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        let (current_week, phase, _, _, _) = global_emission_controller::get_emission_status(global_config, clock);
        
        (is_initialized, is_active, is_paused, current_week, phase)
    }
    
    /// Get Victory allocation with status
    public fun get_victory_allocation_with_status(
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): (u256, bool, String) {
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        let victory_allocation = get_victory_allocation_safe(global_config, clock);
        
        let status = if (!is_initialized) {
            string::utf8(b"Not initialized")
        } else if (!is_active) {
            string::utf8(b"Ended")
        } else if (is_paused) {
            string::utf8(b"Paused")
        } else {
            string::utf8(b"Active")
        };
        
        let active = is_initialized && is_active && !is_paused;
        
        (victory_allocation, active, status)
    }


    public fun calculate_pending_victory_rewards(
        locker: &TokenLocker,
        user: address,
        lock_id: u64,
        lock_period: u64,
        global_config: &GlobalEmissionConfig,
        clock: &Clock
    ): u64 {
        let current_time = clock::timestamp_ms(clock) / 1000;
        
        if (!has_victory_position(locker, user, lock_id)) {
            return 0
        };
        
        let user_position = get_victory_position(locker, user, lock_id);
        let accumulator = get_victory_accumulator(locker, lock_period);
        
        // ✅ CRITICAL: Cap the simulation time to lock end
        let effective_calculation_time = if (current_time > user_position.lock_end_time) {
            user_position.lock_end_time
        } else {
            current_time
        };
        
        // Use capped time for simulation
        let current_acc_per_share = simulate_accumulator_update(
            accumulator, locker, global_config, clock, effective_calculation_time
        );
        
        // Calculate gross rewards using capped accumulator state
        let gross_rewards = safe_mul_div_u256(
            (user_position.stake_amount as u256), 
            current_acc_per_share, 
            PRECISION_FACTOR
        );
        
        if (gross_rewards <= user_position.victory_reward_debt) return 0;
        let pending_u256 = gross_rewards - user_position.victory_reward_debt;
        
        // Convert to u64 safely
        if (pending_u256 > (18446744073709551615 as u256)) {
            18446744073709551615
        } else {
            (pending_u256 as u64)
        }
    }

    /// Get user's total staked across all lock periods
    public fun get_user_total_staked(locker: &TokenLocker, user: address): (u64, u64, u64, u64, u64) {
        let week_amount = get_user_period_total(locker, user, WEEK_LOCK);
        let three_month_amount = get_user_period_total(locker, user, THREE_MONTH_LOCK);
        let year_amount = get_user_period_total(locker, user, YEAR_LOCK);
        let three_year_amount = get_user_period_total(locker, user, THREE_YEAR_LOCK);
        let total = week_amount + three_month_amount + year_amount + three_year_amount;
        
        (week_amount, three_month_amount, year_amount, three_year_amount, total)
    }

    /// Helper function to get user's total for a specific period
    fun get_user_period_total(locker: &TokenLocker, user: address, lock_period: u64): u64 {
        let lock_table = if (lock_period == WEEK_LOCK) {
            &locker.week_locks
        } else if (lock_period == THREE_MONTH_LOCK) {
            &locker.three_month_locks
        } else if (lock_period == YEAR_LOCK) {
            &locker.year_locks
        } else {
            &locker.three_year_locks
        };
        
        if (!table::contains(lock_table, user)) {
            return 0
        };
        
        let user_locks = table::borrow(lock_table, user);
        let mut total = 0;
        let mut i = 0;
        let len = vector::length(user_locks);
        
        while (i < len) {
            let lock = vector::borrow(user_locks, i);
            total = total + lock.amount;
            i = i + 1;
        };
        
        total
    }

    /// Get all user locks across all periods
    public fun get_all_user_locks(locker: &TokenLocker, user: address): vector<Lock> {
        let mut all_locks = vector::empty<Lock>();
        
        // Add locks from each period
        let periods = vector[WEEK_LOCK, THREE_MONTH_LOCK, YEAR_LOCK, THREE_YEAR_LOCK];
        let mut i = 0;
        
        while (i < vector::length(&periods)) {
            let period = *vector::borrow(&periods, i);
            let period_locks = get_user_locks_for_period(locker, user, period);
            
            let mut j = 0;
            while (j < vector::length(&period_locks)) {
                vector::push_back(&mut all_locks, *vector::borrow(&period_locks, j));
                j = j + 1;
            };
            i = i + 1;
        };
        
        all_locks
    }

    // === PRODUCTION VIEW FUNCTIONS ===
    
    /// Get current epoch information safely
    public fun get_current_epoch_info(locker: &TokenLocker): (u64, u64, u64, bool, bool) {
        let current_epoch_id = get_current_epoch_id(locker);
        
        if (current_epoch_id == 0 || !epoch_exists(locker, current_epoch_id)) {
            return (0, 0, 0, false, false) // No current epoch
        };
        
        let epoch = get_epoch(locker, current_epoch_id);
        (
            current_epoch_id,
            epoch.week_start_timestamp,
            epoch.week_end_timestamp,
            epoch.is_claimable,
            epoch.allocations_finalized
        )
    }
    
    /// Get epoch information safely
    public fun get_epoch_info_safe(locker: &TokenLocker, epoch_id: u64): (u64, u64, u64, u64, u64, bool) {
        if (!epoch_exists(locker, epoch_id)) {
            return (0, 0, 0, 0, 0, false)
        };
        
        let epoch = get_epoch(locker, epoch_id);
        (
            epoch.total_sui_revenue,
            epoch.week_start_timestamp,
            epoch.week_end_timestamp,
            epoch.pool_allocations.week_pool_sui + epoch.pool_allocations.three_month_pool_sui + 
            epoch.pool_allocations.year_pool_sui + epoch.pool_allocations.three_year_pool_sui,
            epoch.week_pool_claimed + epoch.three_month_pool_claimed + 
            epoch.year_pool_claimed + epoch.three_year_pool_claimed,
            epoch.is_claimable
        )
    }
    
    /// Check if user can claim specific epoch
    public fun can_user_claim_epoch(
        locker: &TokenLocker, 
        user: address, 
        epoch_id: u64, 
        lock_id: u64,
        clock: &Clock
    ): (bool, String) {
        let current_time = clock::timestamp_ms(clock) / 1000;
        
        // Check epoch exists
        if (!epoch_exists(locker, epoch_id)) {
            return (false, string::utf8(b"Epoch does not exist"))
        };
        
        let epoch = get_epoch(locker, epoch_id);
        
        // Check epoch is finished
        if (current_time < epoch.week_end_timestamp) {
            return (false, string::utf8(b"Week not finished"))
        };
        
        // Check epoch is claimable
        if (!epoch.is_claimable) {
            return (false, string::utf8(b"Claiming disabled"))
        };
        
        // 🔒 SECURITY Check user owns this lock_id
        let (user_owns_lock, _) = find_user_lock_any_pool_safe(locker, user, lock_id);
        if (!user_owns_lock) {
            return (false, string::utf8(b"User does not own this lock"))
        };
        
        // Check user hasn't already claimed THIS SPECIFIC (epoch, lock) combination
        if (has_user_claimed_pool_epoch(locker, user, epoch_id, lock_id)) {
            return (false, string::utf8(b"Already claimed"))
        };
        
        (true, string::utf8(b"Can claim"))
    }

    /// Get specific lock details for testing and verification
    /// Returns (amount, lock_period, lock_id, lock_end, stake_timestamp)
    public fun get_user_lock_details(
        locker: &TokenLocker, 
        user: address, 
        lock_period: u64,
        lock_index: u64
    ): (u64, u64, u64, u64, u64) {
        let lock_table = if (lock_period == WEEK_LOCK) {
            &locker.week_locks
        } else if (lock_period == THREE_MONTH_LOCK) {
            &locker.three_month_locks
        } else if (lock_period == YEAR_LOCK) {
            &locker.year_locks
        } else {
            &locker.three_year_locks
        };
        
        if (!table::contains(lock_table, user)) {
            return (0, 0, 0, 0, 0)
        };
        
        let user_locks = table::borrow(lock_table, user);
        if (lock_index >= vector::length(user_locks)) {
            return (0, 0, 0, 0, 0)
        };
        
        let lock = vector::borrow(user_locks, lock_index);
        (
            lock.amount,
            lock.lock_period,
            lock.id,
            lock.lock_end,
            lock.stake_timestamp
        )
    }
        

    /// 🎯 ADMIN PRESALE LOCK HANDLER - Create locks for presale participants
    /// This function allows admins to automatically lock Victory tokens for users
    /// who participated in the presale, fulfilling the 100-day lock promise.
    public entry fun admin_create_user_lock(
        locker: &mut TokenLocker,
        locked_vault: &mut LockedTokenVault,
        tokens: Coin<VICTORY_TOKEN>,
        user_address: address,
        lock_period: u64,
        global_config: &GlobalEmissionConfig,
        _admin: &AdminCap,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let amount = coin::value(&tokens);
        assert!(amount > 0, EZERO_AMOUNT);
        
        // Validate user address
        assert!(user_address != @0x0, E_ZERO_ADDRESS);
        
        // Validate lock period
        assert!(
            lock_period == WEEK_LOCK || 
            lock_period == THREE_MONTH_LOCK || 
            lock_period == YEAR_LOCK || 
            lock_period == THREE_YEAR_LOCK, 
            E_INVALID_LOCK_PERIOD
        );
        
        let current_time = clock::timestamp_ms(clock) / 1000;
        let lock_id = locker.next_lock_id;
        
        // 🔥 EMISSION CHECK (warn if no rewards but still allow locking for presale)
        let (is_initialized, is_active, is_paused) = validate_emission_state(global_config, clock);
        if (!is_initialized || !is_active || is_paused) {
            let warning_msg = if (!is_initialized) {
                string::utf8(b"Admin locking for presale - emissions not started yet")
            } else if (!is_active) {
                string::utf8(b"Admin locking for presale - emissions ended")  
            } else {
                string::utf8(b"Admin locking for presale - emissions paused")
            };
            
            event::emit(EmissionWarning {
                message: warning_msg,
                lock_id: option::some(lock_id),
                timestamp: current_time,
            });
        };
        
        let lock_end = calculate_intelligent_lock_end(
            global_config,  // Pass controller
            current_time,
            lock_period
        );
        
        let new_lock = Lock {
            id: lock_id,
            amount,
            lock_period,
            lock_end,
            stake_timestamp: current_time,
            last_victory_claim_timestamp: current_time,
            total_victory_claimed: 0,
            last_sui_epoch_claimed: 0,
            claimed_sui_epochs: vector::empty(),
        };
        
        // Add to appropriate lock table for the specified user
        add_lock_to_pool(locker, user_address, new_lock, lock_period, ctx);
        
        // Update totals
        update_pool_totals(locker, lock_period, amount, true);
        locker.next_lock_id = locker.next_lock_id + 1;
        locker.total_locked = locker.total_locked + amount;
        
        // Store tokens in LockedTokenVault
        let token_balance = coin::into_balance(tokens);
        balance::join(&mut locked_vault.locked_balance, token_balance);
        locked_vault.total_locked_amount = locked_vault.total_locked_amount + amount;
        locked_vault.lock_count = locked_vault.lock_count + 1;
        locker.total_locked_tokens = locker.total_locked_tokens + amount;


        // 🔄 ADD at end of existing admin_create_user_lock():
        // **NEW**: Create Victory position for admin-created lock
        create_victory_position(
            locker, user_address, lock_id, amount, lock_period,
            lock_end, current_time, global_config, clock, ctx
        );
        
        // Emit special event for admin-created locks
        event::emit(AdminPresaleLockCreated {
            admin: tx_context::sender(ctx),
            user: user_address,
            lock_id,
            amount,
            lock_period,
            lock_end,
            timestamp: current_time,
        });
        
        // Also emit standard lock event for consistency
        event::emit(TokensLocked {
            user: user_address,
            lock_id,
            amount,
            lock_period,
            lock_end,
        });
    }

    // 🎯 BATCH PROCESSING: Create multiple locks at once for efficiency
    public entry fun admin_batch_create_user_locks(
        locker: &mut TokenLocker,
        locked_vault: &mut LockedTokenVault,
        mut tokens: Coin<VICTORY_TOKEN>,  // 🔧 : Added mut keyword
        user_addresses: vector<address>,
        amounts: vector<u64>,
        lock_periods: vector<u64>,
        global_config: &GlobalEmissionConfig,
        _admin: &AdminCap,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let total_amount = coin::value(&tokens);
        assert!(total_amount > 0, EZERO_AMOUNT);
        
        let users_len = vector::length(&user_addresses);
        let amounts_len = vector::length(&amounts);
        let periods_len = vector::length(&lock_periods);
        
        // Validate all vectors have same length
        assert!(users_len == amounts_len && amounts_len == periods_len, E_INVALID_BATCH_DATA);
        assert!(users_len > 0, E_INVALID_BATCH_DATA);
        
        // Calculate total required amount
        let mut total_required = 0;
        let mut i = 0;
        while (i < amounts_len) {
            total_required = total_required + *vector::borrow(&amounts, i);
            i = i + 1;
        };
        
        assert!(total_amount >= total_required, E_INSUFFICIENT_TOKEN_BALANCE);
        
        // Process each lock
        let mut processed = 0;
        while (processed < users_len) {
            let user = *vector::borrow(&user_addresses, processed);
            let amount = *vector::borrow(&amounts, processed);
            let lock_period = *vector::borrow(&lock_periods, processed);
            
            // Split the required amount for this lock
            let lock_tokens = coin::split(&mut tokens, amount, ctx);
            
            // Create the lock (reuse single lock logic)
            admin_create_single_lock_internal(
                locker,
                locked_vault,
                lock_tokens,
                user,
                lock_period,
                global_config,
                clock,
                ctx
            );
            
            processed = processed + 1;
        };
        
        // Return any remaining tokens to admin
        if (coin::value(&tokens) > 0) {
            transfer::public_transfer(tokens, tx_context::sender(ctx));
        } else {
            coin::destroy_zero(tokens);
        };
    }

    // 🎯 INTERNAL HELPER: Single lock creation for batch processing
    fun admin_create_single_lock_internal(
        locker: &mut TokenLocker,
        locked_vault: &mut LockedTokenVault,
        tokens: Coin<VICTORY_TOKEN>,
        user_address: address,
        lock_period: u64,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        let amount = coin::value(&tokens);
        assert!(amount > 0, EZERO_AMOUNT);
        assert!(user_address != @0x0, E_ZERO_ADDRESS);
        
        // Validate lock period
        assert!(
            lock_period == WEEK_LOCK || 
            lock_period == THREE_MONTH_LOCK || 
            lock_period == YEAR_LOCK || 
            lock_period == THREE_YEAR_LOCK, 
            E_INVALID_LOCK_PERIOD
        );
        
        let current_time = clock::timestamp_ms(clock) / 1000;
        let lock_id = locker.next_lock_id;
        let lock_end = calculate_intelligent_lock_end(
            global_config,  // Pass controller
            current_time,
            lock_period
        );

        let new_lock = Lock {
            id: lock_id,
            amount,
            lock_period,
            lock_end,
            stake_timestamp: current_time,
            last_victory_claim_timestamp: current_time,
            total_victory_claimed: 0,
            last_sui_epoch_claimed: 0,
            claimed_sui_epochs: vector::empty(),
        };
        
        // Add to appropriate lock table
        add_lock_to_pool(locker, user_address, new_lock, lock_period, ctx);
        
        // Update totals
        update_pool_totals(locker, lock_period, amount, true);
        locker.next_lock_id = locker.next_lock_id + 1;
        locker.total_locked = locker.total_locked + amount;
        
        // Store tokens in vault
        let token_balance = coin::into_balance(tokens);
        balance::join(&mut locked_vault.locked_balance, token_balance);
        locked_vault.total_locked_amount = locked_vault.total_locked_amount + amount;
        locked_vault.lock_count = locked_vault.lock_count + 1;
        locker.total_locked_tokens = locker.total_locked_tokens + amount;


        // 🔄 ADD at end of existing admin_create_single_lock_internal():
        // **NEW**: Create Victory position
        create_victory_position(
            locker, user_address, lock_id, amount, lock_period,
            lock_end, current_time, global_config, clock, ctx
        );
                
        // Emit events
        event::emit(AdminPresaleLockCreated {
            admin: tx_context::sender(ctx),
            user: user_address,
            lock_id,
            amount,
            lock_period,
            lock_end,
            timestamp: current_time,
        });
        
        event::emit(TokensLocked {
            user: user_address,
            lock_id,
            amount,
            lock_period,
            lock_end,
        });
    }

    /// Calculate the first eligible epoch for a lock based on stake timestamp
    fun calculate_first_eligible_epoch(
        locker: &TokenLocker,
        lock: &Lock
    ): u64 {
        let mut epoch_id = 1;
        let max_epochs_to_check = 200; // Match production limit
        let mut checked = 0;
        
        // Find the first epoch that started AFTER this lock was staked
        while (checked < max_epochs_to_check && epoch_exists(locker, epoch_id)) {
            let epoch = get_epoch(locker, epoch_id);
            
            // Lock must be staked BEFORE epoch started to be eligible
            if (lock.stake_timestamp < epoch.week_start_timestamp) {
                return epoch_id // First eligible epoch found
            };
            
            epoch_id = epoch_id + 1;
            checked = checked + 1;
        };
        
        // If no eligible epoch found, return max value (no eligibility)
        999999
    }

    /// PRODUCTION READY: Auto-claim all eligible SUI rewards with comprehensive edge case handling
    fun auto_claim_all_eligible_sui_rewards(
        locker: &mut TokenLocker,
        sui_vault: &mut SUIRewardVault,
        user: address,
        lock_to_remove: &Lock,
        current_time: u64,
        ctx: &mut TxContext
    ): u64 {
        let mut total_sui_claimed = 0;
        
        // Get current epoch ID (upper bound)
        let current_epoch_id = get_current_epoch_id(locker);
        if (current_epoch_id == 0) {
            return 0 // No epochs created yet
        };
        
        // Calculate first eligible epoch based on stake time
        let first_eligible_epoch = calculate_first_eligible_epoch(locker, lock_to_remove);
        
        // Find actual starting point (max of first eligible and last claimed + 1)
        let start_epoch = {
            let after_last_claimed = if (lock_to_remove.last_sui_epoch_claimed == 0) {
                1
            } else {
                lock_to_remove.last_sui_epoch_claimed + 1
            };
            
            // Start from whichever is later: first eligible or after last claimed
            if (first_eligible_epoch > after_last_claimed) {
                first_eligible_epoch
            } else {
                after_last_claimed
            }
        };
        
        // Early exit if nothing to process
        if (start_epoch > current_epoch_id || start_epoch == 999999) {
            return 0 // No eligible epochs or all already claimed
        };
        
        // Gas limit protection - max 200 epochs
        let max_epochs_to_process = 200;
        let mut epoch_id = start_epoch;
        let mut processed = 0;
        
        while (epoch_id <= current_epoch_id && processed < max_epochs_to_process) {
            
            if (epoch_exists(locker, epoch_id)) {
                let epoch = get_epoch(locker, epoch_id);
                
                // Comprehensive eligibility check
                if (is_lock_eligible_for_epoch(lock_to_remove, epoch, current_time)) {
                    
                    // Calculate SUI rewards for this epoch
                    let sui_rewards = calculate_pool_based_sui_rewards_with_allocations(
                        &epoch.pool_allocations, 
                        lock_to_remove, 
                        lock_to_remove.lock_period
                    );
                    
                    if (sui_rewards > 0) {
                        // Distribute SUI rewards
                        distribute_sui_from_vault(sui_vault, sui_rewards, user, ctx);
                        
                        // Mark as claimed for this specific lock
                        mark_pool_epoch_claimed(
                            locker, user, epoch_id, lock_to_remove.id, 
                            lock_to_remove.lock_period, sui_rewards, current_time, ctx
                        );
                        
                        total_sui_claimed = total_sui_claimed + sui_rewards;
                        
                        // Emit event for transparency
                        event::emit(PoolSUIClaimed {
                            user,
                            epoch_id,
                            lock_id: lock_to_remove.id,
                            lock_period: lock_to_remove.lock_period,
                            pool_type: get_pool_type_id(lock_to_remove.lock_period),
                            amount_staked: lock_to_remove.amount,
                            sui_claimed: sui_rewards,
                            timestamp: current_time,
                        });
                    };
                };
            };
            
            epoch_id = epoch_id + 1;
            processed = processed + 1;
        };
        
        total_sui_claimed
    }

    /// Comprehensive eligibility check covering all edge cases
    fun is_lock_eligible_for_epoch(
        lock: &Lock, 
        epoch: &WeeklyRevenueEpoch, 
        current_time: u64
    ): bool {
        // Epoch must be claimable
        epoch.is_claimable &&
        // Current time must be past epoch end
        current_time >= epoch.week_end_timestamp &&
        // Lock must have been staked BEFORE epoch started (prevents gaming)
        lock.stake_timestamp < epoch.week_start_timestamp &&
        // Lock must still be active through entire epoch
        lock.lock_end >= epoch.week_end_timestamp
    }

    // 🎯 PRESALE HELPER: Get recommended lock period for presale (100 days ≈ 3 months)
    public fun get_presale_recommended_lock_period(): u64 {
        THREE_MONTH_LOCK // 90 days - closest to 100 days promised
    }

    // 🎯 VIEW FUNCTION: Check if user has any locks (for presale verification)
    public fun user_has_locks(locker: &TokenLocker, user: address): bool {
        let periods = vector[WEEK_LOCK, THREE_MONTH_LOCK, YEAR_LOCK, THREE_YEAR_LOCK];
        
        let mut i = 0;
        while (i < vector::length(&periods)) {
            let period = *vector::borrow(&periods, i);
            let lock_table = if (period == WEEK_LOCK) {
                &locker.week_locks
            } else if (period == THREE_MONTH_LOCK) {
                &locker.three_month_locks
            } else if (period == YEAR_LOCK) {
                &locker.year_locks
            } else {
                &locker.three_year_locks
            };
            
            if (table::contains(lock_table, user)) {
                let user_locks = table::borrow(lock_table, user);
                if (!vector::is_empty(user_locks)) {
                    return true
                };
            };
            i = i + 1;
        };
        
        false
    }

    /// Calculate emission end timestamp
    fun calculate_emission_end_timestamp(global_config: &GlobalEmissionConfig): u64 {
        let (emission_start, _) = global_emission_controller::get_config_info(global_config);
        emission_start + (156 * 7 * SECONDS_PER_DAY)
    }

    /// Calculate intelligent lock_end (cap at emission end)  
    fun calculate_intelligent_lock_end(
        global_config: &GlobalEmissionConfig,  // ✅ Pass controller instead
        current_time: u64, 
        lock_period: u64
    ): u64 {
        let normal_lock_end = current_time + (lock_period * SECONDS_PER_DAY);
        
        // ✅ Get launch time from controller
        let (emission_start_timestamp, _) = global_emission_controller::get_config_info(global_config);
        let emission_end = emission_start_timestamp + (156 * 7 * SECONDS_PER_DAY);
        
        if (normal_lock_end <= emission_end) {
            normal_lock_end
        } else {
            emission_end
        }
    }

    fun create_claim_key(epoch_id: u64, lock_id: u64): u128 {
        assert!(epoch_id < (1u64 << 32), E_EPOCH_TOO_LARGE);
        assert!(lock_id < (1u64 << 32), E_LOCK_ID_TOO_LARGE);
        ((epoch_id as u128) << 64) | (lock_id as u128)
    }

    /// 🔒 SECURITY FUNCTION: Validate user owns specific lock_id
    fun validate_user_owns_lock(locker: &TokenLocker, user: address, lock_id: u64): bool {
        // Check all lock periods for this user
        let periods = vector[WEEK_LOCK, THREE_MONTH_LOCK, YEAR_LOCK, THREE_YEAR_LOCK];
        
        let mut i = 0;
        while (i < vector::length(&periods)) {
            let period = *vector::borrow(&periods, i);
            let user_locks = get_user_locks_for_period(locker, user, period);
            
            // Check if any lock in this period has the specified lock_id
            let mut j = 0;
            while (j < vector::length(&user_locks)) {
                let lock = vector::borrow(&user_locks, j);
                if (lock.id == lock_id) {
                    return true // User owns this lock
                };
                j = j + 1;
            };
            i = i + 1;
        };
        
        false // User does not own this lock_id
    }

    fun find_user_lock_any_pool_safe(locker: &TokenLocker, user: address, lock_id: u64): (bool, u64) {
        let periods = vector[WEEK_LOCK, THREE_MONTH_LOCK, YEAR_LOCK, THREE_YEAR_LOCK];
        
        let mut i = 0;
        while (i < vector::length(&periods)) {
            let period = *vector::borrow(&periods, i);
            let lock_table = if (period == WEEK_LOCK) {
                &locker.week_locks
            } else if (period == THREE_MONTH_LOCK) {
                &locker.three_month_locks
            } else if (period == YEAR_LOCK) {
                &locker.year_locks
            } else {
                &locker.three_year_locks
            };
            
            if (table::contains(lock_table, user)) {
                let user_locks = table::borrow(lock_table, user);
                let mut j = 0;
                let len = vector::length(user_locks);
                while (j < len) {
                    let lock = vector::borrow(user_locks, j);
                    if (lock.id == lock_id) {
                        return (true, period) // Found lock owned by user
                    };
                    j = j + 1;
                };
            };
            i = i + 1;
        };
        (false, 0) // ❌ Lock not found or not owned by user
    }

    public fun get_lock_end_by_index(
        locker: &TokenLocker, 
        user: address, 
        lock_index: u64,
        lock_period: u64
    ): u64 {
        let lock_table = if (lock_period == WEEK_LOCK) {
            &locker.week_locks
        } else if (lock_period == THREE_MONTH_LOCK) {
            &locker.three_month_locks
        } else if (lock_period == YEAR_LOCK) {
            &locker.year_locks
        } else {
            &locker.three_year_locks
        };
        
        if (!table::contains(lock_table, user)) {
            return 0
        };
        
        let user_locks = table::borrow(lock_table, user);
        if (lock_index >= vector::length(user_locks)) {
            return 0
        };
        
        let lock = vector::borrow(user_locks, lock_index);
        lock.lock_end
    }

    /// Get lock details by lock_id and period (essential for testing)
    public fun get_lock_by_id(
        locker: &TokenLocker,
        user: address,
        lock_id: u64,
        lock_period: u64
    ): (u64, u64, u64, u64, u64, u64, bool) {
        let lock_table = if (lock_period == WEEK_LOCK) {
            &locker.week_locks
        } else if (lock_period == THREE_MONTH_LOCK) {
            &locker.three_month_locks
        } else if (lock_period == YEAR_LOCK) {
            &locker.year_locks
        } else {
            &locker.three_year_locks
        };
        
        if (!table::contains(lock_table, user)) {
            return (0, 0, 0, 0, 0, 0, false) // Not found
        };
        
        let user_locks = table::borrow(lock_table, user);
        let mut i = 0;
        let len = vector::length(user_locks);
        
        while (i < len) {
            let lock = vector::borrow(user_locks, i);
            if (lock.id == lock_id) {
                return (
                    lock.amount,
                    lock.lock_period,
                    lock.lock_end,
                    lock.stake_timestamp,
                    lock.last_victory_claim_timestamp,
                    lock.last_sui_epoch_claimed,
                    true // Found
                )
            };
            i = i + 1;
        };
        
        (0, 0, 0, 0, 0, 0, false) // Not found
    }

    /// Get lock details by index (for ordered testing)
    public fun get_lock_by_index(
        locker: &TokenLocker,
        user: address,
        index: u64,
        lock_period: u64
    ): (u64, u64, u64, u64, u64, u64, u64, bool) {
        let lock_table = if (lock_period == WEEK_LOCK) {
            &locker.week_locks
        } else if (lock_period == THREE_MONTH_LOCK) {
            &locker.three_month_locks
        } else if (lock_period == YEAR_LOCK) {
            &locker.year_locks
        } else {
            &locker.three_year_locks
        };
        
        if (!table::contains(lock_table, user)) {
            return (0, 0, 0, 0, 0, 0, 0, false)
        };
        
        let user_locks = table::borrow(lock_table, user);
        if (index >= vector::length(user_locks)) {
            return (0, 0, 0, 0, 0, 0, 0, false)
        };
        
        let lock = vector::borrow(user_locks, index);
        (
            lock.id,
            lock.amount,
            lock.lock_period,
            lock.lock_end,
            lock.stake_timestamp,
            lock.last_victory_claim_timestamp,
            lock.last_sui_epoch_claimed,
            true
        )
    }

    /// Get epoch details with full information
    public fun get_epoch_full_details(
        locker: &TokenLocker,
        epoch_id: u64
    ): (u64, u64, u64, u64, u64, u64, u64, u64, bool, bool) {
        if (!epoch_exists(locker, epoch_id)) {
            return (0, 0, 0, 0, 0, 0, 0, 0, false, false)
        };
        
        let epoch = get_epoch(locker, epoch_id);
        (
            epoch.total_sui_revenue,
            epoch.week_start_timestamp,
            epoch.week_end_timestamp,
            epoch.pool_allocations.year_pool_sui,
            epoch.pool_allocations.year_pool_total_staked,
            epoch.year_pool_claimed,
            epoch_id,
            epoch.pool_allocations.year_pool_allocation,
            epoch.is_claimable,
            epoch.allocations_finalized
        )
    }

    /// Check if specific (user, epoch, lock) combination was claimed
    public fun check_specific_claim_status(
        locker: &TokenLocker,
        user: address,
        epoch_id: u64,
        lock_id: u64
    ): bool {
        has_user_claimed_pool_epoch(locker, user, epoch_id, lock_id)
    }

    /// Get user's claim history for specific lock
    public fun get_user_claim_history(
        locker: &TokenLocker,
        user: address,
        lock_id: u64
    ): (bool, u64, u64) {
        if (!table::contains(&locker.user_victory_claims, user)) {
            return (false, 0, 0)
        };
        
        let user_claims = table::borrow(&locker.user_victory_claims, user);
        if (!table::contains(user_claims, lock_id)) {
            return (false, 0, 0)
        };
        
        let claim_record = table::borrow(user_claims, lock_id);
        (true, claim_record.total_claimed, claim_record.last_claim_timestamp)
    }

    // === COMPLETE USER DATA TRACKING FUNCTIONS ===
        
    /// 🎯 VIEW FUNCTION: Get claimable summary for all user's locks
    public fun get_claimable_summary_for_user(
        locker: &TokenLocker,
        user: address,
        clock: &Clock
    ): (u64, u64, u64) { // (total_locks_with_rewards, total_epochs, total_sui)
        let all_user_locks = get_all_user_locks(locker, user);
        let locks_count = vector::length(&all_user_locks);
        
        if (locks_count == 0) {
            return (0, 0, 0)
        };
        
        let mut locks_with_rewards = 0;
        let mut total_epochs = 0;
        let mut total_sui = 0;
        let mut i = 0;
        
        while (i < locks_count) {
            let user_lock = vector::borrow(&all_user_locks, i);
            let (epochs, amounts, lock_total) = get_claimable_epochs_for_lock(
                locker, user, user_lock.id, clock
            );
            
            if (lock_total > 0) {
                locks_with_rewards = locks_with_rewards + 1;
                total_epochs = total_epochs + vector::length(&epochs);
                total_sui = total_sui + lock_total;
            };
            
            i = i + 1;
        };
        
        (locks_with_rewards, total_epochs, total_sui)
    }

    /// 🎯 VIEW FUNCTION: Get claimable epochs and amounts for a specific lock
    public fun get_claimable_epochs_for_lock(
        locker: &TokenLocker,
        user: address,
        lock_id: u64,
        clock: &Clock
    ): (vector<u64>, vector<u64>, u64) { // (epoch_ids, sui_amounts, total_sui)
        let current_time = clock::timestamp_ms(clock) / 1000;
        
        // Validate user owns lock
        let (user_owns_lock, lock_period) = find_user_lock_any_pool_safe(locker, user, lock_id);
        if (!user_owns_lock) {
            return (vector::empty(), vector::empty(), 0)
        };
        
        let (user_lock, _) = find_user_lock_any_pool(locker, user, lock_id);
        
        let mut claimable_epochs = vector::empty<u64>();
        let mut claimable_amounts = vector::empty<u64>();
        let mut total_claimable = 0;
        
        let current_epoch_id = get_current_epoch_id(locker);
        if (current_epoch_id == 0) {
            return (claimable_epochs, claimable_amounts, total_claimable)
        };
        
        let first_eligible_epoch = calculate_first_eligible_epoch(locker, user_lock);
        let start_epoch = {
            let after_last_claimed = if (user_lock.last_sui_epoch_claimed == 0) {
                1
            } else {
                user_lock.last_sui_epoch_claimed + 1
            };
            
            if (first_eligible_epoch > after_last_claimed) {
                first_eligible_epoch
            } else {
                after_last_claimed
            }
        };
        
        if (start_epoch <= current_epoch_id && start_epoch != 999999) {
            let mut epoch_id = start_epoch;
            let max_epochs = 200; // System max epochs
            let mut checked = 0;
            
            while (epoch_id <= current_epoch_id && checked < max_epochs) {
                if (epoch_exists(locker, epoch_id)) {
                    let epoch = get_epoch(locker, epoch_id);
                    
                    if (epoch.is_claimable && 
                        current_time >= epoch.week_end_timestamp &&
                        user_lock.stake_timestamp < epoch.week_start_timestamp &&
                        user_lock.lock_end >= epoch.week_end_timestamp &&
                        !has_user_claimed_pool_epoch(locker, user, epoch_id, lock_id)) {
                        
                        let sui_rewards = calculate_pool_based_sui_rewards_with_allocations(
                            &epoch.pool_allocations, 
                            user_lock, 
                            lock_period
                        );
                        
                        if (sui_rewards > 0) {
                            vector::push_back(&mut claimable_epochs, epoch_id);
                            vector::push_back(&mut claimable_amounts, sui_rewards);
                            total_claimable = total_claimable + sui_rewards;
                        };
                    };
                };
                
                epoch_id = epoch_id + 1;
                checked = checked + 1;
            };
        };
        
        (claimable_epochs, claimable_amounts, total_claimable)
    }

    // === COMPREHENSIVE USER DATA TRACKING ===

    /// 🔍 Get all claimed epochs for a specific lock with detailed information
    public fun get_lock_claim_history(
        locker: &TokenLocker,
        user: address,
        lock_id: u64
    ): (vector<u64>, vector<u64>, vector<u64>, u64, u64) {
        // Returns: (claimed_epochs, claim_amounts, claim_timestamps, total_claimed, last_claim_epoch)
        
        // Validate user owns lock
        let (user_owns_lock, lock_period) = find_user_lock_any_pool_safe(locker, user, lock_id);
        if (!user_owns_lock) {
            return (vector::empty(), vector::empty(), vector::empty(), 0, 0)
        };
        
        let (user_lock, _) = find_user_lock_any_pool(locker, user, lock_id);
        
        let mut claimed_epochs = vector::empty<u64>();
        let mut claim_amounts = vector::empty<u64>();
        let mut claim_timestamps = vector::empty<u64>();
        let mut total_claimed = 0;
        
        // Check if user has any claims recorded
        if (!table::contains(&locker.user_epoch_claims, user)) {
            return (claimed_epochs, claim_amounts, claim_timestamps, total_claimed, user_lock.last_sui_epoch_claimed)
        };
        
        let user_claims = table::borrow(&locker.user_epoch_claims, user);
        
        // Iterate through all possible epochs (max 200)
        let current_epoch_id = get_current_epoch_id(locker);
        let max_epochs = if (current_epoch_id > 200) 200 else current_epoch_id;
        let mut epoch_id = 1;
        
        while (epoch_id <= max_epochs) {
            let claim_key = create_claim_key(epoch_id, lock_id);
            
            if (table::contains(user_claims, claim_key)) {
                let claim_record = table::borrow(user_claims, claim_key);
                
                vector::push_back(&mut claimed_epochs, epoch_id);
                vector::push_back(&mut claim_amounts, claim_record.sui_claimed);
                vector::push_back(&mut claim_timestamps, claim_record.claim_timestamp);
                total_claimed = total_claimed + claim_record.sui_claimed;
            };
            
            epoch_id = epoch_id + 1;
        };
        
        (claimed_epochs, claim_amounts, claim_timestamps, total_claimed, user_lock.last_sui_epoch_claimed)
    }

    /// 📊 Get comprehensive claim summary for ALL user's locks
    public fun get_user_comprehensive_claim_summary(
        locker: &TokenLocker,
        user: address
    ): (vector<u64>, vector<u64>, vector<u64>, vector<u64>, u64, u64) {
        // Returns: (lock_ids, lock_periods, epochs_claimed_per_lock, total_claimed_per_lock, grand_total_epochs, grand_total_sui)
        
        let all_user_locks = get_all_user_locks(locker, user);
        let locks_count = vector::length(&all_user_locks);
        
        let mut lock_ids = vector::empty<u64>();
        let mut lock_periods = vector::empty<u64>();
        let mut epochs_claimed_per_lock = vector::empty<u64>();
        let mut total_claimed_per_lock = vector::empty<u64>();
        let mut grand_total_epochs = 0;
        let mut grand_total_sui = 0;
        
        if (locks_count == 0) {
            return (lock_ids, lock_periods, epochs_claimed_per_lock, total_claimed_per_lock, grand_total_epochs, grand_total_sui)
        };
        
        let mut i = 0;
        while (i < locks_count) {
            let user_lock = vector::borrow(&all_user_locks, i);
            let lock_id = user_lock.id;
            let lock_period = user_lock.lock_period;
            
            // Get claim history for this lock
            let (claimed_epochs, claim_amounts, _, total_claimed, _) = get_lock_claim_history(locker, user, lock_id);
            let epochs_count = vector::length(&claimed_epochs);
            
            vector::push_back(&mut lock_ids, lock_id);
            vector::push_back(&mut lock_periods, lock_period);
            vector::push_back(&mut epochs_claimed_per_lock, epochs_count);
            vector::push_back(&mut total_claimed_per_lock, total_claimed);
            
            grand_total_epochs = grand_total_epochs + epochs_count;
            grand_total_sui = grand_total_sui + total_claimed;
            
            i = i + 1;
        };
        
        (lock_ids, lock_periods, epochs_claimed_per_lock, total_claimed_per_lock, grand_total_epochs, grand_total_sui)
    }

    /// 📈 Get epoch-by-epoch breakdown for a user across all locks
    public fun get_user_epoch_breakdown(
        locker: &TokenLocker,
        user: address
    ): (vector<u64>, vector<u64>, vector<u64>) {
        // Returns: (epoch_ids, total_claimed_per_epoch, locks_claimed_per_epoch)
        
        let mut epoch_totals = vector::empty<u64>();
        let mut epoch_ids = vector::empty<u64>();
        let mut locks_per_epoch = vector::empty<u64>();
        
        if (!table::contains(&locker.user_epoch_claims, user)) {
            return (epoch_ids, epoch_totals, locks_per_epoch)
        };
        
        let user_claims = table::borrow(&locker.user_epoch_claims, user);
        let current_epoch_id = get_current_epoch_id(locker);
        let max_epochs = if (current_epoch_id > 200) 200 else current_epoch_id;
        
        let mut epoch_id = 1;
        while (epoch_id <= max_epochs) {
            let mut epoch_total = 0;
            let mut locks_count = 0;
            
            // Get all user's locks to check claims for this epoch
            let all_user_locks = get_all_user_locks(locker, user);
            let total_locks = vector::length(&all_user_locks);
            
            let mut i = 0;
            while (i < total_locks) {
                let user_lock = vector::borrow(&all_user_locks, i);
                let claim_key = create_claim_key(epoch_id, user_lock.id);
                
                if (table::contains(user_claims, claim_key)) {
                    let claim_record = table::borrow(user_claims, claim_key);
                    epoch_total = epoch_total + claim_record.sui_claimed;
                    locks_count = locks_count + 1;
                };
                
                i = i + 1;
            };
            
            if (epoch_total > 0) {
                vector::push_back(&mut epoch_ids, epoch_id);
                vector::push_back(&mut epoch_totals, epoch_total);
                vector::push_back(&mut locks_per_epoch, locks_count);
            };
            
            epoch_id = epoch_id + 1;
        };
        
        (epoch_ids, epoch_totals, locks_per_epoch)
    }

    /// 🎯 Check claim status for multiple epochs and locks at once
    public fun batch_check_claim_status(
        locker: &TokenLocker,
        user: address,
        epoch_ids: vector<u64>,
        lock_ids: vector<u64>
    ): vector<bool> {
        // Returns vector of claim statuses for each (epoch, lock) pair
        
        let epochs_len = vector::length(&epoch_ids);
        let locks_len = vector::length(&lock_ids);
        
        assert!(epochs_len == locks_len, E_INVALID_BATCH_DATA);
        
        let mut claim_statuses = vector::empty<bool>();
        let mut i = 0;
        
        while (i < epochs_len) {
            let epoch_id = *vector::borrow(&epoch_ids, i);
            let lock_id = *vector::borrow(&lock_ids, i);
            
            let is_claimed = has_user_claimed_pool_epoch(locker, user, epoch_id, lock_id);
            vector::push_back(&mut claim_statuses, is_claimed);
            
            i = i + 1;
        };
        
        claim_statuses
    }

    /// 📋 Get detailed claim record for specific (epoch, lock) combination  
    public fun get_detailed_claim_record(
        locker: &TokenLocker,
        user: address,
        epoch_id: u64,
        lock_id: u64
    ): (bool, u64, u64, u64, u64, u64) {
        // Returns: (claimed, sui_amount, claim_timestamp, lock_period, pool_type, amount_staked)
        
        if (!table::contains(&locker.user_epoch_claims, user)) {
            return (false, 0, 0, 0, 0, 0)
        };
        
        let user_claims = table::borrow(&locker.user_epoch_claims, user);
        let claim_key = create_claim_key(epoch_id, lock_id);
        
        if (!table::contains(user_claims, claim_key)) {
            return (false, 0, 0, 0, 0, 0)
        };
        
        let claim_record = table::borrow(user_claims, claim_key);
        (
            true,
            claim_record.sui_claimed,
            claim_record.claim_timestamp,
            claim_record.lock_period,
            (claim_record.pool_type as u64),
            claim_record.amount_staked
        )
    }

    /// 🔍 Get complete user dashboard data in one call
    public fun get_user_dashboard_data(
        locker: &TokenLocker,
        user: address,
        clock: &Clock
    ): (
        // Claim History
        u64, u64, u64,  // (total_locks, total_claimed_epochs, total_claimed_sui)
        // Claimable Now  
        u64, u64, u64,  // (locks_with_claimable, claimable_epochs, claimable_sui)
        // Lock Summary
        u64, u64, u64, u64, u64  // (week_locks, month_locks, year_locks, three_year_locks, total_staked)
    ) {
        // Historical claims
        let (_, _, epochs_per_lock, claimed_per_lock, total_epochs, total_claimed) = 
            get_user_comprehensive_claim_summary(locker, user);
        let total_locks = vector::length(&epochs_per_lock);
        
        // Current claimable
        let (claimable_locks, claimable_epochs, claimable_sui) = 
            get_claimable_summary_for_user(locker, user, clock);
        
        // Lock distribution
        let (week_amount, month_amount, year_amount, three_year_amount, total_staked) = 
            get_user_total_staked(locker, user);
        
        // Count locks by period
        let week_locks_count = vector::length(&get_user_locks_for_period(locker, user, WEEK_LOCK));
        let month_locks_count = vector::length(&get_user_locks_for_period(locker, user, THREE_MONTH_LOCK));
        let year_locks_count = vector::length(&get_user_locks_for_period(locker, user, YEAR_LOCK));
        let three_year_locks_count = vector::length(&get_user_locks_for_period(locker, user, THREE_YEAR_LOCK));
        
        (
            total_locks, total_epochs, total_claimed,
            claimable_locks, claimable_epochs, claimable_sui,
            week_locks_count, month_locks_count, year_locks_count, three_year_locks_count, total_staked
        )
    }

    // === LOCK-SPECIFIC TRACKING ===

    /// Get all epochs that exist in the system
    public fun get_all_epochs_info(locker: &TokenLocker): (vector<u64>, vector<u64>, vector<bool>) {
        // Returns: (epoch_ids, total_revenue, is_claimable)
        
        let current_epoch_id = get_current_epoch_id(locker);
        let max_epochs = if (current_epoch_id > 200) 200 else current_epoch_id;
        let mut epoch_ids = vector::empty<u64>();
        let mut revenues = vector::empty<u64>();
        let mut claimable_status = vector::empty<bool>();
        
        let mut epoch_id = 1;
        while (epoch_id <= max_epochs) {
            if (epoch_exists(locker, epoch_id)) {
                let epoch = get_epoch(locker, epoch_id);
                
                vector::push_back(&mut epoch_ids, epoch_id);
                vector::push_back(&mut revenues, epoch.total_sui_revenue);
                vector::push_back(&mut claimable_status, epoch.is_claimable);
            };
            
            epoch_id = epoch_id + 1;
        };
        
        (epoch_ids, revenues, claimable_status)
    }

    /// Get user's lock IDs for easier frontend integration
    public fun get_user_lock_ids_by_period(
        locker: &TokenLocker,
        user: address,
        lock_period: u64
    ): vector<u64> {
        let user_locks = get_user_locks_for_period(locker, user, lock_period);
        let mut lock_ids = vector::empty<u64>();
        
        let mut i = 0;
        let len = vector::length(&user_locks);
        while (i < len) {
            let lock = vector::borrow(&user_locks, i);
            vector::push_back(&mut lock_ids, lock.id);
            i = i + 1;
        };
        
        lock_ids
    }

    /// 📊 Get detailed lock information for UI
    public fun get_lock_detailed_info(
        locker: &TokenLocker,
        user: address,
        lock_id: u64,
        clock: &Clock
    ): (bool, u64, u64, u64, u64, u64, u64, u64, u64, u64) {
        // Returns: (found, amount, lock_period, lock_end, stake_timestamp, last_victory_claim, 
        //          total_victory_claimed, last_sui_epoch, total_claimed_epochs, claimable_now)
        
        let (user_owns_lock, lock_period) = find_user_lock_any_pool_safe(locker, user, lock_id);
        if (!user_owns_lock) {
            return (false, 0, 0, 0, 0, 0, 0, 0, 0, 0)
        };
        
        let (user_lock, _) = find_user_lock_any_pool(locker, user, lock_id);
        
        // Get claim history
        let (claimed_epochs, _, _, total_claimed_sui, _) = get_lock_claim_history(locker, user, lock_id);
        let total_claimed_epochs = vector::length(&claimed_epochs);
        
        // Get claimable now
        let (_, _, claimable_now) = get_claimable_epochs_for_lock(locker, user, lock_id, clock);
        
        (
            true,
            user_lock.amount,
            user_lock.lock_period,
            user_lock.lock_end,
            user_lock.stake_timestamp,
            user_lock.last_victory_claim_timestamp,
            user_lock.total_victory_claimed,
            user_lock.last_sui_epoch_claimed,
            total_claimed_epochs,
            claimable_now
        )
    }

    /// 🎯 Get user's position in each pool for current epoch
    public fun get_user_pool_positions(
        locker: &TokenLocker,
        user: address,
        clock: &Clock
    ): (u64, u64, u64, u64, u64, u64, u64, u64) {
        // Returns: (week_staked, week_pool_total, month_staked, month_pool_total, 
        //          year_staked, year_pool_total, three_year_staked, three_year_pool_total)
        
        let (week_amount, month_amount, year_amount, three_year_amount, _) = 
            get_user_total_staked(locker, user);
        
        let (week_pool_total, month_pool_total, year_pool_total, three_year_pool_total, _) = 
            get_pool_statistics(locker);
        
        (
            week_amount, week_pool_total,
            month_amount, month_pool_total, 
            year_amount, year_pool_total,
            three_year_amount, three_year_pool_total
        )
    }

    /// 📈 Get projected rewards for current epoch
    public fun get_projected_current_epoch_rewards(
        locker: &TokenLocker,
        user: address
    ): (u64, u64, u64, u64, u64) {
        // Returns: (week_projected, month_projected, year_projected, three_year_projected, total_projected)
        
        let current_epoch_id = get_current_epoch_id(locker);
        if (current_epoch_id == 0 || !epoch_exists(locker, current_epoch_id)) {
            return (0, 0, 0, 0, 0)
        };
        
        let epoch = get_epoch(locker, current_epoch_id);
        let (week_staked, month_staked, year_staked, three_year_staked, _) = 
            get_user_total_staked(locker, user);
        
        // Calculate projected rewards based on current pool allocations
        let week_projected = if (epoch.pool_allocations.week_pool_total_staked > 0 && week_staked > 0) {
            safe_mul_div(week_staked, epoch.pool_allocations.week_pool_sui, epoch.pool_allocations.week_pool_total_staked)
        } else { 0 };
        
        let month_projected = if (epoch.pool_allocations.three_month_pool_total_staked > 0 && month_staked > 0) {
            safe_mul_div(month_staked, epoch.pool_allocations.three_month_pool_sui, epoch.pool_allocations.three_month_pool_total_staked)
        } else { 0 };
        
        let year_projected = if (epoch.pool_allocations.year_pool_total_staked > 0 && year_staked > 0) {
            safe_mul_div(year_staked, epoch.pool_allocations.year_pool_sui, epoch.pool_allocations.year_pool_total_staked)
        } else { 0 };
        
        let three_year_projected = if (epoch.pool_allocations.three_year_pool_total_staked > 0 && three_year_staked > 0) {
            safe_mul_div(three_year_staked, epoch.pool_allocations.three_year_pool_sui, epoch.pool_allocations.three_year_pool_total_staked)
        } else { 0 };
        
        let total_projected = week_projected + month_projected + year_projected + three_year_projected;
        
        (week_projected, month_projected, year_projected, three_year_projected, total_projected)
    }

    /// 🔍 Get user's complete claim matrix (epochs x locks)
    public fun get_user_claim_matrix(
        locker: &TokenLocker,
        user: address
    ): (vector<u64>, vector<u64>, vector<vector<bool>>) {
        // Returns: (epoch_ids, lock_ids, claim_matrix[epoch][lock] = claimed)
        
        let all_user_locks = get_all_user_locks(locker, user);
        let locks_count = vector::length(&all_user_locks);
        
        if (locks_count == 0) {
            return (vector::empty(), vector::empty(), vector::empty())
        };
        
        // Get all lock IDs
        let mut lock_ids = vector::empty<u64>();
        let mut i = 0;
        while (i < locks_count) {
            let user_lock = vector::borrow(&all_user_locks, i);
            vector::push_back(&mut lock_ids, user_lock.id);
            i = i + 1;
        };
        
        // Get all epochs
        let current_epoch_id = get_current_epoch_id(locker);
        let max_epochs = if (current_epoch_id > 200) 200 else current_epoch_id;
        let mut epoch_ids = vector::empty<u64>();
        let mut epoch_id = 1;
        while (epoch_id <= max_epochs) {
            if (epoch_exists(locker, epoch_id)) {
                vector::push_back(&mut epoch_ids, epoch_id);
            };
            epoch_id = epoch_id + 1;
        };
        
        // Build claim matrix
        let mut claim_matrix = vector::empty<vector<bool>>();
        let epochs_count = vector::length(&epoch_ids);
        
        let mut e = 0;
        while (e < epochs_count) {
            let epoch_id = *vector::borrow(&epoch_ids, e);
            let mut epoch_claims = vector::empty<bool>();
            
            let mut l = 0;
            while (l < locks_count) {
                let lock_id = *vector::borrow(&lock_ids, l);
                let is_claimed = has_user_claimed_pool_epoch(locker, user, epoch_id, lock_id);
                vector::push_back(&mut epoch_claims, is_claimed);
                l = l + 1;
            };
            
            vector::push_back(&mut claim_matrix, epoch_claims);
            e = e + 1;
        };
        
        (epoch_ids, lock_ids, claim_matrix)
    }

    // === BATCH CLAIM PREVIEW FUNCTIONS ===

    /// Preview what will be claimed for a specific lock (reuses get_claimable_epochs_for_lock)
    public fun preview_claim_for_lock(
        locker: &TokenLocker,
        user: address,
        lock_id: u64,
        clock: &Clock
    ): (bool, u64, u64, u64, vector<u64>, vector<u64>) {
        // Returns: (can_claim, total_epochs, total_sui, lock_amount, epoch_ids, sui_amounts)
        
        // Validate user owns this lock first
        let (user_owns_lock, lock_period) = find_user_lock_any_pool_safe(locker, user, lock_id);
        if (!user_owns_lock) {
            return (false, 0, 0, 0, vector::empty(), vector::empty())
        };
        
        // Get lock details
        let (user_lock, _) = find_user_lock_any_pool(locker, user, lock_id);
        
        // Reuse existing tracking function!
        let (claimable_epochs, claimable_amounts, total_claimable) = 
            get_claimable_epochs_for_lock(locker, user, lock_id, clock);
        
        let epoch_count = vector::length(&claimable_epochs);
        let can_claim = epoch_count > 0 && total_claimable > 0;
        
        (can_claim, epoch_count, total_claimable, user_lock.amount, claimable_epochs, claimable_amounts)
    }

    /// Preview what will be claimed for ALL user's locks (reuses existing functions)
    public fun preview_claim_for_all_user_locks(
        locker: &TokenLocker,
        user: address,
        clock: &Clock
    ): (bool, u64, u64, u64, vector<u64>, vector<u64>, vector<u64>) {
        // Returns: (can_claim, total_locks_with_rewards, total_epochs, total_sui, lock_ids, epoch_counts, sui_amounts)
        
        // Reuse existing comprehensive summary function!
        let (locks_with_claimable, total_epochs, total_sui) = 
            get_claimable_summary_for_user(locker, user, clock);
        
        if (locks_with_claimable == 0) {
            return (false, 0, 0, 0, vector::empty(), vector::empty(), vector::empty())
        };
        
        // Get all user's locks
        let all_user_locks = get_all_user_locks(locker, user);
        let mut lock_ids_with_rewards = vector::empty<u64>();
        let mut epoch_counts_per_lock = vector::empty<u64>();
        let mut sui_amounts_per_lock = vector::empty<u64>();
        
        let mut i = 0;
        while (i < vector::length(&all_user_locks)) {
            let user_lock = vector::borrow(&all_user_locks, i);
            let lock_id = user_lock.id;
            
            // Reuse existing function for each lock!
            let (_, _, lock_total) = get_claimable_epochs_for_lock(locker, user, lock_id, clock);
            
            if (lock_total > 0) {
                let (epochs, _, _) = get_claimable_epochs_for_lock(locker, user, lock_id, clock);
                vector::push_back(&mut lock_ids_with_rewards, lock_id);
                vector::push_back(&mut epoch_counts_per_lock, vector::length(&epochs));
                vector::push_back(&mut sui_amounts_per_lock, lock_total);
            };
            i = i + 1;
        };
        
        (true, locks_with_claimable, total_epochs, total_sui, lock_ids_with_rewards, epoch_counts_per_lock, sui_amounts_per_lock)
    }


    /// Helper: Calculate emission week number from timestamp
    fun calculate_emission_week_from_timestamp(config: &GlobalEmissionConfig, timestamp: u64): u64 {
        let (emission_start, _) = global_emission_controller::get_config_info(config);
        
        if (timestamp < emission_start) {
            return 0
        };
        
        let elapsed_seconds = timestamp - emission_start;
        let weeks_elapsed = elapsed_seconds / (SECONDS_PER_DAY * 7);
        weeks_elapsed + 1 // Week 1, 2, 3, ...
    }

    /// Helper: Get emission week start timestamp
    fun calculate_emission_week_start(config: &GlobalEmissionConfig, week: u64): u64 {
        let (emission_start, _) = global_emission_controller::get_config_info(config);
        
        if (week <= 1) {
            emission_start
        } else {
            emission_start + ((week - 1) * SECONDS_PER_DAY * 7)
        }
    }

    /// Helper: Get Victory allocation for specific emission week
    fun get_victory_allocation_for_emission_week(config: &GlobalEmissionConfig, week: u64): u256 {
        // This calls your global emission controller
        global_emission_controller::get_victory_allocation_for_week(config, week)
    }


    /// 🔧 HELPER: Calculate user rewards for a portion of an emission week with perfect precision
    fun calculate_user_rewards_for_week_portion_precise(
        locker: &TokenLocker,
        user_lock: &Lock,
        week_victory_per_sec: u256,
        seconds_in_portion: u64
    ): u64 {
        if (week_victory_per_sec == 0 || seconds_in_portion == 0) {
            return 0
        };
        
        let lock_period_allocation_bp = get_dynamic_victory_allocation(locker, user_lock.lock_period);
        let pool_victory_per_sec = (week_victory_per_sec * (lock_period_allocation_bp as u256)) / (BASIS_POINTS as u256);
        
        // ✅ Always use current pool total for accuracy
        let current_pool_total = get_pool_total_locked(locker, user_lock.lock_period);
        if (current_pool_total == 0) {
            return 0
        };
        
        let user_victory_per_sec = (pool_victory_per_sec * (user_lock.amount as u256)) / (current_pool_total as u256);
        let portion_rewards = user_victory_per_sec * (seconds_in_portion as u256);
        
        if (portion_rewards > (18446744073709551615 as u256)) {
            18446744073709551615
        } else {
            (portion_rewards as u64)
        }
    }


    /// 📅 HELPER: Get emission week start timestamp with precision
    fun calculate_emission_week_start_precise(config: &GlobalEmissionConfig, week: u64): u64 {
        let (emission_start, _) = global_emission_controller::get_config_info(config);
        
        if (week <= 1) {
            emission_start
        } else if (week > 156) {
            emission_start + (156 * SECONDS_PER_DAY * 7) // Cap at emission end
        } else {
            emission_start + ((week - 1) * SECONDS_PER_DAY * 7)
        }
    }

    /// ⚡ HELPER: Get Victory allocation for specific emission week with precision
    fun get_victory_allocation_for_emission_week_precise(config: &GlobalEmissionConfig, week: u64): u256 {
        if (week == 0 || week > 156) {
            return 0
        };
        
        // This calls your global emission controller to get EXACT Victory staking allocation
        // for the specific week, accounting for all phase changes and decay
        global_emission_controller::get_victory_allocation_for_week(config, week)
    }

    /// 📊 HELPER: Get Victory allocation percentage for lock period (from TokenLocker config)
    fun get_victory_allocation_percentage_for_period(lock_period: u64): u64 {

        if (lock_period == WEEK_LOCK) 200        // 2% of Victory staking
        else if (lock_period == THREE_MONTH_LOCK) 800  // 8% of Victory staking
        else if (lock_period == YEAR_LOCK) 2500        // 25% of Victory staking
        else 6500                                      // 65% of Victory staking (3-year)
    }

    /// 🔍 HELPER: Calculate effective reward time, capped to lock end time
    fun calculate_effective_reward_time(
        last_claim_timestamp: u64,
        current_time: u64,
        lock_end: u64
    ): u64 {
        // If last claim was after lock ended, no additional rewards
        if (last_claim_timestamp >= lock_end) {
            return 0
        };
        
        // If current time is within lock period, use actual elapsed time
        if (current_time <= lock_end) {
            return current_time - last_claim_timestamp
        };
        
        // If current time exceeds lock end, cap rewards to lock end time
        lock_end - last_claim_timestamp
    }

    fun calculate_emission_week_from_timestamp_precise(config: &GlobalEmissionConfig, timestamp: u64): u64 {
        global_emission_controller::get_canonical_week_for_timestamp(config, timestamp)
    }


    // Get accumulator stats
    public fun get_victory_accumulator_stats(locker: &TokenLocker, lock_period: u64): (u256, u64, u64, u64) {
        let accumulator = get_victory_accumulator(locker, lock_period);
        (
            accumulator.acc_victory_per_share,
            accumulator.last_reward_time,
            accumulator.total_staked,
            accumulator.total_victory_distributed
        )
    }

    // Get user position details
    public fun get_user_victory_position_details(
        locker: &TokenLocker,
        user: address,
        lock_id: u64
    ): (u64, u64, u64, u64, u256, u64, u64, bool) {
        if (!has_victory_position(locker, user, lock_id)) {
            return (0, 0, 0, 0, 0, 0, 0, false)
        };
        
        let position = get_victory_position(locker, user, lock_id);
        (
            position.stake_amount,
            position.lock_start_time,
            position.lock_end_time,
            position.lock_period,
            position.victory_reward_debt,
            position.total_victory_claimed,
            position.last_victory_claim_time,
            position.is_active
        )
    }

    // Get mutable accumulator reference
    fun get_victory_accumulator_mut(locker: &mut TokenLocker, lock_period: u64): &mut VictoryPoolAccumulator {
        if (lock_period == WEEK_LOCK) &mut locker.victory_week_accumulator
        else if (lock_period == THREE_MONTH_LOCK) &mut locker.victory_three_month_accumulator
        else if (lock_period == YEAR_LOCK) &mut locker.victory_year_accumulator
        else &mut locker.victory_three_year_accumulator
    }

    // Get immutable accumulator reference
    fun get_victory_accumulator(locker: &TokenLocker, lock_period: u64): &VictoryPoolAccumulator {
        if (lock_period == WEEK_LOCK) &locker.victory_week_accumulator
        else if (lock_period == THREE_MONTH_LOCK) &locker.victory_three_month_accumulator
        else if (lock_period == YEAR_LOCK) &locker.victory_year_accumulator
        else &locker.victory_three_year_accumulator
    }

    // Check if user has Victory position
    fun has_victory_position(locker: &TokenLocker, user: address, lock_id: u64): bool {
        if (!table::contains(&locker.victory_user_positions, user)) return false;
        let user_positions = table::borrow(&locker.victory_user_positions, user);
        table::contains(user_positions, lock_id)
    }

    // Get user position (immutable)
    fun get_victory_position(locker: &TokenLocker, user: address, lock_id: u64): &VictoryUserPosition {
        let user_positions = table::borrow(&locker.victory_user_positions, user);
        table::borrow(user_positions, lock_id)
    }

    // Get user position (mutable)
    fun get_victory_position_mut(locker: &mut TokenLocker, user: address, lock_id: u64): &mut VictoryUserPosition {
        let user_positions = table::borrow_mut(&mut locker.victory_user_positions, user);
        table::borrow_mut(user_positions, lock_id)
    }

    fun safe_mul_div_u256(a: u256, b: u256, c: u256): u256 {
        if (c == 0) return 0;
        if (a == 0 || b == 0) return 0;
        (a * b) / c
    }

    fun update_victory_accumulator_for_period(
        locker: &mut TokenLocker,
        lock_period: u64,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        current_time: u64
    ) {
        let period_allocation_bp = get_dynamic_victory_allocation(locker, lock_period);
        let accumulator = get_victory_accumulator_mut(locker, lock_period);
        
        if (accumulator.total_staked == 0) {
            accumulator.last_reward_time = current_time;
            return
        };
        
        if (current_time <= accumulator.last_reward_time) return;
        
        // ✅ KEEP: All your original math exactly as it was
        let total_rewards = calculate_period_rewards_multi_week(
            global_config,
            period_allocation_bp,
            accumulator.last_reward_time,
            current_time
        );
        
        let rewards_per_share = safe_mul_div_u256(total_rewards, PRECISION_FACTOR, (accumulator.total_staked as u256));
        accumulator.acc_victory_per_share = accumulator.acc_victory_per_share + rewards_per_share;
        accumulator.last_reward_time = current_time;
        
        if (total_rewards <= (18446744073709551615 as u256)) {
            accumulator.total_victory_distributed = accumulator.total_victory_distributed + (total_rewards as u64);
        };
    }

    fun simulate_accumulator_update(
        accumulator: &VictoryPoolAccumulator,
        locker: &TokenLocker,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        current_time: u64
    ): u256 {
        if (accumulator.total_staked == 0) return accumulator.acc_victory_per_share;
        if (current_time <= accumulator.last_reward_time) return accumulator.acc_victory_per_share;
        
        // 🔧 Extract period allocation before calculation
        let period_allocation_bp = get_dynamic_victory_allocation(locker, accumulator.lock_period);
        let last_reward_time = accumulator.last_reward_time;
        let total_staked = accumulator.total_staked;
        let current_acc_per_share = accumulator.acc_victory_per_share;
        
        // 🔄 UPDATED LOGIC: Use multi-week calculation with extracted values
        let total_rewards = calculate_period_rewards_multi_week(
            global_config,
            period_allocation_bp,
            last_reward_time,
            current_time
        );
        
        let rewards_per_share = safe_mul_div_u256(
            total_rewards, 
            PRECISION_FACTOR, 
            (total_staked as u256)
        );
        
        current_acc_per_share + rewards_per_share
    }

    fun apply_time_cap_to_rewards(
        pending_rewards: u256,
        last_claim_time: u64,
        current_time: u64,
        lock_end_time: u64
    ): u256 {
        // ✅ CRITICAL: If lock expired, cap calculation time to lock_end_time
        let effective_current_time = if (current_time > lock_end_time) {
            lock_end_time
        } else {
            current_time
        };
        
        // If last claim was at or after lock end, no more rewards
        if (last_claim_time >= lock_end_time) {
            return 0
        };
        
        // If effective current time is not after last claim, no rewards
        if (effective_current_time <= last_claim_time) {
            return 0
        };
        
        // Calculate what portion of requested time is valid
        let total_requested_time = current_time - last_claim_time;
        let valid_earning_time = effective_current_time - last_claim_time;
        
        // If all requested time is valid, return full rewards
        if (valid_earning_time >= total_requested_time) {
            pending_rewards
        } else {
            // Scale rewards proportionally to valid time
            safe_mul_div_u256(
                pending_rewards,
                (valid_earning_time as u256),
                (total_requested_time as u256)
            )
        }
    }

    // Update user debt after claim
    fun update_user_position_debt(user_position: &mut VictoryUserPosition, accumulator: &VictoryPoolAccumulator) {
        user_position.victory_reward_debt = safe_mul_div_u256(
            (user_position.stake_amount as u256),
            accumulator.acc_victory_per_share,
            PRECISION_FACTOR
        );
    }

    fun create_victory_position(
        locker: &mut TokenLocker,
        user: address,
        lock_id: u64,
        amount: u64,
        lock_period: u64,
        lock_end_time: u64,
        current_time: u64,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        ctx: &mut TxContext
    ) {
        // ✅ STEP 1: Update accumulator FIRST (with old total_staked)
        update_victory_accumulator_for_period(locker, lock_period, global_config, clock, current_time);
        
        // ✅ STEP 2: Get current accumulator state AFTER proper update
        let current_acc_per_share = {
            let accumulator = get_victory_accumulator(locker, lock_period);
            accumulator.acc_victory_per_share
        };
        
        // ✅ STEP 3: NOW update total_staked (after accumulator update)
        let accumulator_mut = get_victory_accumulator_mut(locker, lock_period);
        accumulator_mut.total_staked = accumulator_mut.total_staked + amount;
        
        // ✅ STEP 4: Calculate debt based on UPDATED accumulator
        let initial_debt = safe_mul_div_u256(
            (amount as u256), 
            current_acc_per_share, 
            PRECISION_FACTOR
        );
        
        let position = VictoryUserPosition {
            stake_amount: amount,
            lock_start_time: current_time,
            lock_end_time,
            lock_period,
            victory_reward_debt: initial_debt, // ✅ Now uses correct debt!
            total_victory_claimed: 0,
            last_victory_claim_time: current_time,
            is_active: true,
            position_id: lock_id,
        };
        
        // ✅ STEP 5: Store position
        if (!table::contains(&locker.victory_user_positions, user)) {
            table::add(&mut locker.victory_user_positions, user, table::new(ctx));
        };
        let user_positions = table::borrow_mut(&mut locker.victory_user_positions, user);
        table::add(user_positions, lock_id, position);
    }

    fun deactivate_victory_position(locker: &mut TokenLocker, user: address, lock_id: u64) {
        if (!has_victory_position(locker, user, lock_id)) return;
        
        // ✅ EXTRACT values before any mutable borrows
        let (amount, lock_period) = {
            let user_position = get_victory_position(locker, user, lock_id);
            (user_position.stake_amount, user_position.lock_period)
        };
        
        // Deactivate position first
        let user_position_mut = get_victory_position_mut(locker, user, lock_id);
        user_position_mut.is_active = false;
        
        // ✅ CRITICAL: Update accumulator BEFORE reducing total_staked
        // This ensures final rewards are calculated with correct pool total
        let accumulator = get_victory_accumulator_mut(locker, lock_period);
        
        // Only reduce total if there was actually staked amount
        if (accumulator.total_staked >= amount) {
            accumulator.total_staked = accumulator.total_staked - amount;
        };
    }


    fun calculate_masterchef_pending_victory(
        user_position: &VictoryUserPosition,
        accumulator: &VictoryPoolAccumulator,
        locker: &TokenLocker,
        global_config: &GlobalEmissionConfig,
        clock: &Clock,
        current_time: u64
    ): u64 {
        if (!user_position.is_active) return 0;
        
        // ✅ Time capping (keep existing logic)
        let effective_calculation_time = if (current_time > user_position.lock_end_time) {
            user_position.lock_end_time
        } else {
            current_time
        };
        
        // 🔄 UPDATED: Now uses multi-week logic via simulate_accumulator_update
        let current_acc_per_share = simulate_accumulator_update(
            accumulator, locker, global_config, clock, effective_calculation_time
        );
        
        let gross_rewards = safe_mul_div_u256(
            (user_position.stake_amount as u256), 
            current_acc_per_share, 
            PRECISION_FACTOR
        );
        
        if (gross_rewards <= user_position.victory_reward_debt) return 0;
        let pending_u256 = gross_rewards - user_position.victory_reward_debt;
        
        if (pending_u256 > (18446744073709551615 as u256)) {
            18446744073709551615
        } else {
            (pending_u256 as u64)
        }
    }

    // 🆕 HELPER: multi-week logic
    fun calculate_period_rewards_multi_week(
        global_config: &GlobalEmissionConfig,
        period_allocation_bp: u64,
        start_time: u64,
        end_time: u64
    ): u256 {
        if (start_time >= end_time) return 0;
        
        let (emission_start_timestamp, is_paused) = global_emission_controller::get_config_info(global_config);
        if (emission_start_timestamp == 0 || is_paused) return 0;
        
        let first_week = calculate_emission_week_from_timestamp_precise(global_config, start_time);
        let last_week = calculate_emission_week_from_timestamp_precise(global_config, end_time);
        
        if (first_week == 0 || last_week == 0 || first_week > 156 || last_week > 156) {
            return 0
        };
        
        let mut total_rewards = 0u256;
        let mut week_number = first_week;
        
        while (week_number <= last_week && week_number <= 156) {
            let week_start_ts = calculate_emission_week_start_precise(global_config, week_number);
            let week_end_ts = week_start_ts + (SECONDS_PER_DAY * 7);
            
            let overlap_start = if (start_time > week_start_ts) start_time else week_start_ts;
            let overlap_end = if (end_time < week_end_ts) end_time else week_end_ts;
            
            if (overlap_start < overlap_end) {
                let seconds_in_week = overlap_end - overlap_start;
                
                // 🔄 REUSE: Your tested logic
                let week_victory_per_sec = get_victory_allocation_for_emission_week_precise(global_config, week_number);
                let period_victory_per_sec = (week_victory_per_sec * (period_allocation_bp as u256)) / (BASIS_POINTS as u256);
                
                let week_rewards = period_victory_per_sec * (seconds_in_week as u256);
                total_rewards = total_rewards + week_rewards;
            };
            
            week_number = week_number + 1;
        };
        
        total_rewards
    }

    #[test_only]
    public fun init_for_testing(ctx: &mut TxContext) {
        init(ctx)
    }

}