#[test_only]
module suitrump_dex::library_tests {
    use sui::test_utils::assert_eq;
    use suitrump_dex::library;
    use std::debug;

    // Constants
    const BASIS_POINTS: u256 = 10000;
    const TOTAL_FEE_BPS: u256 = 30;     // 0.3%
    const TEAM_FEE_BPS: u256 = 9;       // 0.09%
    const LOCKER_FEE_BPS: u256 = 3;     // 0.03%
    const BUYBACK_FEE_BPS: u256 = 3;    // 0.03%
    const LP_FEE_BPS: u256 = 15;        // 0.15%

    const ONE: u256 = 1000000000;  // 1 with 9 decimals

    const QUADRILLION: u256 = 1_000_000_000_000_000;
    const QUINTILLION: u256 = 1_000_000_000_000_000_000;
    const SEXTILLION: u256 = 1_000_000_000_000_000_000_000;

    const MAX_U256: u256 = 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff;
    const HALF_MAX_U256: u256 = 0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff;
    const DECILLION: u256 = 1_000_000_000_000_000_000_000_000_000_000_000; // 10^33

    #[test]
    fun test_fee_breakdown() {
        let amount: u256 = 10000 * ONE;  // 10000 tokens with 9 decimals
        let (team_fee, locker_fee, buyback_fee, lp_fee) = library::compute_fee_amounts(amount);
        
        // Calculate expected fees with high precision
        let total_fee_amount = (amount * TOTAL_FEE_BPS) / BASIS_POINTS;
        
        debug::print(&b"Test amount:");
        debug::print(&amount);
        debug::print(&b"Total fee amount:");
        debug::print(&total_fee_amount);
        debug::print(&b"Actual fees (team/locker/buyback/lp):");
        debug::print(&team_fee);
        debug::print(&locker_fee);
        debug::print(&buyback_fee);
        debug::print(&lp_fee);

        // Verify individual fees
        assert!(team_fee == (total_fee_amount * TEAM_FEE_BPS) / TOTAL_FEE_BPS, 0);
        assert!(locker_fee == (total_fee_amount * LOCKER_FEE_BPS) / TOTAL_FEE_BPS, 1);
        assert!(buyback_fee == (total_fee_amount * BUYBACK_FEE_BPS) / TOTAL_FEE_BPS, 2);
        assert!(lp_fee == (total_fee_amount * LP_FEE_BPS) / TOTAL_FEE_BPS, 3);
    }

    

    #[test]
    fun test_get_amount_out_small() {
        let amount_in = 1000 * ONE;  // 1000 tokens
        let reserve = 1000000 * ONE; // 1M tokens for both reserves
        
        let amount_out = library::get_amount_out(amount_in, reserve, reserve);
        
        // Calculate minimum expected output (considering price impact)
        let min_output = (amount_in * 996) / 1000; // 99.6% after fees and slight price impact
        
        debug::print(&b"Input amount:");
        debug::print(&amount_in);
        debug::print(&b"Output amount:");
        debug::print(&amount_out);
        debug::print(&b"Min expected:");
        debug::print(&min_output);

        assert!(amount_out > 0, 0);
        assert!(amount_out >= min_output, 1);
        assert!(amount_out < amount_in, 2); // Should be less than input due to fees
    }

    #[test]
    fun test_get_amount_in_small() {
        let amount_out = 1000 * ONE;  // Want 1000 tokens out
        let reserve = 1000000 * ONE;  // 1M tokens for both reserves
        
        let amount_in = library::get_amount_in(amount_out, reserve, reserve);
        
        // Calculate maximum expected input with adjusted tolerance
        let max_input = (amount_out * 1005) / 1000; // Allow up to 100.5% for fees and slippage
        
        debug::print(&b"Desired output:");
        debug::print(&amount_out);
        debug::print(&b"Required input:");
        debug::print(&amount_in);
        debug::print(&b"Max expected input:");
        debug::print(&max_input);

        assert!(amount_in > amount_out, 0);  // Must input more than output due to fee
        assert!(amount_in <= max_input, 1);  // Shouldn't require more than maximum
    }

    #[test]
    fun test_price_impact() {
        let reserve = 1000000 * ONE;  // 1M token reserve
        
        // Use much smaller percentages to show price impact
        let small_trade = reserve / 10000;    // 0.01% of reserve
        let medium_trade = reserve / 1000;    // 0.1% of reserve
        let large_trade = reserve / 100;      // 1% of reserve
        
        let small_out = library::get_amount_out(small_trade, reserve, reserve);
        let medium_out = library::get_amount_out(medium_trade, reserve, reserve);
        let large_out = library::get_amount_out(large_trade, reserve, reserve);
        
        // Calculate effective output rates
        let small_rate = (small_out * BASIS_POINTS) / small_trade;
        let medium_rate = (medium_out * BASIS_POINTS) / medium_trade;
        let large_rate = (large_out * BASIS_POINTS) / large_trade;
        
        debug::print(&b"Trade amounts (small/medium/large):");
        debug::print(&small_trade);
        debug::print(&medium_trade);
        debug::print(&large_trade);
        debug::print(&b"Output rates (small/medium/large):");
        debug::print(&small_rate);
        debug::print(&medium_rate);
        debug::print(&large_rate);

        // Smaller trades should have better rates
        assert!(small_rate >= medium_rate, 0);
        assert!(medium_rate >= large_rate, 1);
    }

    #[test]
    fun test_extreme_values() {
        // Test with huge amounts (simulating meme coins with 18 decimals)
        let amount_in = QUINTILLION;  // 1 quintillion tokens
        let reserve = SEXTILLION;     // 1 sextillion tokens in reserve
        
        // Test get_amount_out with extreme values
        let amount_out = library::get_amount_out(amount_in, reserve, reserve);
        
        // Calculate expected minimum output (after 0.3% fee)
        let min_expected_out = (amount_in * 996) / 1000; // 99.6% considering fee and minor price impact
        
        debug::print(&b"Extreme Value Swap Test:");
        debug::print(&b"Input amount (quintillion):");
        debug::print(&amount_in);
        debug::print(&b"Reserve size (sextillion):");
        debug::print(&reserve);
        debug::print(&b"Output amount:");
        debug::print(&amount_out);
        debug::print(&b"Minimum expected:");
        debug::print(&min_expected_out);

        // Verify output is within expected range
        assert!(amount_out > 0, 0);
        assert!(amount_out >= min_expected_out, 1);
        assert!(amount_out < amount_in, 2); // Should be less than input due to fees

        // Test fee computation with large numbers
        let (team_fee, locker_fee, buyback_fee, lp_fee) = library::compute_fee_amounts(amount_in);

        debug::print(&b"Fee breakdown for huge amount:");
        debug::print(&b"Team fee (0.06%):");
        debug::print(&team_fee);
        debug::print(&b"Locker fee (0.03%):");
        debug::print(&locker_fee);
        debug::print(&b"Buyback fee (0.03%):");
        debug::print(&buyback_fee);
        debug::print(&b"LP fee (0.18%):");
        debug::print(&lp_fee);

        // Verify fees add up correctly
        let total_fee = team_fee + locker_fee + buyback_fee + lp_fee;
        let expected_total_fee = (amount_in * 30) / 10000; // 0.3%
        assert!(total_fee == expected_total_fee, 3);

        // Test get_amount_in with extreme values
        let huge_output = QUINTILLION;  // Want 1 quintillion tokens out
        let amount_in_required = library::get_amount_in(huge_output, reserve, reserve);
        
        // Expected input should be around 100.3% of output due to fees
        let max_input_expected = (huge_output * 1005) / 1000; // Allow up to 100.5% for fees + slippage

        debug::print(&b"Extreme Value Get Amount In Test:");
        debug::print(&b"Desired output (quintillion):");
        debug::print(&huge_output);
        debug::print(&b"Required input:");
        debug::print(&amount_in_required);
        debug::print(&b"Max expected input:");
        debug::print(&max_input_expected);

        assert!(amount_in_required > huge_output, 4); // Must be more than output due to fees
        assert!(amount_in_required <= max_input_expected, 5); // Shouldn't exceed max expected

        // Test price impact with large values
        let large_trade = reserve / 100;     // 1% of reserve
        let larger_trade = reserve / 10;     // 10% of reserve
        let largest_trade = reserve / 4;     // 25% of reserve

        let out1 = library::get_amount_out(large_trade, reserve, reserve);
        let out2 = library::get_amount_out(larger_trade, reserve, reserve);
        let out3 = library::get_amount_out(largest_trade, reserve, reserve);

        debug::print(&b"Large trades price impact:");
        debug::print(&b"1% trade effective rate:");
        debug::print(&((out1 * 10000) / large_trade));
        debug::print(&b"10% trade effective rate:");
        debug::print(&((out2 * 10000) / larger_trade));
        debug::print(&b"25% trade effective rate:");
        debug::print(&((out3 * 10000) / largest_trade));

        // Verify price impact increases with size
        let rate1 = (out1 * 10000) / large_trade;
        let rate2 = (out2 * 10000) / larger_trade;
        let rate3 = (out3 * 10000) / largest_trade;
        
        assert!(rate1 >= rate2, 6);
        assert!(rate2 >= rate3, 7);
    }

    #[test]
    fun test_consecutive_large_swaps() {
        let initial_reserve = SEXTILLION;
        let mut reserve_in = initial_reserve;
        let mut reserve_out = initial_reserve;
        let swap_amount = QUINTILLION;

        debug::print(&b"Consecutive large swaps test:");
        debug::print(&b"Initial reserves:");
        debug::print(&initial_reserve);
        debug::print(&b"Swap amount:");
        debug::print(&swap_amount);

        let mut i = 0;
        while (i < 5) {
            let out_amount = library::get_amount_out(swap_amount, reserve_in, reserve_out);
            
            debug::print(&b"Swap number:");
            debug::print(&i);
            debug::print(&b"Output amount:");
            debug::print(&out_amount);
            debug::print(&b"Effective rate:");
            debug::print(&((out_amount * 10000) / swap_amount));

            reserve_in = reserve_in + swap_amount;
            reserve_out = reserve_out - out_amount;

            assert!(out_amount > 0, 0);
            assert!(reserve_out > initial_reserve / 2, 1); // Should never drain more than 50%
            
            i = i + 1;
        };
    }
    #[test]
    fun test_maximum_possible_values() {
        let initial_reserve = DECILLION * 1000;  // 10^36
        let swap_amount = DECILLION;             // 10^33
        
        debug::print(&b"Maximum value test:");
        debug::print(&b"Initial reserve (10^36):");
        debug::print(&initial_reserve);
        debug::print(&b"Swap amount (10^33):");
        debug::print(&swap_amount);

        // Test 1: Extreme swap
        let amount_out = library::get_amount_out(swap_amount, initial_reserve, initial_reserve);
        debug::print(&b"Output amount for extreme swap:");
        debug::print(&amount_out);
        debug::print(&b"Effective rate:");
        debug::print(&((amount_out * 10000) / swap_amount));

        // Test 2: Fee distribution at extreme values
        let (team_fee, locker_fee, buyback_fee, lp_fee) = library::compute_fee_amounts(swap_amount);
        
        debug::print(&b"Fee breakdown for extreme amount:");
        debug::print(&b"Team fee (0.06%):");
        debug::print(&team_fee);
        debug::print(&b"Locker fee (0.03%):");
        debug::print(&locker_fee);
        debug::print(&b"Buyback fee (0.03%):");
        debug::print(&buyback_fee);
        debug::print(&b"LP fee (0.18%):");
        debug::print(&lp_fee);

        // Test 3: Multiple large swaps simulation
        let mut reserve_in = initial_reserve;
        let mut reserve_out = initial_reserve;

        debug::print(&b"Sequential extreme swaps:");
        let mut i = 0;
        while (i < 3) {
            let out = library::get_amount_out(swap_amount, reserve_in, reserve_out);
            debug::print(&b"Swap:");
            debug::print(&i);
            debug::print(&b"Output:");
            debug::print(&out);
            debug::print(&b"Rate:");
            debug::print(&((out * 10000) / swap_amount));

            reserve_in = reserve_in + swap_amount;
            reserve_out = reserve_out - out;
            i = i + 1;
        };

        // Test 4: Maximum possible input test
        let max_safe_input = initial_reserve / 2;  // 50% of reserve
        let huge_out = library::get_amount_out(max_safe_input, initial_reserve, initial_reserve);
        
        debug::print(&b"Maximum safe input test (50% of reserve):");
        debug::print(&b"Input amount:");
        debug::print(&max_safe_input);
        debug::print(&b"Output amount:");
        debug::print(&huge_out);
        debug::print(&b"Price impact (bps):");
        debug::print(&(10000 - (huge_out * 10000) / max_safe_input));

        // Verify all values are within safe bounds
        assert!(huge_out < initial_reserve, 0);
        assert!(huge_out > 0, 1);
        
        // Verify fee calculations remain accurate at extreme values
        let total_fee = team_fee + locker_fee + buyback_fee + lp_fee;
        let expected_total_fee = (swap_amount * 30) / 10000;
        assert!(total_fee == expected_total_fee, 2);

        // Verify price impact increases proportionally
        let small_rate = ((amount_out * 10000) / swap_amount);
        let large_rate = ((huge_out * 10000) / max_safe_input);
        assert!(small_rate >= large_rate, 3);
    }

    #[test]
    /// Test that the K-invariant is preserved after the fix
    /// This test should PASS with the corrected get_amount_out function
    fun test_k_invariant_preservation_after_fix() {
        // Test multiple trade sizes to ensure invariant holds
        let reserve: u256 = 1_000_000 * ONE; // 1M tokens on each side
        
        // Test cases: small (0.1%), medium (5%), large (20%) trades
        let test_amounts = vector[
            reserve / 1000,  // 0.1% trade
            reserve / 20,    // 5% trade  
            reserve / 5      // 20% trade (aggressive but realistic)
        ];
        
        let mut i = 0;
        while (i < vector::length(&test_amounts)) {
            let amount_in = *vector::borrow(&test_amounts, i);
            
            // Get output using FIXED function
            let amount_out = library::get_amount_out(amount_in, reserve, reserve);
            
            // Calculate K values before and after
            let k_before = reserve * reserve;
            let new_reserve_in = reserve + amount_in;
            let new_reserve_out = reserve - amount_out;
            let k_after = new_reserve_in * new_reserve_out;
            
            debug::print(&b"K-Invariant Test - Trade Size:");
            debug::print(&((amount_in * 10000) / reserve)); // Trade size in basis points
            debug::print(&b"K before:");
            debug::print(&k_before);
            debug::print(&b"K after:");
            debug::print(&k_after);
            debug::print(&b"K ratio (should be >= 1.0):");
            debug::print(&((k_after * 10000) / k_before));
            
            // CRITICAL: K should never decrease (invariant preservation)
            assert!(k_after >= k_before, i);
            
            // For trades with fees, K should actually increase slightly
            if (amount_in > reserve / 10000) { // For trades > 0.01%
                assert!(k_after > k_before, i + 100);
            };
            
            i = i + 1;
        };
    }

    #[test]
    /// Test rounding behavior in get_amount_in to prevent micro-theft
    /// Verifies that users always pay sufficient amount (no underpayment)
    fun test_rounding_prevents_micro_theft() {
        let reserve: u256 = 1_000_000 * ONE;
        
        // Test with amounts that would cause rounding issues
        let test_outputs = vector[
            3,      // Tiny amount that tests rounding edge cases
            7,      // Prime number that doesn't divide evenly
            999,    // Just under 1000
            1001,   // Just over 1000
            reserve / 123456, // Irregular fraction of reserve
        ];
        
        let mut total_underpayment = 0u256;
        let mut i = 0;
        
        while (i < vector::length(&test_outputs)) {
            let amount_out = *vector::borrow(&test_outputs, i);
            
            // Get required input using FIXED function (with rounding up)
            let amount_in_required = library::get_amount_in(amount_out, reserve, reserve);
            
            // Simulate what the old (broken) function would return
            let numerator = reserve * amount_out * BASIS_POINTS;
            let denominator = (reserve - amount_out) * (BASIS_POINTS - TOTAL_FEE_BPS);
            let old_amount_in = numerator / denominator; // WITHOUT rounding up
            
            // Calculate potential underpayment
            let underpayment = if (amount_in_required > old_amount_in) {
                amount_in_required - old_amount_in
            } else {
                0
            };
            
            total_underpayment = total_underpayment + underpayment;
            
            debug::print(&b"Rounding Test - Output Amount:");
            debug::print(&amount_out);
            debug::print(&b"Fixed required input:");
            debug::print(&amount_in_required);
            debug::print(&b"Old (broken) required input:");
            debug::print(&old_amount_in);
            debug::print(&b"Prevented underpayment:");
            debug::print(&underpayment);
            
            // CRITICAL: Fixed version should require at least as much as old version
            assert!(amount_in_required >= old_amount_in, i);
            
            // Verify the input actually produces sufficient output
            let actual_output = library::get_amount_out(amount_in_required, reserve, reserve);
            assert!(actual_output >= amount_out, i + 100);
            
            i = i + 1;
        };
        
        debug::print(&b"Total prevented micro-theft across all test cases:");
        debug::print(&total_underpayment);
        
        // In a high-volume DEX, this prevented theft adds up significantly
        assert!(total_underpayment > 0, 999); // Should prevent some underpayment
    }

    #[test]
    fun test_swap_fee_and_precision_loss() {
        let reserve = 1_000_000 * ONE; // 1M tokens, symmetric reserves

        // Step 1: Perform a swap with known input
        let input_amount = 1_000 * ONE; // 1000 tokens in
        let output_amount = library::get_amount_out(input_amount, reserve, reserve);

        // Step 2: Invert it: use output_amount as target, get required input
        let required_input = library::get_amount_in(output_amount, reserve, reserve);

        debug::print(&b"Input → Output → Input roundtrip:");
        debug::print(&b"Initial input amount:");
        debug::print(&input_amount);
        debug::print(&b"Output received:");
        debug::print(&output_amount);
        debug::print(&b"Input needed to get that output:");
        debug::print(&required_input);

        // Step 3: Compute discrepancy due to fee + rounding
        let loss = if (required_input > input_amount) {
            required_input - input_amount
        } else {
            input_amount - required_input
        };

        debug::print(&b"Effective fee or rounding delta:");
        debug::print(&loss);

        // Step 4: Expectations
        assert!(output_amount < input_amount, 100); // Output must be less (due to fee)
        assert!(required_input >= input_amount, 101); // Reverse input should be more or equal
        assert!(loss < (5 * ONE), 102); // Rounding error should be less than 5 tokens (adjust as needed)
    }

    #[test]
    #[expected_failure(abort_code = 201)] // ERR_INSUFFICIENT_AMOUNT
    fun test_micro_transaction_blocked() {
        library::get_amount_out(100, 1000000000, 1000000000); // Should fail
    }

    const THOUSAND: u256 = 1000;
    const MILLION: u256 = 1_000_000;
    const BILLION: u256 = 1_000_000_000;
    const TRILLION: u256 = 1_000_000_000_000;
    const HUNDRED_TRILLION: u256 = 100_000_000_000_000;

    #[test]
    fun test_extreme_rounding_stress_scenarios() {
        debug::print(&b"=== EXTREME ROUNDING STRESS TEST ===");
        debug::print(&b"Testing edge cases where rounding inconsistencies are most likely");
        
        // Test Case 1: Small amounts above minimum trade size
        debug::print(&b"");
        debug::print(&b"🔍 TEST 1: Small amounts (above MIN_TRADE_SIZE)");
        test_roundtrip_scenario(
            10000u256,     // 10K input (above minimum 1000)
            TRILLION,      // 1T reserve_in (huge)
            TRILLION,      // 1T reserve_out (huge)
            b"Small vs Huge Reserves"
        );
        
        // Test Case 2: Moderate reserve imbalance (realistic)
        debug::print(&b"");
        debug::print(&b"🔍 TEST 2: Moderate reserve imbalance");
        test_roundtrip_scenario(
            100 * MILLION,     // 100M input
            10 * TRILLION,     // 10T reserve_in
            100 * MILLION,     // 100M reserve_out (100:1 ratio)
            b"Moderate Imbalance 10T:100M"
        );
        
        // Test Case 3: Small reserves with proportional input
        debug::print(&b"");
        debug::print(&b"🔍 TEST 3: Small reserves with proportional input");
        test_roundtrip_scenario(
            50000u256,     // 50K input
            10 * MILLION,  // 10M reserve_in (small but realistic)
            10 * MILLION,  // 10M reserve_out (small but realistic)
            b"Proportional small reserves"
        );
        
        // Test Case 4: Boundary condition - moderate slippage
        debug::print(&b"");
        debug::print(&b"🔍 TEST 4: Moderate slippage scenario");
        test_roundtrip_scenario(
            1 * MILLION,   // 1M input  
            10 * MILLION,  // 10M reserve_in (10% of reserve)
            10 * MILLION,  // 10M reserve_out
            b"Moderate slippage scenario"
        );
        
        // Test Case 5: Odd numbers that might cause rounding issues
        debug::print(&b"");
        debug::print(&b"🔍 TEST 5: Odd numbers prone to rounding");
        test_roundtrip_scenario(
            1234567u256,   // Odd input
            9876543u256,   // Odd reserve_in
            8765432u256,   // Odd reserve_out
            b"Odd numbers everywhere"
        );
        
        // Test Case 6: Prime numbers (mathematically challenging)
        debug::print(&b"");
        debug::print(&b"🔍 TEST 6: Prime numbers");
        test_roundtrip_scenario(
            982451u256,    // Prime input
            15485863u256,  // Prime reserve_in
            17299009u256,  // Prime reserve_out
            b"Prime number challenge"
        );
        
        // Test Case 7: Powers of 2 (binary boundary conditions)
        debug::print(&b"");
        debug::print(&b"🔍 TEST 7: Powers of 2");
        test_roundtrip_scenario(
            1048576u256,   // 2^20
            16777216u256,  // 2^24
            33554432u256,  // 2^25
            b"Powers of 2"
        );
        
        // Test Case 8: Large scale realistic amounts
        debug::print(&b"");
        debug::print(&b"🔍 TEST 8: Large scale realistic amounts");
        test_roundtrip_scenario(
            10 * BILLION,  // 10B input
            HUNDRED_TRILLION,  // 100T reserve_in
            HUNDRED_TRILLION,  // 100T reserve_out  
            b"Large scale realistic"
        );
        
        debug::print(&b"");
        debug::print(&b"📊 STRESS TEST ANALYSIS COMPLETE");
        debug::print(&b"If all scenarios show delta=0 or minimal delta, no vulnerability exists");
    }

    fun test_roundtrip_scenario(
        input_amount: u256,
        reserve_in: u256, 
        reserve_out: u256,
        scenario_name: vector<u8>
    ) {
        debug::print(&scenario_name);
        debug::print(&b"Input amount:");
        debug::print(&input_amount);
        debug::print(&b"Reserve in:");
        debug::print(&reserve_in);
        debug::print(&b"Reserve out:");
        debug::print(&reserve_out);
        
        // Step 1: input -> output
        let output_amount = library::get_amount_out(input_amount, reserve_in, reserve_out);
        debug::print(&b"Output amount:");
        debug::print(&output_amount);
        
        // Step 2: output -> required input (roundtrip)
        let required_input = library::get_amount_in(output_amount, reserve_in, reserve_out);
        debug::print(&b"Required input for that output:");
        debug::print(&required_input);
        
        // Step 3: Calculate delta
        let delta = if (required_input > input_amount) {
            required_input - input_amount
        } else {
            input_amount - required_input
        };
        
        debug::print(&b"Roundtrip delta:");
        debug::print(&delta);
        
        // Step 4: Calculate relative error as percentage
        let relative_error_bp = if (input_amount > 0) {
            (delta * 10000) / input_amount  // Basis points
        } else {
            0
        };
        
        debug::print(&b"Relative error (basis points):");
        debug::print(&relative_error_bp);
        
        // Step 5: Analyze the results
        if (delta == 0) {
            debug::print(&b"✅ PERFECT: Zero rounding error");
        } else if (relative_error_bp <= 1) {
            debug::print(&b"✅ EXCELLENT: <0.01% error");
        } else if (relative_error_bp <= 10) {
            debug::print(&b"⚠️ ACCEPTABLE: <0.1% error");
        } else {
            debug::print(&b"❌ CONCERNING: >0.1% error");
        };
        
        debug::print(&b"---");
    }

    #[test] 
    fun test_sequential_operations_drift() {
        debug::print(&b"=== TESTING SEQUENTIAL OPERATIONS DRIFT ===");
        debug::print(&b"Multiple swaps in sequence to detect cumulative rounding drift");
        
        let mut current_amount = 1000 * MILLION; // Start with 1B tokens
        let reserve_in = 10 * BILLION;           // 10B reserve
        let reserve_out = 10 * BILLION;          // 10B reserve
        
        debug::print(&b"Starting amount:");
        debug::print(&current_amount);
        debug::print(&b"Reserves (both):");
        debug::print(&reserve_in);
        
        // Perform 10 round-trips and track drift
        let mut i = 0;
        let mut max_drift = 0u256;
        let mut total_drift = 0u256;
        
        while (i < 10) {
            debug::print(&b"");
            debug::print(&b"Iteration:");
            debug::print(&(i + 1));
            
            let original_amount = current_amount;
            
            // Forward: amount -> output
            let output = library::get_amount_out(current_amount, reserve_in, reserve_out);
            
            // Reverse: output -> required input
            let required_input = library::get_amount_in(output, reserve_in, reserve_out);
            
            let drift = if (required_input > original_amount) {
                required_input - original_amount
            } else {
                original_amount - required_input
            };
            
            debug::print(&b"Original:");
            debug::print(&original_amount);
            debug::print(&b"After roundtrip:");
            debug::print(&required_input);
            debug::print(&b"Drift:");
            debug::print(&drift);
            
            // Track maximum and total drift
            if (drift > max_drift) {
                max_drift = drift;
            };
            total_drift = total_drift + drift;
            
            // Use required_input for next iteration to simulate sequential swaps
            current_amount = required_input;
            i = i + 1;
        };
        
        debug::print(&b"");
        debug::print(&b"📊 SEQUENTIAL DRIFT ANALYSIS:");
        debug::print(&b"Maximum single drift:");
        debug::print(&max_drift);
        debug::print(&b"Total accumulated drift:");
        debug::print(&total_drift);
        debug::print(&b"Average drift per operation:");
        debug::print(&(total_drift / 10));
        
        // Assessment
        if (max_drift == 0) {
            debug::print(&b"✅ PERFECT: No drift detected");
        } else if (max_drift < 1000) {
            debug::print(&b"✅ EXCELLENT: Drift < 1000 units"); 
        } else if (max_drift < 10000) {
            debug::print(&b"⚠️ ACCEPTABLE: Drift < 10000 units");
        } else {
            debug::print(&b"❌ CONCERNING: Significant drift detected");
        };
    }

    #[test]
    fun test_precision_edge_cases() {
        debug::print(&b"=== PRECISION EDGE CASES ===");
        debug::print(&b"Testing scenarios most likely to expose precision issues");
        
        // Edge Case 1: Minimum trade size (exactly at limit)
        debug::print(&b"");
        debug::print(&b"🔍 EDGE CASE 1: Minimum trade size (1000 units)");
        test_roundtrip_scenario(1000u256, BILLION, BILLION, b"Minimum trade size");
        
        // Edge Case 2: Just above minimum trade size
        debug::print(&b"");  
        debug::print(&b"🔍 EDGE CASE 2: Just above minimum trade size");
        test_roundtrip_scenario(1001u256, BILLION, BILLION, b"Just above minimum");
        
        // Edge Case 3: Fee boundary (where fee calculation matters)
        debug::print(&b"");
        debug::print(&b"🔍 EDGE CASE 3: Fee calculation boundary");
        let fee_boundary = 100000u256; // Amount where fee impact is meaningful
        test_roundtrip_scenario(fee_boundary, BILLION, BILLION, b"Fee boundary");
        
        // Edge Case 4: Moderate input with smaller reserves
        debug::print(&b"");
        debug::print(&b"🔍 EDGE CASE 4: Moderate input, smaller reserves");
        test_roundtrip_scenario(100000u256, 10 * MILLION, 10 * MILLION, b"Moderate input, smaller reserves");
        
        // Edge Case 5: Reserves differ slightly
        debug::print(&b"");
        debug::print(&b"🔍 EDGE CASE 5: Slightly different reserves");
        test_roundtrip_scenario(50000u256, 10 * MILLION, 11 * MILLION, b"Slightly different reserves");
        
        debug::print(&b"");
        debug::print(&b"📊 Edge case testing complete");
    }
}