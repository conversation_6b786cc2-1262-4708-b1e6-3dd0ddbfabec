#[test_only]
module suitrump_dex::test_coins {
    use std::option;
    use sui::coin::{Self, TreasuryCap, CoinMetadata};
    use sui::transfer;
    use sui::tx_context::TxContext;
    use sui::test_utils;

    // Original tokens
    public struct USDC has drop {}
    public struct WETH has drop {}
    public struct USDT has drop {}
    public struct STK1 has drop {}
    public struct STK5 has drop {}
    public struct STK10 has drop {}

    // New tokens for multi-hop exploit testing (ordered alphabetically)
    public struct ATOKEN has drop {}  // First alphabetically
    public struct BTOKEN has drop {}  // Second
    public struct MTOKEN has drop {}  // Middle token for multi-hop
    public struct TTOKEN has drop {}  // Fourth  
    public struct UTOKEN has drop {}  // Fifth
    public struct WTOKEN has drop {}  // Sixth
    public struct ZTOKEN has drop {}  // Last alphabetically

    public struct NAVX has drop {}  // New token for NAVX

    #[test_only]
    public fun init_for_testing(ctx: &mut TxContext) {
        // Initialize any necessary setup for test tokens
    }
}