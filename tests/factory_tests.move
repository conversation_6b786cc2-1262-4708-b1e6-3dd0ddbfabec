#[test_only]
module suitrump_dex::factory_tests {
    use sui::test_scenario::{Self as ts, <PERSON><PERSON><PERSON>};
    use std::string::utf8;
    use std::option;
    use sui::test_utils::assert_eq;
    use suitrump_dex::factory::{Self, Factory};
    use suitrump_dex::pair::{Self, AdminCap};
    use suitrump_dex::test_coins::{Self, USDC};

    const ADMIN: address = @0x1;
    const USER: address = @0x2;
    const TEAM: address = @0x44;
    const LOCKER: address = @0x45;
    const BUYBACK: address = @0x46;

    // Add constants for large number testing
    const BILLION: u128 = 1_000_000_000;
    const TRILLION: u128 = 1_000_000_000_000;

    fun setup(scenario: &mut Scenario) {
        ts::next_tx(scenario, ADMIN);
        {
            factory::init_for_testing(ts::ctx(scenario));
            pair::init_for_testing(ts::ctx(scenario));
        };
    }

    #[test]
    fun test_create_pair() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);

            // ✅ FIXED: Use correct alphabetical order (USDC < SUI)
            let pair_addr = factory::create_pair<USDC, sui::sui::SUI>(
                &mut factory,
                utf8(b"USDC"),  // T0 name (USDC comes first alphabetically)
                utf8(b"SUI"),   // T1 name (SUI comes second alphabetically)
                ts::ctx(&mut scenario)
            );

            // ✅ FIXED: Use same order when getting pair
            let mut existing_pair = factory::get_pair<USDC, sui::sui::SUI>(&factory);
            assert!(option::is_some(&existing_pair), 1);
            assert!(option::extract(&mut existing_pair) == pair_addr, 2);
            assert_eq(factory::all_pairs_length(&factory), 1);

            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };
        ts::end(scenario);
    }

    #[test]
    fun test_pair_name_and_symbol() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);

            // ✅ FIXED: Create pair with correct alphabetical order
            factory::create_pair<USDC, sui::sui::SUI>(
                &mut factory,
                utf8(b"USDC"),  // T0 = USDC (first alphabetically)
                utf8(b"SUI"),   // T1 = SUI (second alphabetically)
                ts::ctx(&mut scenario)
            );

            ts::next_tx(&mut scenario, ADMIN);
            {
                // ✅ FIXED: Take pair with correct type order
                let pair = ts::take_shared<pair::Pair<USDC, sui::sui::SUI>>(&scenario);
                
                // ✅ UPDATED: Expected name reflects the token order passed to create_pair
                // The pair::new function creates name as "Suitrump V2 {token0_name}/{token1_name}"
                // Since we pass USDC first, then SUI, the name should be "Suitrump V2 USDC/SUI"
                assert_eq(pair::get_name(&pair), utf8(b"Suitrump V2 USDC/SUI"));
                assert_eq(pair::get_symbol(&pair), utf8(b"SUIT-V2"));
                
                ts::return_shared(pair);
            };

            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };
        ts::end(scenario);
    }

    #[test]
    fun test_get_pair() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);

            // ✅ FIXED: Create pair with correct alphabetical order
            let pair_addr = factory::create_pair<USDC, sui::sui::SUI>(
                &mut factory,
                utf8(b"USDC"),  // T0 = USDC (first alphabetically)
                utf8(b"SUI"),   // T1 = SUI (second alphabetically)
                ts::ctx(&mut scenario)
            );

            // ✅ FIXED: Only correct order lookup (USDC < SUI)
            let mut existing_pair = factory::get_pair<USDC, sui::sui::SUI>(&factory);
            assert!(option::is_some(&existing_pair), 1);
            assert!(option::extract(&mut existing_pair) == pair_addr, 2);

            // ❌ REMOVED: Reverse lookup no longer possible after auditor's fix
            // After the fix, factory::get_pair<sui::sui::SUI, USDC> would abort
            // because sort_tokens() would abort on backwards order
            
            // ✅ NEW: Test that backwards lookup fails (optional)
            // This demonstrates that the fix prevents backwards access

            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };
        ts::end(scenario);
    }

    // ✅ OPTIONAL: Add separate test to verify backwards lookup fails
    #[test]
    #[expected_failure(abort_code = 7)] // ERROR_PAIR_NOT_SORTED
    fun test_get_pair_backwards_fails() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);

            // Create pair with correct order
            factory::create_pair<USDC, sui::sui::SUI>(
                &mut factory,
                utf8(b"USDC"),
                utf8(b"SUI"),
                ts::ctx(&mut scenario)
            );

            // This should abort with ERROR_PAIR_NOT_SORTED
            let _reverse_pair = factory::get_pair<sui::sui::SUI, USDC>(&factory);

            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };
        ts::end(scenario);
    }

    #[test]
    #[expected_failure(abort_code = 1)]
    fun test_create_identical_tokens() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);

            factory::create_pair<USDC, USDC>(
                &mut factory,
                utf8(b"USDC"),
                utf8(b"USDC"),
                ts::ctx(&mut scenario)
            );

            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };
        ts::end(scenario);
    }

    #[test]
    #[expected_failure(abort_code = 2)]
    fun test_create_existing_pair() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);

            // ✅ FIXED: Create first pair with correct alphabetical order
            factory::create_pair<USDC, sui::sui::SUI>(
                &mut factory,
                utf8(b"USDC"),  // T0 name (USDC comes first alphabetically)
                utf8(b"SUI"),   // T1 name (SUI comes second alphabetically)
                ts::ctx(&mut scenario)
            );

            // ✅ FIXED: Attempt to create same pair again (should fail with ERROR_PAIR_EXISTS)
            factory::create_pair<USDC, sui::sui::SUI>(
                &mut factory,
                utf8(b"USDC"),
                utf8(b"SUI"),
                ts::ctx(&mut scenario)
            );

            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };
        ts::end(scenario);
    }

    #[test]
    fun test_multiple_pairs() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);

            // ✅ FIXED: Create first pair with correct alphabetical order
            let pair1_addr = factory::create_pair<USDC, sui::sui::SUI>(
                &mut factory,
                utf8(b"USDC"),  // T0 = USDC (first alphabetically)
                utf8(b"SUI"),   // T1 = SUI (second alphabetically)
                ts::ctx(&mut scenario)
            );

            // ✅ FIXED: Verify first pair with same order
            let mut existing_pair = factory::get_pair<USDC, sui::sui::SUI>(&factory);
            assert!(option::extract(&mut existing_pair) == pair1_addr, 1);
            assert_eq(factory::all_pairs_length(&factory), 1);

            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };

        ts::end(scenario);
    }

    #[test]
    fun test_robust_sorting_implementation() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);

            // Test 1: Create pair with correct alphabetical order (USDC < SUI)
            let pair_addr = factory::create_pair<USDC, sui::sui::SUI>(
                &mut factory,
                utf8(b"USDC"),
                utf8(b"SUI"),
                ts::ctx(&mut scenario)
            );

            // Test 2: Verify the pair can be found with correct order
            let mut existing_pair = factory::get_pair<USDC, sui::sui::SUI>(&factory);
            assert!(option::is_some(&existing_pair), 1);
            assert!(option::extract(&mut existing_pair) == pair_addr, 2);

            // Test 3: Verify robust comparison worked - only 1 pair exists
            assert_eq(factory::all_pairs_length(&factory), 1);

            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };

        ts::end(scenario);
    }

    #[test]
    fun test_robust_sorting_consistency() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);

            // Test 1: Create pair and verify it exists
            let pair_addr = factory::create_pair<USDC, sui::sui::SUI>(
                &mut factory,
                utf8(b"USDC"),
                utf8(b"SUI"),
                ts::ctx(&mut scenario)
            );

            // Test 2: Multiple consecutive lookups should return same result
            let lookup1 = factory::get_pair<USDC, sui::sui::SUI>(&factory);
            let lookup2 = factory::get_pair<USDC, sui::sui::SUI>(&factory);
            let lookup3 = factory::get_pair<USDC, sui::sui::SUI>(&factory);
            
            // All lookups should find the pair
            assert!(option::is_some(&lookup1), 1);
            assert!(option::is_some(&lookup2), 2);
            assert!(option::is_some(&lookup3), 3);
            
            // All should return the same address
            assert!(option::borrow(&lookup1) == option::borrow(&lookup2), 4);
            assert!(option::borrow(&lookup2) == option::borrow(&lookup3), 5);
            assert!(option::borrow(&lookup1) == &pair_addr, 6);

            // Test 3: Verify only one pair exists (no duplicates created)
            assert_eq(factory::all_pairs_length(&factory), 1);

            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };

        // Test 4: Cross-transaction consistency
        ts::next_tx(&mut scenario, USER); // Different user
        {
            let factory = ts::take_shared<Factory>(&scenario);
            
            // Same lookup from different transaction and user should work
            let cross_tx_lookup = factory::get_pair<USDC, sui::sui::SUI>(&factory);
            assert!(option::is_some(&cross_tx_lookup), 7);
            
            // Should still be only one pair
            assert_eq(factory::all_pairs_length(&factory), 1);
            
            ts::return_shared(factory);
        };

        // Test 5: Verify sorting determinism by attempting duplicate creation
        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);

            // This should fail because robust sorting produces same TokenPair key
            let pair_exists_before = factory::get_pair<USDC, sui::sui::SUI>(&factory);
            assert!(option::is_some(&pair_exists_before), 8);

            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };

        ts::end(scenario);
    }

    #[test]
    #[expected_failure(abort_code = 2)] // ERROR_PAIR_EXISTS
    fun test_robust_sorting_prevents_duplicates() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);

            // Create first pair
            factory::create_pair<USDC, sui::sui::SUI>(
                &mut factory,
                utf8(b"USDC"),
                utf8(b"SUI"),
                ts::ctx(&mut scenario)
            );

            // Verify it exists
            let first_pair = factory::get_pair<USDC, sui::sui::SUI>(&factory);
            assert!(option::is_some(&first_pair), 1);

            // Try to create same pair again - should fail with ERROR_PAIR_EXISTS
            // This proves our robust sorting produces consistent TokenPair keys
            factory::create_pair<USDC, sui::sui::SUI>(
                &mut factory,
                utf8(b"USD Coin"),  // Different name
                utf8(b"Sui Token"), // Different name
                ts::ctx(&mut scenario)
            );

            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };

        ts::end(scenario);
    }
}