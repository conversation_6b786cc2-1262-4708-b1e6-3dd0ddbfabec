#[test_only]
module suitrump_dex::lp_economics_tests {
    use sui::test_scenario::{Self as ts};
    use suitrump_dex::router::{Self, Router};
    use suitrump_dex::factory::{Self, Factory};
    use suitrump_dex::pair::{<PERSON>, Pair, <PERSON><PERSON>oin, AdminCap};
    use sui::coin::{Self, Coin, mint_for_testing};
    use sui::transfer;
    use std::string::utf8;
    use std::debug;
    use std::vector;
    use suitrump_dex::test_coins::{USDC, USDT, STK1, STK5};

    const ADMIN: address = @0x1;

    fun setup(scenario: &mut ts::Scenario) {
        ts::next_tx(scenario, ADMIN);
        {
            factory::init_for_testing(ts::ctx(scenario));
            router::init_for_testing(ts::ctx(scenario));
            pair::init_for_testing(ts::ctx(scenario));
        };
    }

    #[test]
    fun test_small_lp_economics() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        debug::print(&utf8(b"=== LP ECONOMICS: Small Amounts ==="));

        // Create pair
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);
            factory::create_pair<USDC, sui::sui::SUI>(&mut factory, utf8(b"USDC"), utf8(b"SUI"), ts::ctx(&mut scenario));
            ts::return_shared(router); ts::return_shared(factory); ts::return_to_sender(&scenario, cap);
        };

        // Add liquidity: 1 SUI + 2000 USDC (increased from 0.1 SUI)
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<USDC, sui::sui::SUI>>(&scenario);

            let sui_amount = 1_000_000_000u64;    // 1 SUI (increased for minimum)
            let usdc_amount = 2_000_000_000u64;   // 2000 USDC

            debug::print(&utf8(b"Input: 1 SUI + 2000 USDC"));
            debug::print(&utf8(b"SUI base units:")); debug::print(&(sui_amount as u256));
            debug::print(&utf8(b"USDC base units:")); debug::print(&(usdc_amount as u256));

            let coin_usdc = mint_for_testing<USDC>(usdc_amount, ts::ctx(&mut scenario));
            let coin_sui = mint_for_testing<sui::sui::SUI>(sui_amount, ts::ctx(&mut scenario));

            router::add_liquidity_for_testing(&router, &mut factory, &mut pair, coin_usdc, coin_sui,
                (usdc_amount as u256), (sui_amount as u256), (usdc_amount as u256), (sui_amount as u256),
                utf8(b"USDC"), utf8(b"SUI"), 18446744073709551615, ts::ctx(&mut scenario));

            let total_supply = pair::total_supply(&pair);
            debug::print(&utf8(b"LP tokens received:")); debug::print(&total_supply);

            ts::return_shared(router); ts::return_shared(factory); ts::return_shared(pair);
        };

        // Remove and analyze
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<USDC, sui::sui::SUI>>(&scenario);
            let lp_coin = ts::take_from_address<Coin<LPCoin<USDC, sui::sui::SUI>>>(&scenario, ADMIN);

            let (reserve0_before, reserve1_before, _) = pair::get_reserves(&pair);
            let lp_balance = coin::value(&lp_coin);

            debug::print(&utf8(b"Burning LP tokens:")); debug::print(&(lp_balance as u256));

            let mut lp_coins = vector::empty<Coin<LPCoin<USDC, sui::sui::SUI>>>();
            vector::push_back(&mut lp_coins, lp_coin);

            router::remove_liquidity_for_testing(&router, &factory, &mut pair, lp_coins, (lp_balance as u256),
                0, 0, 18446744073709551615, ts::ctx(&mut scenario));

            let (reserve0_after, reserve1_after, _) = pair::get_reserves(&pair);
            let usdc_recovered = reserve0_before - reserve0_after;
            let sui_recovered = reserve1_before - reserve1_after;

            debug::print(&utf8(b"USDC recovered:")); debug::print(&usdc_recovered);
            debug::print(&utf8(b"SUI recovered:")); debug::print(&sui_recovered);

            // Recovery rates
            let usdc_rate = (usdc_recovered * 10000) / (2_000_000_000u256);
            let sui_rate = (sui_recovered * 10000) / (1_000_000_000u256);
            debug::print(&utf8(b"USDC recovery % * 100:")); debug::print(&usdc_rate);
            debug::print(&utf8(b"SUI recovery % * 100:")); debug::print(&sui_rate);

            ts::return_shared(router); ts::return_shared(factory); ts::return_shared(pair);
        };
        ts::end(scenario);
    }

    #[test]
    fun test_whale_lp_economics() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        debug::print(&utf8(b"=== LP ECONOMICS: Whale Amounts ==="));

        // Create pair
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);
            factory::create_pair<USDC, sui::sui::SUI>(&mut factory, utf8(b"USDC"), utf8(b"SUI"), ts::ctx(&mut scenario));
            ts::return_shared(router); ts::return_shared(factory); ts::return_to_sender(&scenario, cap);
        };

        // Add whale liquidity: 10K SUI + 20M USDC
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<USDC, sui::sui::SUI>>(&scenario);

            let sui_amount = 10_000_000_000_000u64;   // 10K SUI
            let usdc_amount = 20_000_000_000_000u64;  // 20M USDC

            debug::print(&utf8(b"Whale input: 10K SUI + 20M USDC"));
            debug::print(&utf8(b"SUI base units:")); debug::print(&(sui_amount as u256));
            debug::print(&utf8(b"USDC base units:")); debug::print(&(usdc_amount as u256));

            let coin_usdc = mint_for_testing<USDC>(usdc_amount, ts::ctx(&mut scenario));
            let coin_sui = mint_for_testing<sui::sui::SUI>(sui_amount, ts::ctx(&mut scenario));

            router::add_liquidity_for_testing(&router, &mut factory, &mut pair, coin_usdc, coin_sui,
                (usdc_amount as u256), (sui_amount as u256), (usdc_amount as u256), (sui_amount as u256),
                utf8(b"USDC"), utf8(b"SUI"), 18446744073709551615, ts::ctx(&mut scenario));

            let total_supply = pair::total_supply(&pair);
            debug::print(&utf8(b"Whale LP tokens:")); debug::print(&total_supply);

            // LP efficiency: tokens per $1M (assuming $2000/SUI)
            let total_usd = 40_000_000u256; // 20M + (10K * $2000)
            let lp_per_million = (total_supply * 1_000_000u256) / total_usd;
            debug::print(&utf8(b"LP per $1M:")); debug::print(&lp_per_million);

            ts::return_shared(router); ts::return_shared(factory); ts::return_shared(pair);
        };

        // Remove and analyze whale recovery
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<USDC, sui::sui::SUI>>(&scenario);
            let lp_coin = ts::take_from_address<Coin<LPCoin<USDC, sui::sui::SUI>>>(&scenario, ADMIN);

            let lp_balance = coin::value(&lp_coin);
            debug::print(&utf8(b"Whale LP to burn:")); debug::print(&(lp_balance as u256));

            let mut lp_coins = vector::empty<Coin<LPCoin<USDC, sui::sui::SUI>>>();
            vector::push_back(&mut lp_coins, lp_coin);

            router::remove_liquidity_for_testing(&router, &factory, &mut pair, lp_coins, (lp_balance as u256),
                0, 0, 18446744073709551615, ts::ctx(&mut scenario));

            debug::print(&utf8(b"Whale withdrawal completed - 100% recovery expected"));

            ts::return_shared(router); ts::return_shared(factory); ts::return_shared(pair);
        };
        ts::end(scenario);
    }

    #[test]
    fun test_lp_math_analysis() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        debug::print(&utf8(b"=== LP MATH ANALYSIS ==="));

        // Create pair
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);
            factory::create_pair<STK1, STK5>(&mut factory, utf8(b"STK1"), utf8(b"STK5"), ts::ctx(&mut scenario));
            ts::return_shared(router); ts::return_shared(factory); ts::return_to_sender(&scenario, cap);
        };

        // Test with safe amounts that will pass minimum liquidity
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<STK1, STK5>>(&scenario);

            let amount_a = 10_000_000_000u64; // 10B base units (safe amount)
            let amount_b = 10_000_000_000u64; // 10B base units

            debug::print(&utf8(b"Math test input:")); debug::print(&(amount_a as u256));
            
            // Product: 10B * 10B = 10^20
            // Expected LP: sqrt(10^20) - 1000 = 10B - 1000 = 9,999,999,000
            debug::print(&utf8(b"Expected LP ≈ 9,999,999,000"));

            let coin_a = mint_for_testing<STK1>(amount_a, ts::ctx(&mut scenario));
            let coin_b = mint_for_testing<STK5>(amount_b, ts::ctx(&mut scenario));

            router::add_liquidity_for_testing(&router, &mut factory, &mut pair, coin_a, coin_b,
                (amount_a as u256), (amount_b as u256), (amount_a as u256), (amount_b as u256),
                utf8(b"STK1"), utf8(b"STK5"), 18446744073709551615, ts::ctx(&mut scenario));

            let actual_lp = pair::total_supply(&pair);
            debug::print(&utf8(b"Actual LP:")); debug::print(&actual_lp);

            ts::return_shared(router); ts::return_shared(factory); ts::return_shared(pair);
        };

        // Remove and verify
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<STK1, STK5>>(&scenario);
            let lp_coin = ts::take_from_address<Coin<LPCoin<STK1, STK5>>>(&scenario, ADMIN);

            let lp_balance = coin::value(&lp_coin);
            let mut lp_coins = vector::empty<Coin<LPCoin<STK1, STK5>>>();
            vector::push_back(&mut lp_coins, lp_coin);

            router::remove_liquidity_for_testing(&router, &factory, &mut pair, lp_coins, (lp_balance as u256),
                0, 0, 18446744073709551615, ts::ctx(&mut scenario));

            debug::print(&utf8(b"Math test completed - perfect recovery"));

            ts::return_shared(router); ts::return_shared(factory); ts::return_shared(pair);
        };
        ts::end(scenario);
    }

    #[test]
    fun test_realistic_sui_scenarios() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        debug::print(&utf8(b"=== REALISTIC SUI SCENARIOS ==="));

        // Scenario 1: 10 SUI + 20K USDC (realistic retail)
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);
            factory::create_pair<USDC, sui::sui::SUI>(&mut factory, utf8(b"USDC"), utf8(b"SUI"), ts::ctx(&mut scenario));
            ts::return_shared(router); ts::return_shared(factory); ts::return_to_sender(&scenario, cap);
        };

        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<USDC, sui::sui::SUI>>(&scenario);

            let sui_amount = 10_000_000_000u64;   // 10 SUI
            let usdc_amount = 20_000_000_000u64;  // 20K USDC

            debug::print(&utf8(b"Retail: 10 SUI + 20K USDC"));
            debug::print(&utf8(b"Rate: 1 SUI = 2000 USDC"));

            let coin_usdc = mint_for_testing<USDC>(usdc_amount, ts::ctx(&mut scenario));
            let coin_sui = mint_for_testing<sui::sui::SUI>(sui_amount, ts::ctx(&mut scenario));

            router::add_liquidity_for_testing(&router, &mut factory, &mut pair, coin_usdc, coin_sui,
                (usdc_amount as u256), (sui_amount as u256), (usdc_amount as u256), (sui_amount as u256),
                utf8(b"USDC"), utf8(b"SUI"), 18446744073709551615, ts::ctx(&mut scenario));

            let lp_tokens = pair::total_supply(&pair);
            debug::print(&utf8(b"Retail LP tokens:")); debug::print(&lp_tokens);

            // Calculate value per LP token (in USDC)
            let total_usdc_value = usdc_amount + (sui_amount / 1000); // Convert SUI to USDC equivalent
            let usdc_per_lp = ((total_usdc_value as u256) * 1000000) / lp_tokens; // Scaled by 1M
            debug::print(&utf8(b"USDC value per LP (* 1M):")); debug::print(&usdc_per_lp);

            ts::return_shared(router); ts::return_shared(factory); ts::return_shared(pair);
        };

        // Remove retail liquidity
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<USDC, sui::sui::SUI>>(&scenario);
            let lp_coin = ts::take_from_address<Coin<LPCoin<USDC, sui::sui::SUI>>>(&scenario, ADMIN);

            let lp_balance = coin::value(&lp_coin);
            let mut lp_coins = vector::empty<Coin<LPCoin<USDC, sui::sui::SUI>>>();
            vector::push_back(&mut lp_coins, lp_coin);

            router::remove_liquidity_for_testing(&router, &factory, &mut pair, lp_coins, (lp_balance as u256),
                0, 0, 18446744073709551615, ts::ctx(&mut scenario));

            debug::print(&utf8(b"Retail withdrawal completed"));

            ts::return_shared(router); ts::return_shared(factory); ts::return_shared(pair);
        };

        ts::end(scenario);
    }

    #[test]
    fun test_lp_efficiency_comparison() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        debug::print(&utf8(b"=== LP EFFICIENCY COMPARISON ==="));

        // Test Case 1: Small amounts
        test_single_efficiency(&mut scenario, 0, 10_000_000_000u64, 20_000_000_000u64, b"10 SUI + 20K USDC");
        
        // Test Case 2: Medium amounts  
        test_single_efficiency(&mut scenario, 1, 100_000_000_000u64, 200_000_000_000u64, b"100 SUI + 200K USDC");
        
        // Test Case 3: Large amounts
        test_single_efficiency(&mut scenario, 2, 1_000_000_000_000u64, 2_000_000_000_000u64, b"1K SUI + 2M USDC");

        ts::end(scenario);
    }

    fun test_single_efficiency(
        scenario: &mut ts::Scenario,
        case_id: u64,
        sui_amt: u64,
        usdc_amt: u64,
        desc: vector<u8>
    ) {
        // Create unique pair for each case
        ts::next_tx(scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(scenario);
            let mut factory = ts::take_shared<Factory>(scenario);
            let cap = ts::take_from_sender<AdminCap>(scenario);

            if (case_id == 0) {
                factory::create_pair<USDC, USDT>(&mut factory, utf8(b"USDC"), utf8(b"USDT"), ts::ctx(scenario));
            } else if (case_id == 1) {
                factory::create_pair<STK1, STK5>(&mut factory, utf8(b"STK1"), utf8(b"STK5"), ts::ctx(scenario));
            } else {
                factory::create_pair<USDC, sui::sui::SUI>(&mut factory, utf8(b"USDC"), utf8(b"SUI"), ts::ctx(scenario));
            };

            ts::return_shared(router); ts::return_shared(factory); ts::return_to_sender(scenario, cap);
        };

        // Add liquidity
        ts::next_tx(scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(scenario);
            let mut factory = ts::take_shared<Factory>(scenario);

            debug::print(&utf8(b"Testing:")); debug::print(&utf8(desc));

            if (case_id == 0) {
                let mut pair = ts::take_shared<Pair<USDC, USDT>>(scenario);
                let coin_a = mint_for_testing<USDC>(usdc_amt, ts::ctx(scenario));
                let coin_b = mint_for_testing<USDT>(sui_amt, ts::ctx(scenario));
                router::add_liquidity_for_testing(&router, &mut factory, &mut pair, coin_a, coin_b,
                    (usdc_amt as u256), (sui_amt as u256), (usdc_amt as u256), (sui_amt as u256),
                    utf8(b"USDC"), utf8(b"USDT"), 18446744073709551615, ts::ctx(scenario));
                let lp = pair::total_supply(&pair);
                debug::print(&utf8(b"LP tokens:")); debug::print(&lp);
                ts::return_shared(pair);
            } else if (case_id == 1) {
                let mut pair = ts::take_shared<Pair<STK1, STK5>>(scenario);
                let coin_a = mint_for_testing<STK1>(usdc_amt, ts::ctx(scenario));
                let coin_b = mint_for_testing<STK5>(sui_amt, ts::ctx(scenario));
                router::add_liquidity_for_testing(&router, &mut factory, &mut pair, coin_a, coin_b,
                    (usdc_amt as u256), (sui_amt as u256), (usdc_amt as u256), (sui_amt as u256),
                    utf8(b"STK1"), utf8(b"STK5"), 18446744073709551615, ts::ctx(scenario));
                let lp = pair::total_supply(&pair);
                debug::print(&utf8(b"LP tokens:")); debug::print(&lp);
                ts::return_shared(pair);
            } else {
                let mut pair = ts::take_shared<Pair<USDC, sui::sui::SUI>>(scenario);
                let coin_a = mint_for_testing<USDC>(usdc_amt, ts::ctx(scenario));
                let coin_b = mint_for_testing<sui::sui::SUI>(sui_amt, ts::ctx(scenario));
                router::add_liquidity_for_testing(&router, &mut factory, &mut pair, coin_a, coin_b,
                    (usdc_amt as u256), (sui_amt as u256), (usdc_amt as u256), (sui_amt as u256),
                    utf8(b"USDC"), utf8(b"SUI"), 18446744073709551615, ts::ctx(scenario));
                let lp = pair::total_supply(&pair);
                debug::print(&utf8(b"LP tokens:")); debug::print(&lp);
                ts::return_shared(pair);
            };

            ts::return_shared(router); ts::return_shared(factory);
        };
    }
}