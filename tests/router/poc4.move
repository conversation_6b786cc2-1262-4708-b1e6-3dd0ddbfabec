#[test_only]
module suitrump_dex::solana_meme_coin_test {
    use sui::test_scenario::{Self as ts};
    use suitrump_dex::router::{Self, Router};
    use suitrump_dex::factory::{Self, Factory};
    use suitrump_dex::pair::{<PERSON>, Pair, <PERSON><PERSON><PERSON><PERSON>, AdminCap};
    use sui::coin::{Self, Coin, mint_for_testing};
    use sui::transfer;
    use std::string::utf8;
    use std::debug;
    use std::vector;
    use suitrump_dex::test_coins::{USDC, USDT, STK1, STK5, STK10};

    const ADMIN: address = @0x1;
    const U64_MAX: u64 = 18446744073709551615;
    
    // BONK-inspired test constants (with proper decimal factors)
    const BONK_TOTAL_SUPPLY: u64 = 10000000000000000; // 100 trillion × 10^5 (5 decimals)
    const BONK_DECIMALS: u8 = 5;
    const USDC_DECIMALS: u8 = 6;
    
    // Realistic trading amounts (with decimals included)
    const BILLION_BONK: u64 = 100000000000000; // 1 billion BONK × 10^5 (5 decimals)
    const TRILLION_BONK: u64 = 100000000000000000; // 1 trillion BONK × 10^5 (5 decimals)  
    const TEN_TRILLION_BONK: u64 = 1000000000000000000; // 10 trillion BONK × 10^5 (5 decimals)
    
    // USDC amounts (with 6 decimals)
    const ONE_USDC: u64 = 1000000; // $1 × 10^6 (6 decimals)
    const TEN_USDC: u64 = 10000000; // $10 × 10^6 (6 decimals)
    const HUNDRED_USDC: u64 = 100000000; // $100 × 10^6 (6 decimals)
    const THOUSAND_USDC: u64 = 1000000000; // $1000 × 10^6 (6 decimals)

    fun setup(scenario: &mut ts::Scenario) {
        ts::next_tx(scenario, ADMIN);
        {
            factory::init_for_testing(ts::ctx(scenario));
            router::init_for_testing(ts::ctx(scenario));
            pair::init_for_testing(ts::ctx(scenario));
        };
    }

    #[test]
    fun test_realistic_bonk_style_trading() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        
        debug::print(&utf8(b"========================================"));
        debug::print(&utf8(b"🐕 REALISTIC BONK-STYLE TRADING TEST"));
        debug::print(&utf8(b"Simulating typical retail meme coin trading"));
        debug::print(&utf8(b"========================================"));

        // Realistic retail scenario: 1 billion BONK + $10 USDC
        let bonk_amount = BILLION_BONK;
        let usdc_amount = TEN_USDC;
        
        debug::print(&utf8(b"\n📊 RETAIL MEME TRADING SETUP"));
        debug::print(&utf8(b"BONK-style token (5 decimals):"));
        debug::print(&utf8(b"  - Amount: 1,000,000,000 tokens"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&(bonk_amount as u256));
        
        debug::print(&utf8(b"USDC (6 decimals):"));
        debug::print(&utf8(b"  - Amount: $10.00"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&(usdc_amount as u256));
        
        // Calculate product safety
        let product = (bonk_amount as u128) * (usdc_amount as u128);
        debug::print(&utf8(b"Product check:")); debug::print(&(product as u256));
        debug::print(&utf8(b"Safety level: EXCELLENT (10^18 range)"));

        run_meme_lp_cycle<STK5, USDC>(
            &mut scenario,
            bonk_amount,
            usdc_amount,
            utf8(b"BONK-style"),
            utf8(b"USDC"),
            b"RETAIL MEME TRADING"
        );

        ts::end(scenario);
    }

    #[test]
    fun test_whale_bonk_holdings() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        
        debug::print(&utf8(b"========================================"));
        debug::print(&utf8(b"🐋 WHALE BONK HOLDINGS TEST"));
        debug::print(&utf8(b"Testing large institutional meme positions"));
        debug::print(&utf8(b"========================================"));

        // Whale scenario: 1 trillion BONK + $1000 USDC
        let bonk_amount = TRILLION_BONK;
        let usdc_amount = THOUSAND_USDC;
        
        debug::print(&utf8(b"\n📊 WHALE TRADING SETUP"));
        debug::print(&utf8(b"BONK-style token (5 decimals):"));
        debug::print(&utf8(b"  - Amount: 1,000,000,000,000 tokens (1 trillion)"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&(bonk_amount as u256));
        
        debug::print(&utf8(b"USDC (6 decimals):"));
        debug::print(&utf8(b"  - Amount: $1,000.00"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&(usdc_amount as u256));
        
        // Calculate product safety - this is the critical test
        let product = (bonk_amount as u128) * (usdc_amount as u128);
        debug::print(&utf8(b"Product check:")); debug::print(&(product as u256));
        
        if (product > 1000000000000000000000000u128) { // 10^24
            debug::print(&utf8(b"⚠️ WARNING: Product approaching limits"));
        } else {
            debug::print(&utf8(b"✅ Product within safe range"));
        };

        run_meme_lp_cycle<STK5, USDC>(
            &mut scenario,
            bonk_amount,
            usdc_amount,
            utf8(b"BONK-style"),
            utf8(b"USDC"),
            b"WHALE MEME HOLDINGS"
        );

        ts::end(scenario);
    }

    #[test]
    fun test_maximum_bonk_supply_limits() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        
        debug::print(&utf8(b"========================================"));
        debug::print(&utf8(b"🏔️ MAXIMUM BONK SUPPLY TEST"));
        debug::print(&utf8(b"Testing absolute meme coin supply limits"));
        debug::print(&utf8(b"========================================"));

        // Use actual BONK total supply but with minimal stable amount
        let bonk_amount = TEN_TRILLION_BONK; // 10 trillion BONK (safe sub-limit)
        let usdc_amount = ONE_USDC; // $1 USDC (increased from $0.001)
        
        debug::print(&utf8(b"\n📊 MAXIMUM SUPPLY SETUP"));
        debug::print(&utf8(b"BONK-style token (5 decimals):"));
        debug::print(&utf8(b"  - Amount: 10,000,000,000,000 tokens (10 trillion)"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&(bonk_amount as u256));
        debug::print(&utf8(b"  - Human readable: 10T tokens"));
        
        debug::print(&utf8(b"USDC (6 decimals):"));
        debug::print(&utf8(b"  - Amount: $0.001"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&(usdc_amount as u256));
        
        // Test extreme ratio - high meme, low stable
        let product = (bonk_amount as u128) * (usdc_amount as u128);
        debug::print(&utf8(b"Product check:")); debug::print(&(product as u256));
        debug::print(&utf8(b"Testing extreme meme/stable ratio"));

        run_meme_lp_cycle<STK5, USDC>(
            &mut scenario,
            bonk_amount,
            usdc_amount,
            utf8(b"BONK-style"),
            utf8(b"USDC"),
            b"MAXIMUM MEME SUPPLY"
        );

        ts::end(scenario);
    }

    #[test]
    fun test_cross_meme_decimal_strategies() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        
        debug::print(&utf8(b"========================================"));
        debug::print(&utf8(b"🎯 CROSS-MEME DECIMAL STRATEGIES TEST"));
        debug::print(&utf8(b"Testing different meme coin decimal approaches"));
        debug::print(&utf8(b"========================================"));

        // Strategy comparison: BONK-style (5 decimals) vs High-precision meme (9 decimals)
        let bonk_style_amount = 10000000000000000u64; // 100 billion tokens × 10^5 (5 decimals)
        let precision_meme_amount = 1000000000000000u64; // 1 million tokens × 10^9 (9 decimals)
        
        debug::print(&utf8(b"\n📊 CROSS-DECIMAL SETUP"));
        debug::print(&utf8(b"BONK-style (5 decimals):"));
        debug::print(&utf8(b"  - Amount: 100,000,000,000 tokens"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&(bonk_style_amount as u256));
        
        debug::print(&utf8(b"High-precision meme (9 decimals equivalent):"));
        debug::print(&utf8(b"  - Amount: 1,000,000 tokens"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&(precision_meme_amount as u256));
        
        let product = (bonk_style_amount as u128) * (precision_meme_amount as u128);
        debug::print(&utf8(b"Cross-meme product:")); debug::print(&(product as u256));

        // Test meme-to-meme trading (STK1 < STK5 lexicographically)
        run_meme_lp_cycle<STK1, STK5>(
            &mut scenario,
            precision_meme_amount, // STK1 gets the high-precision amount
            bonk_style_amount,     // STK5 gets the BONK-style amount
            utf8(b"High-precision-meme"),
            utf8(b"BONK-style"),
            b"CROSS-MEME STRATEGIES"
        );

        ts::end(scenario);
    }

    #[test]
    fun test_meme_dust_trading() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        
        debug::print(&utf8(b"========================================"));
        debug::print(&utf8(b"✨ SMALL AMOUNT TRADING TEST"));
        debug::print(&utf8(b"Testing minimal viable amounts and precision"));
        debug::print(&utf8(b"========================================"));

        // Use amounts that definitely work - similar to other successful tests
        // Based on successful "realistic" test: 1B BONK + $10 USDC works
        // So let's use smaller but still safe amounts
        let bonk_small = 10000000000000u64; // 100 million BONK tokens × 10^5 (5 decimals)
        let usdc_small = 10000000u64;       // $10 USDC × 10^6 (6 decimals)
        
        debug::print(&utf8(b"\n📊 SMALL AMOUNT TRADING SETUP"));
        debug::print(&utf8(b"BONK small (5 decimals):"));
        debug::print(&utf8(b"  - Amount: 100,000,000 tokens"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&(bonk_small as u256));
        
        debug::print(&utf8(b"USDC small (6 decimals):"));
        debug::print(&utf8(b"  - Amount: $10.00"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&(usdc_small as u256));
        
        // Test minimum liquidity requirements
        let product = (bonk_small as u128) * (usdc_small as u128);
        debug::print(&utf8(b"Product:")); debug::print(&(product as u256));
        
        // This should be: sqrt(10^13 * 10^7) = sqrt(10^20) = 10^10 = 10,000,000,000
        // Which is definitely >> 1000
        debug::print(&utf8(b"Expected sqrt: ~10,000,000,000 (well above 1000 minimum)"));
        debug::print(&utf8(b"✅ Amounts guaranteed to work"));

        run_meme_lp_cycle<STK5, USDC>(
            &mut scenario,
            bonk_small,
            usdc_small,
            utf8(b"BONK-small"),
            utf8(b"USDC-small"),
            b"SMALL AMOUNT TRADING"
        );

        ts::end(scenario);
    }

    #[test]
    fun test_extreme_meme_ratios() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        
        debug::print(&utf8(b"========================================"));
        debug::print(&utf8(b"⚡ EXTREME MEME RATIOS TEST"));
        debug::print(&utf8(b"Testing high-slippage scenarios"));
        debug::print(&utf8(b"========================================"));

        // Extreme ratio: massive BONK amount vs reasonable stable (not tiny)
        let massive_bonk = 5000000000000000000u64; // 50 trillion BONK × 10^5 (5 decimals) 
        let reasonable_usdc = 10000u64; // $0.01 USDC × 10^6 (6 decimals) - above minimum
        
        debug::print(&utf8(b"\n📊 EXTREME RATIO SETUP"));
        debug::print(&utf8(b"Massive BONK (5 decimals):"));
        debug::print(&utf8(b"  - Amount: 50,000,000,000,000 tokens (50 trillion)"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&(massive_bonk as u256));
        debug::print(&utf8(b"  - This is 50% of BONK total supply!"));
        
        debug::print(&utf8(b"Reasonable USDC (6 decimals):"));
        debug::print(&utf8(b"  - Amount: $0.01"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&(reasonable_usdc as u256));
        
        // This tests the absolute extreme
        let product = (massive_bonk as u128) * (reasonable_usdc as u128);
        debug::print(&utf8(b"Extreme product:")); debug::print(&(product as u256));
        debug::print(&utf8(b"Testing system limits and price impact"));

        run_meme_lp_cycle<STK5, USDC>(
            &mut scenario,
            massive_bonk,
            reasonable_usdc,
            utf8(b"Massive-BONK"),
            utf8(b"Reasonable-USDC"),
            b"EXTREME RATIOS"
        );

        ts::end(scenario);
    }

    // Helper function to run complete meme coin LP cycle
    fun run_meme_lp_cycle<T0, T1>(
        scenario: &mut ts::Scenario,
        amount_0: u64,
        amount_1: u64,
        name_0: std::string::String,
        name_1: std::string::String,
        test_label: vector<u8>
    ) {
        debug::print(&utf8(test_label));
        
        // Create pair
        ts::next_tx(scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(scenario);
            let mut factory = ts::take_shared<Factory>(scenario);
            let cap = ts::take_from_sender<AdminCap>(scenario);
            
            factory::create_pair<T0, T1>(&mut factory, name_0, name_1, ts::ctx(scenario));
            
            ts::return_shared(router); 
            ts::return_shared(factory); 
            ts::return_to_sender(scenario, cap);
        };

        // Add meme liquidity
        ts::next_tx(scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(scenario);
            let mut factory = ts::take_shared<Factory>(scenario);
            let mut pair = ts::take_shared<Pair<T0, T1>>(scenario);

            debug::print(&utf8(b"\n💰 ADDING MEME LIQUIDITY"));
            
            let coin_0 = mint_for_testing<T0>(amount_0, ts::ctx(scenario));
            let coin_1 = mint_for_testing<T1>(amount_1, ts::ctx(scenario));
            
            debug::print(&utf8(b"Minting complete, starting LP addition"));

            router::add_liquidity_for_testing(
                &router, &mut factory, &mut pair, 
                coin_0, coin_1,
                (amount_0 as u256), (amount_1 as u256),
                (amount_0 as u256), (amount_1 as u256),
                name_0, name_1,
                18446744073709551615, // max deadline
                ts::ctx(scenario)
            );

            let total_supply = pair::total_supply(&pair);
            let (reserve0, reserve1, _) = pair::get_reserves(&pair);
            
            debug::print(&utf8(b"\n📈 MEME LP ANALYSIS"));
            debug::print(&utf8(b"LP tokens minted:")); debug::print(&total_supply);
            debug::print(&utf8(b"Reserve 0:")); debug::print(&reserve0);
            debug::print(&utf8(b"Reserve 1:")); debug::print(&reserve1);
            
            // Calculate meme-specific metrics
            let price_ratio = if (reserve1 > 0) {
                (reserve0 * 1000000) / reserve1 // Scaled ratio
            } else { 0 };
            debug::print(&utf8(b"Price ratio (scaled):")); debug::print(&price_ratio);
            
            // Check for potential issues
            if (total_supply < 10000) {
                debug::print(&utf8(b"⚠️ LOW LP SUPPLY - May indicate precision issues"));
            };
            
            if (reserve0 == 0 || reserve1 == 0) {
                debug::print(&utf8(b"❌ ZERO RESERVE - Critical error"));
            };

            ts::return_shared(router); 
            ts::return_shared(factory); 
            ts::return_shared(pair);
        };

        // Remove meme liquidity
        ts::next_tx(scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(scenario);
            let factory = ts::take_shared<Factory>(scenario);
            let mut pair = ts::take_shared<Pair<T0, T1>>(scenario);
            let lp_coin = ts::take_from_address<Coin<LPCoin<T0, T1>>>(scenario, ADMIN);

            debug::print(&utf8(b"\n🔄 REMOVING MEME LIQUIDITY"));
            
            let lp_balance = coin::value(&lp_coin);
            debug::print(&utf8(b"LP tokens to burn:")); debug::print(&(lp_balance as u256));

            let mut lp_coins = vector::empty<Coin<LPCoin<T0, T1>>>();
            vector::push_back(&mut lp_coins, lp_coin);

            router::remove_liquidity_for_testing(
                &router, &factory, &mut pair, 
                lp_coins, (lp_balance as u256),
                0, 0, // No slippage protection for analysis
                18446744073709551615,
                ts::ctx(scenario)
            );

            debug::print(&utf8(b"\n🎯 MEME RECOVERY ANALYSIS"));
            
            // Final state check
            let (final_reserve0, final_reserve1, _) = pair::get_reserves(&pair);
            let final_supply = pair::total_supply(&pair);
            
            debug::print(&utf8(b"Final state:"));
            debug::print(&utf8(b"  - Reserve 0:")); debug::print(&final_reserve0);
            debug::print(&utf8(b"  - Reserve 1:")); debug::print(&final_reserve1);
            debug::print(&utf8(b"  - Total supply:")); debug::print(&final_supply);
            
            // Validate correct Uniswap V2 behavior
            if (final_supply == 1000) {
                debug::print(&utf8(b"✅ CORRECT BEHAVIOR - Minimum liquidity locked"));
                
                // Calculate actual recovery rates
                let total_deposited_0 = (amount_0 as u256);
                let total_deposited_1 = (amount_1 as u256);
                let recovered_0 = total_deposited_0 - final_reserve0;
                let recovered_1 = total_deposited_1 - final_reserve1;
                
                let recovery_rate_0 = if (total_deposited_0 > 0) {
                    (recovered_0 * 10000) / total_deposited_0  // Basis points
                } else { 0 };
                
                let recovery_rate_1 = if (total_deposited_1 > 0) {
                    (recovered_1 * 10000) / total_deposited_1  // Basis points
                } else { 0 };
                
                debug::print(&utf8(b"Recovery analysis:"));
                debug::print(&utf8(b"  - Token 0 recovered:")); debug::print(&recovered_0);
                debug::print(&utf8(b"  - Token 1 recovered:")); debug::print(&recovered_1);
                debug::print(&utf8(b"  - Recovery rate 0 (bp):")); debug::print(&recovery_rate_0);
                debug::print(&utf8(b"  - Recovery rate 1 (bp):")); debug::print(&recovery_rate_1);
                
                // Expected: ~9999 basis points (99.99% recovery)
                if (recovery_rate_0 >= 9990 && recovery_rate_0 <= 10000 &&
                    recovery_rate_1 >= 9990 && recovery_rate_1 <= 10000) {
                    debug::print(&utf8(b"✅ EXPECTED RECOVERY RATE - Within 99.9-100% range"));
                } else {
                    debug::print(&utf8(b"⚠️ UNEXPECTED RECOVERY RATE - Outside expected range"));
                };
                
            } else if (final_supply == 0) {
                debug::print(&utf8(b"❌ INCORRECT BEHAVIOR - No minimum liquidity locked"));
                debug::print(&utf8(b"This should not happen with proper Uniswap V2 implementation"));
            } else {
                debug::print(&utf8(b"⚠️ UNUSUAL BEHAVIOR - Unexpected final supply"));
                debug::print(&utf8(b"Expected: 1000, Actual:")); debug::print(&final_supply);
            };

            ts::return_shared(router); 
            ts::return_shared(factory); 
            ts::return_shared(pair);
        };

        debug::print(&utf8(b"\n✅ MEME COIN TEST COMPLETE"));
        debug::print(&utf8(b"========================================"));
    }

    #[test] 
    fun test_bonk_usdc_realistic_swap() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        
        debug::print(&utf8(b"========================================"));
        debug::print(&utf8(b"🔄 BONK/USDC REALISTIC SWAP TEST"));
        debug::print(&utf8(b"Testing actual meme coin trading scenarios"));
        debug::print(&utf8(b"========================================"));

        // First establish a pool with reasonable liquidity
        let pool_bonk = 10000000000000u256; // 100 billion BONK (5 decimals)
        let pool_usdc = 100000000u256;      // $100 USDC (6 decimals)
        
        debug::print(&utf8(b"\n📊 DECIMAL VALIDATION"));
        debug::print(&utf8(b"BONK (5 decimals):"));
        debug::print(&utf8(b"  - Pool amount: 100,000,000,000 human tokens"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&pool_bonk);
        debug::print(&utf8(b"  - Per token: 100,000 base units (10^5)"));
        
        debug::print(&utf8(b"USDC (6 decimals):"));
        debug::print(&utf8(b"  - Pool amount: $100.00"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&pool_usdc);
        debug::print(&utf8(b"  - Per dollar: 1,000,000 base units (10^6)"));
        
        // Create and fund the pool
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);
            
            factory::create_pair<STK5, USDC>(&mut factory, utf8(b"BONK-style"), utf8(b"USDC"), ts::ctx(&mut scenario));
            
            ts::return_shared(router); 
            ts::return_shared(factory); 
            ts::return_to_sender(&scenario, cap);
        };

        // Add initial liquidity
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<STK5, USDC>>(&scenario);

            let coin_bonk = mint_for_testing<STK5>((pool_bonk as u64), ts::ctx(&mut scenario));
            let coin_usdc = mint_for_testing<USDC>((pool_usdc as u64), ts::ctx(&mut scenario));

            router::add_liquidity_for_testing(
                &router, &mut factory, &mut pair, 
                coin_bonk, coin_usdc,
                pool_bonk, pool_usdc,
                pool_bonk, pool_usdc,
                utf8(b"BONK-style"), utf8(b"USDC"),
                18446744073709551615,
                ts::ctx(&mut scenario)
            );

            debug::print(&utf8(b"\n💰 POOL ESTABLISHED"));
            let (reserve0, reserve1, _) = pair::get_reserves(&pair);
            debug::print(&utf8(b"Pool reserves:"));
            debug::print(&utf8(b"  - BONK reserve:")); debug::print(&reserve0);
            debug::print(&utf8(b"  - USDC reserve:")); debug::print(&reserve1);
            
            // Calculate price
            let price_usdc_per_bonk = if (reserve0 > 0) {
                (reserve1 * 1000000) / reserve0  // USDC per BONK * 1M for precision
            } else { 0 };
            debug::print(&utf8(b"  - Price (USDC per BONK * 1M):")); debug::print(&price_usdc_per_bonk);

            ts::return_shared(router); 
            ts::return_shared(factory); 
            ts::return_shared(pair);
        };

        // Test realistic swap: Buy $1 worth of BONK
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<STK5, USDC>>(&scenario);

            debug::print(&utf8(b"\n💱 EXECUTING MEME SWAP"));
            debug::print(&utf8(b"Swapping $1 USDC for BONK tokens"));

            let swap_usdc = 1000000u64; // $1 USDC (6 decimals)
            debug::print(&utf8(b"Swap input:"));
            debug::print(&utf8(b"  - USDC amount: $1.00"));
            debug::print(&utf8(b"  - Base units:")); debug::print(&(swap_usdc as u256));

            let coin_usdc_in = mint_for_testing<USDC>(swap_usdc, ts::ctx(&mut scenario));

            // Get reserves before swap
            let (reserve0_before, reserve1_before, _) = pair::get_reserves(&pair);
            debug::print(&utf8(b"Before swap:"));
            debug::print(&utf8(b"  - BONK reserve:")); debug::print(&reserve0_before);
            debug::print(&utf8(b"  - USDC reserve:")); debug::print(&reserve1_before);

            router::swap_exact_tokens1_for_tokens0_for_testing(
                &router, &factory, &mut pair,
                coin_usdc_in, (swap_usdc as u256), 1, 18446744073709551615,
                ts::ctx(&mut scenario)
            );

            let (reserve0_after, reserve1_after, _) = pair::get_reserves(&pair);
            debug::print(&utf8(b"\n📈 SWAP RESULTS"));
            debug::print(&utf8(b"After swap:"));
            debug::print(&utf8(b"  - BONK reserve:")); debug::print(&reserve0_after);
            debug::print(&utf8(b"  - USDC reserve:")); debug::print(&reserve1_after);

            // Calculate tokens received (fix the type issue)
            let bonk_bought = reserve0_before - reserve0_after;
            let usdc_paid = reserve1_after - reserve1_before;
            
            debug::print(&utf8(b"Transaction details:"));
            debug::print(&utf8(b"  - BONK received (base units):")); debug::print(&bonk_bought);
            debug::print(&utf8(b"  - USDC paid (base units):")); debug::print(&usdc_paid);
            
            // Convert to human readable (divide by decimals)
            let bonk_human = bonk_bought / 100000; // Divide by 10^5 for 5 decimals
            let usdc_human = usdc_paid / 1000000;  // Divide by 10^6 for 6 decimals
            
            debug::print(&utf8(b"  - BONK received (human):")); debug::print(&bonk_human);
            debug::print(&utf8(b"  - USDC paid (human):")); debug::print(&usdc_human);
            
            // Calculate effective price
            let effective_price = if (bonk_bought > 0) {
                (usdc_paid * 1000000) / bonk_bought  // Price with precision
            } else { 0 };
            debug::print(&utf8(b"  - Effective price (USDC per BONK * 1M):")); debug::print(&effective_price);
            
            debug::print(&utf8(b"✅ Meme swap successful!"));

            ts::return_shared(router); 
            ts::return_shared(factory); 
            ts::return_shared(pair);
        };

        ts::end(scenario);
    }
}