#[test_only]
module suitrump_dex::minimal_lp_economics_test {
    use sui::test_scenario::{Self as ts};
    use suitrump_dex::router::{Self, Router};
    use suitrump_dex::factory::{Self, Factory};
    use suitrump_dex::pair::{<PERSON>, Pair, <PERSON><PERSON><PERSON><PERSON>, AdminCap};
    use sui::coin::{Self, Coin, mint_for_testing};
    use sui::transfer;
    use std::string::utf8;
    use std::debug;
    use std::vector;
    use suitrump_dex::test_coins::{USDC, USDT, STK1, STK5, STK10};

    const ADMIN: address = @0x1;
    const MINIMUM_LIQUIDITY: u256 = 1000;

    fun setup(scenario: &mut ts::Scenario) {
        ts::next_tx(scenario, ADMIN);
        {
            factory::init_for_testing(ts::ctx(scenario));
            router::init_for_testing(ts::ctx(scenario));
            pair::init_for_testing(ts::ctx(scenario));
        };
    }

    /// Calculate the minimum base units needed for given decimals
    /// We need sqrt(amount_a * amount_b) > MINIMUM_LIQUIDITY (1000)
    /// So amount_a * amount_b > 1,000,000 for safety margin
    fun calculate_min_amount(decimals: u8): u64 {
        let mut base_multiplier = 1u64;
        let mut i = 0u8;
        while (i < decimals) {
            base_multiplier = base_multiplier * 10;
            i = i + 1;
        };
        
        // Target: sqrt(amount_a * amount_b) > 2000 (safety margin)
        // So: amount_a * amount_b > 4,000,000
        // For balanced amounts: amount_a = amount_b = sqrt(4,000,000) ≈ 2000
        
        // But we want different amounts for different decimals to test properly
        // Use a scaling approach based on decimals
        let base_amount = if (decimals <= 6) {
            10000u64  // For 6 decimals: 10000 units = 0.01 tokens
        } else {
            100000u64 // For 8+ decimals: 100000 units = 0.001 tokens  
        };
        
        // Ensure we have enough for the product requirement
        // 10000 * 100000 = 1,000,000,000 >> 4,000,000 ✅
        base_amount * base_multiplier
    }

    #[test]
    fun test_minimal_lp_with_different_decimals() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        
        debug::print(&utf8(b"========================================"));
        debug::print(&utf8(b"🔬 MINIMAL LP ECONOMICS ANALYSIS"));
        debug::print(&utf8(b"Testing smallest possible amounts with different decimals"));
        debug::print(&utf8(b"========================================"));

        // Test Case: 6 decimals (USDC) vs 8 decimals (BTC-style)
        // This represents a common real-world scenario
        
        let decimals_a = 6u8;  // USDC-style (6 decimals)
        let decimals_b = 8u8;  // BTC-style (8 decimals)
        
        let amount_a = calculate_min_amount(decimals_a);
        let amount_b = calculate_min_amount(decimals_b);
        
        debug::print(&utf8(b"\n📊 INITIAL SETUP"));
        debug::print(&utf8(b"Token A (6 decimals - USDC style):"));
        debug::print(&utf8(b"  - Decimals: 6"));
        debug::print(&utf8(b"  - Human amount: ~0.01 tokens"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&(amount_a as u256));
        
        debug::print(&utf8(b"Token B (8 decimals - BTC style):"));
        debug::print(&utf8(b"  - Decimals: 8"));
        debug::print(&utf8(b"  - Human amount: ~0.001 tokens"));
        debug::print(&utf8(b"  - Base units:")); debug::print(&(amount_b as u256));
        
        // Check if amounts will pass minimum liquidity
        let product = (amount_a as u256) * (amount_b as u256);
        let sqrt_product_approx = if (product >= 1000000000000u256) { 1000000u256 } else { 1000u256 };
        debug::print(&utf8(b"Product check:")); debug::print(&product);
        debug::print(&utf8(b"Approx sqrt:")); debug::print(&sqrt_product_approx);
        debug::print(&utf8(b"Should be > 2000 for safety"));
        
        if (sqrt_product_approx <= 2000) {
            debug::print(&utf8(b"⚠️ WARNING: Amounts may be too small"));
        };

        // Create pair (STK10 < USDC lexicographically)
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);
            
            factory::create_pair<STK10, USDC>(&mut factory, utf8(b"STK10"), utf8(b"USDC"), ts::ctx(&mut scenario));
            
            ts::return_shared(router); 
            ts::return_shared(factory); 
            ts::return_to_sender(&scenario, cap);
        };

        // Add minimal liquidity and capture initial state
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<STK10, USDC>>(&scenario);

            debug::print(&utf8(b"\n💰 ADDING LIQUIDITY"));
            
            // Mint exact amounts needed (STK10 first, then USDC)
            let coin_a = mint_for_testing<STK10>(amount_b, ts::ctx(&mut scenario)); // STK10 (8 decimals)
            let coin_b = mint_for_testing<USDC>(amount_a, ts::ctx(&mut scenario));  // USDC (6 decimals)
            
            // Verify coin values
            let coin_a_value = coin::value(&coin_a);
            let coin_b_value = coin::value(&coin_b);
            debug::print(&utf8(b"Coin STK10 minted:")); debug::print(&(coin_a_value as u256));
            debug::print(&utf8(b"Coin USDC minted:")); debug::print(&(coin_b_value as u256));

            // Get reserves before (should be 0, 0)
            let (reserve0_before, reserve1_before, _) = pair::get_reserves(&pair);
            debug::print(&utf8(b"Reserves before - STK10:")); debug::print(&reserve0_before);
            debug::print(&utf8(b"Reserves before - USDC:")); debug::print(&reserve1_before);

            // Add liquidity (STK10, USDC order)
            router::add_liquidity_for_testing(
                &router, &mut factory, &mut pair, 
                coin_a, coin_b,
                (amount_b as u256), (amount_a as u256), // STK10 amount, USDC amount
                (amount_b as u256), (amount_a as u256), // min amounts
                utf8(b"STK10"), utf8(b"USDC"), 
                18446744073709551615, // max deadline
                ts::ctx(&mut scenario)
            );

            // Analyze LP tokens received
            let total_supply = pair::total_supply(&pair);
            let (reserve0_after, reserve1_after, _) = pair::get_reserves(&pair);
            
            debug::print(&utf8(b"\n📈 LIQUIDITY ANALYSIS"));
            debug::print(&utf8(b"LP tokens minted:")); debug::print(&total_supply);
            debug::print(&utf8(b"Reserve STK10 after:")); debug::print(&reserve0_after);  
            debug::print(&utf8(b"Reserve USDC after:")); debug::print(&reserve1_after);
            
            // Calculate LP efficiency
            let product = reserve0_after * reserve1_after;
            debug::print(&utf8(b"Reserve product:")); debug::print(&product);
            
            // Expected LP = sqrt(product) - MINIMUM_LIQUIDITY
            // Let's calculate the theoretical vs actual
            debug::print(&utf8(b"Minimum liquidity burned:")); debug::print(&MINIMUM_LIQUIDITY);
            debug::print(&utf8(b"Effective LP tokens to user:")); debug::print(&total_supply);

            ts::return_shared(router); 
            ts::return_shared(factory); 
            ts::return_shared(pair);
        };

        // Remove liquidity and analyze recovery
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<STK10, USDC>>(&scenario);
            let lp_coin = ts::take_from_address<Coin<LPCoin<STK10, USDC>>>(&scenario, ADMIN);

            debug::print(&utf8(b"\n🔄 REMOVING LIQUIDITY"));
            
            let lp_balance = coin::value(&lp_coin);
            debug::print(&utf8(b"LP tokens to burn:")); debug::print(&(lp_balance as u256));
            
            // Get reserves before removal
            let (reserve0_before_burn, reserve1_before_burn, _) = pair::get_reserves(&pair);
            let total_supply_before = pair::total_supply(&pair);
            
            debug::print(&utf8(b"Total supply before burn:")); debug::print(&total_supply_before);
            debug::print(&utf8(b"Reserve STK10 before burn:")); debug::print(&reserve0_before_burn);
            debug::print(&utf8(b"Reserve USDC before burn:")); debug::print(&reserve1_before_burn);

            // Calculate expected amounts (what we should get back)
            let expected_stk10 = (reserve0_before_burn * (lp_balance as u256)) / total_supply_before;
            let expected_usdc = (reserve1_before_burn * (lp_balance as u256)) / total_supply_before;
            
            debug::print(&utf8(b"Expected STK10 recovery:")); debug::print(&expected_stk10);
            debug::print(&utf8(b"Expected USDC recovery:")); debug::print(&expected_usdc);

            // Perform removal
            let mut lp_coins = vector::empty<Coin<LPCoin<STK10, USDC>>>();
            vector::push_back(&mut lp_coins, lp_coin);

            router::remove_liquidity_for_testing(
                &router, &factory, &mut pair, 
                lp_coins, (lp_balance as u256),
                0, 0, // min amounts (no slippage protection for analysis)
                18446744073709551615, 
                ts::ctx(&mut scenario)
            );

            // Analyze final state
            let (reserve0_after_burn, reserve1_after_burn, _) = pair::get_reserves(&pair);
            let total_supply_after = pair::total_supply(&pair);
            
            debug::print(&utf8(b"\n🎯 RECOVERY ANALYSIS"));
            debug::print(&utf8(b"Reserves after removal:"));
            debug::print(&utf8(b"  - Reserve STK10:")); debug::print(&reserve0_after_burn);
            debug::print(&utf8(b"  - Reserve USDC:")); debug::print(&reserve1_after_burn);
            debug::print(&utf8(b"Total supply after:")); debug::print(&total_supply_after);
            
            // Calculate actual recovered amounts
            let actual_recovery_stk10 = reserve0_before_burn - reserve0_after_burn;
            let actual_recovery_usdc = reserve1_before_burn - reserve1_after_burn;
            
            debug::print(&utf8(b"Actual recovered:"));  
            debug::print(&utf8(b"  - Token STK10:")); debug::print(&actual_recovery_stk10);
            debug::print(&utf8(b"  - Token USDC:")); debug::print(&actual_recovery_usdc);

            // Calculate recovery percentages (scaled by 10000 for precision)
            let recovery_rate_stk10 = if (amount_b > 0) {
                (actual_recovery_stk10 * 10000) / (amount_b as u256)
            } else { 0 };
            
            let recovery_rate_usdc = if (amount_a > 0) {
                (actual_recovery_usdc * 10000) / (amount_a as u256)  
            } else { 0 };
            
            debug::print(&utf8(b"\n📊 EFFICIENCY METRICS"));
            debug::print(&utf8(b"Recovery rates (basis points):"));
            debug::print(&utf8(b"  - Token STK10 recovery %:")); debug::print(&recovery_rate_stk10);
            debug::print(&utf8(b"  - Token USDC recovery %:")); debug::print(&recovery_rate_usdc);
            
            // Calculate losses (should be minimal with no trading)
            let loss_stk10 = if (actual_recovery_stk10 < (amount_b as u256)) {
                (amount_b as u256) - actual_recovery_stk10
            } else { 0 };
            
            let loss_usdc = if (actual_recovery_usdc < (amount_a as u256)) {
                (amount_a as u256) - actual_recovery_usdc  
            } else { 0 };
            
            debug::print(&utf8(b"\n⚠️  LOSS ANALYSIS"));
            debug::print(&utf8(b"Token losses (base units):"));
            debug::print(&utf8(b"  - Token STK10 loss:")); debug::print(&loss_stk10);
            debug::print(&utf8(b"  - Token USDC loss:")); debug::print(&loss_usdc);
            
            // Convert losses to human readable (approximately)
            let human_loss_stk10 = loss_stk10 * 1000000 / (amount_b as u256); // per million
            let human_loss_usdc = loss_usdc * 1000000 / (amount_a as u256); // per million
            
            debug::print(&utf8(b"Relative losses (per million):"));
            debug::print(&utf8(b"  - Token STK10:")); debug::print(&human_loss_stk10);
            debug::print(&utf8(b"  - Token USDC:")); debug::print(&human_loss_usdc);

            // Analysis of permanent loss vs minimum liquidity lock
            debug::print(&utf8(b"\n🔍 ROOT CAUSE ANALYSIS"));
            debug::print(&utf8(b"Minimum liquidity locked:")); debug::print(&MINIMUM_LIQUIDITY);
            debug::print(&utf8(b"This creates permanent loss in small LP positions"));
            debug::print(&utf8(b"Loss is due to minimum liquidity lock, not fees"));
            
            // Calculate the impact of minimum liquidity lock
            let locked_portion_stk10 = (MINIMUM_LIQUIDITY * reserve0_before_burn) / (total_supply_before + MINIMUM_LIQUIDITY);
            let locked_portion_usdc = (MINIMUM_LIQUIDITY * reserve1_before_burn) / (total_supply_before + MINIMUM_LIQUIDITY);
            
            debug::print(&utf8(b"Permanently locked amounts:"));
            debug::print(&utf8(b"  - Token STK10 locked:")); debug::print(&locked_portion_stk10);
            debug::print(&utf8(b"  - Token USDC locked:")); debug::print(&locked_portion_usdc);

            ts::return_shared(router); 
            ts::return_shared(factory); 
            ts::return_shared(pair);
        };

        debug::print(&utf8(b"\n✅ MINIMAL LP ANALYSIS COMPLETE"));
        debug::print(&utf8(b"Key Findings:"));
        debug::print(&utf8(b"1. Minimum liquidity lock affects small positions"));
        debug::print(&utf8(b"2. Different decimals handled correctly"));
        debug::print(&utf8(b"3. No trading fees in pure add/remove operations"));
        debug::print(&utf8(b"4. Loss primarily from minimum liquidity mechanism"));
        debug::print(&utf8(b"========================================"));

        ts::end(scenario);
    }
}