#[test_only]
module suitrump_dex::sui_ecosystem_lp_tests {
    use sui::test_scenario::{Self as ts};
    use suitrump_dex::router::{Self, Router};
    use suitrump_dex::factory::{Self, Factory};
    use suitrump_dex::pair::{<PERSON>, <PERSON>ir, <PERSON><PERSON><PERSON><PERSON>, AdminCap};
    use sui::coin::{Self, Coin};
    use sui::transfer;
    use std::string::utf8;
    use std::debug;
    use std::vector;
    use suitrump_dex::test_coins;

    const ADMIN: address = @0x1;

    fun setup(scenario: &mut ts::Scenario) {
        ts::next_tx(scenario, ADMIN);
        {
            factory::init_for_testing(ts::ctx(scenario));
            router::init_for_testing(ts::ctx(scenario));
            pair::init_for_testing(ts::ctx(scenario));
        };
    }

    fun to_base_units(amount: u64, decimals: u8): u64 {
        let mut result = amount;
        let mut i = 0u8;
        while (i < decimals) {
            result = result * 10;
            i = i + 1;
        };
        result
    }

    #[test]
    fun test_sui_whale_scenarios() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        debug::print(&utf8(b"=== SUI WHALE SCENARIOS ==="));

        // Whale Case 1: SUI/USDC (9/6 decimals) - $10M pool
        test_pair_scenario(&mut scenario, 0, 5000000u64, 10000000u64, 9u8, 6u8, b"5M SUI + 10M USDC");
        
        // Whale Case 2: Major token pair (9/9 decimals) - Balanced whale
        test_pair_scenario(&mut scenario, 1, 1000000000u64, 1000000000u64, 9u8, 9u8, b"1B Token + 1B Token");
        
        // Whale Case 3: High-value token (8/6 decimals) - BTC style
        test_pair_scenario(&mut scenario, 2, 100000u64, 5000000000u64, 8u8, 6u8, b"1K BTC + 5B USDC");

        ts::end(scenario);
    }

    #[test]
    fun test_sui_retail_scenarios() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        debug::print(&utf8(b"=== SUI RETAIL SCENARIOS ==="));

        // Retail Case 1: Small SUI/USDC position (9/6 decimals)
        test_pair_scenario(&mut scenario, 0, 100u64, 200u64, 9u8, 6u8, b"100 SUI + 200 USDC");
        
        // Retail Case 2: Meme coin position (6/9 decimals)
        test_pair_scenario(&mut scenario, 1, 1000000u64, 50u64, 6u8, 9u8, b"1M Meme + 50 SUI");
        
        // Retail Case 3: Gaming token (7/6 decimals)
        test_pair_scenario(&mut scenario, 2, 50000u64, 100u64, 7u8, 6u8, b"500K Game + 100 USDC");

        ts::end(scenario);
    }

    #[test] 
    fun test_sui_decimal_matrix() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        debug::print(&utf8(b"=== SUI DECIMAL MATRIX TEST ==="));

        // Test all common Sui decimal combinations with same token amounts
        let base_amount = 100000u64; // 100K tokens

        // 9 vs 6 (most common: SUI vs USDC)
        test_pair_scenario(&mut scenario, 0, base_amount, base_amount, 9u8, 6u8, b"9dec vs 6dec");
        
        // 8 vs 6 (BTC style vs USDC)  
        test_pair_scenario(&mut scenario, 1, base_amount, base_amount, 8u8, 6u8, b"8dec vs 6dec");
        
        // 9 vs 8 (SUI vs BTC style)
        test_pair_scenario(&mut scenario, 2, base_amount, base_amount, 9u8, 8u8, b"9dec vs 8dec");
        
        // 7 vs 6 (Gaming tokens vs USDC)
        test_pair_scenario(&mut scenario, 3, base_amount, base_amount, 7u8, 6u8, b"7dec vs 6dec");
        
        // 6 vs 5 (USDC vs some tokens)
        test_pair_scenario(&mut scenario, 4, base_amount, base_amount, 6u8, 5u8, b"6dec vs 5dec");

        ts::end(scenario);
    }

    #[test]
    fun test_sui_extreme_values() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        debug::print(&utf8(b"=== SUI EXTREME VALUES TEST ==="));

        // Extreme Case 1: Maximum safe u64 amounts
        test_pair_scenario(&mut scenario, 0, 18446744073u64, 18446744073u64, 6u8, 6u8, b"Max safe amounts");
        
        // Extreme Case 2: Trillion token supply (meme coin style)
        test_pair_scenario(&mut scenario, 1, 1000000000000u64, 1000u64, 5u8, 9u8, b"1T Meme + 1K SUI");
        
        // Extreme Case 3: High precision vs low precision
        test_pair_scenario(&mut scenario, 2, 1000u64, 1000000000u64, 9u8, 5u8, b"1K High + 1B Low");

        ts::end(scenario);
    }

    #[test]
    fun test_real_sui_ratios() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        debug::print(&utf8(b"=== REAL SUI ECOSYSTEM RATIOS ==="));

        // Real Ratio 1: SUI/USDC at $2 SUI (realistic market)
        test_pair_scenario(&mut scenario, 0, 500000u64, 1000000u64, 9u8, 6u8, b"500K SUI + 1M USDC");
        
        // Real Ratio 2: DeFi governance token vs SUI 
        test_pair_scenario(&mut scenario, 1, 100000u64, 10000000u64, 8u8, 9u8, b"100K Gov + 10M SUI");
        
        // Real Ratio 3: NFT utility token vs USDC
        test_pair_scenario(&mut scenario, 2, 10000000u64, 50000u64, 6u8, 6u8, b"10M NFT + 50K USDC");

        ts::end(scenario);
    }

    // Unified test function for all scenarios
    fun test_pair_scenario(
        scenario: &mut ts::Scenario,
        case_id: u64,
        amount_a: u64,
        amount_b: u64,
        dec_a: u8,
        dec_b: u8,
        desc: vector<u8>
    ) {
        debug::print(&utf8(b"\n--- Testing:"));
        debug::print(&utf8(desc));
        
        let base_a = to_base_units(amount_a, dec_a);
        let base_b = to_base_units(amount_b, dec_b);
        
        debug::print(&utf8(b"Amounts:"));
        debug::print(&(amount_a as u256));
        debug::print(&(amount_b as u256));
        debug::print(&utf8(b"Decimals:"));
        debug::print(&(dec_a as u256));
        debug::print(&(dec_b as u256));
        debug::print(&utf8(b"Base units:"));
        debug::print(&(base_a as u256));
        debug::print(&(base_b as u256));

        // Skip if below minimum requirements
        if (base_a < 1000 || base_b < 1000) {
            debug::print(&utf8(b"⚠️ Skipping - below minimum"));
            return
        };

        // Create pair
        ts::next_tx(scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(scenario);
            let mut factory = ts::take_shared<Factory>(scenario);
            let cap = ts::take_from_sender<AdminCap>(scenario);

            let token_pair = case_id % 5;
            if (token_pair == 0) {
                factory::create_pair<test_coins::USDC, test_coins::USDT>(
                    &mut factory, utf8(b"USDC"), utf8(b"USDT"), ts::ctx(scenario)
                );
            } else if (token_pair == 1) {
                factory::create_pair<test_coins::STK1, test_coins::STK5>(
                    &mut factory, utf8(b"STK1"), utf8(b"STK5"), ts::ctx(scenario)
                );
            } else if (token_pair == 2) {
                factory::create_pair<test_coins::STK10, sui::sui::SUI>(
                    &mut factory, utf8(b"STK10"), utf8(b"SUI"), ts::ctx(scenario)
                );
            } else if (token_pair == 3) {
                factory::create_pair<test_coins::STK1, test_coins::USDC>(
                    &mut factory, utf8(b"STK1"), utf8(b"USDC"), ts::ctx(scenario)
                );
            } else {
                factory::create_pair<test_coins::STK5, test_coins::USDT>(
                    &mut factory, utf8(b"STK5"), utf8(b"USDT"), ts::ctx(scenario)
                );
            };

            ts::return_shared(router);
            ts::return_shared(factory);
            ts::return_to_sender(scenario, cap);
        };

        // Add liquidity
        ts::next_tx(scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(scenario);
            let mut factory = ts::take_shared<Factory>(scenario);

            let token_pair = case_id % 5;
            if (token_pair == 0) {
                add_usdc_usdt(&router, &mut factory, scenario, base_a, base_b);
            } else if (token_pair == 1) {
                add_stk1_stk5(&router, &mut factory, scenario, base_a, base_b);
            } else if (token_pair == 2) {
                add_stk10_sui(&router, &mut factory, scenario, base_a, base_b);
            } else if (token_pair == 3) {
                add_stk1_usdc(&router, &mut factory, scenario, base_a, base_b);
            } else {
                add_stk5_usdt(&router, &mut factory, scenario, base_a, base_b);
            };

            ts::return_shared(router);
            ts::return_shared(factory);
        };

        // Remove liquidity  
        ts::next_tx(scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(scenario);
            let factory = ts::take_shared<Factory>(scenario);

            let token_pair = case_id % 5;
            if (token_pair == 0) {
                remove_usdc_usdt(&router, &factory, scenario);
            } else if (token_pair == 1) {
                remove_stk1_stk5(&router, &factory, scenario);
            } else if (token_pair == 2) {
                remove_stk10_sui(&router, &factory, scenario);
            } else if (token_pair == 3) {
                remove_stk1_usdc(&router, &factory, scenario);
            } else {
                remove_stk5_usdt(&router, &factory, scenario);
            };

            ts::return_shared(router);
            ts::return_shared(factory);
        };

        debug::print(&utf8(b"✅ Scenario completed"));
    }

    // Add liquidity helper functions
    fun add_usdc_usdt(router: &Router, factory: &mut Factory, scenario: &mut ts::Scenario, amount_a: u64, amount_b: u64) {
        let mut pair = ts::take_shared<Pair<test_coins::USDC, test_coins::USDT>>(scenario);
        let coin_a = coin::mint_for_testing<test_coins::USDC>(amount_a, ts::ctx(scenario));
        let coin_b = coin::mint_for_testing<test_coins::USDT>(amount_b, ts::ctx(scenario));

        router::add_liquidity_for_testing(
            router, factory, &mut pair, coin_a, coin_b,
            (amount_a as u256), (amount_b as u256), (amount_a as u256), (amount_b as u256),
            utf8(b"USDC"), utf8(b"USDT"), 18446744073709551615, ts::ctx(scenario)
        );
        ts::return_shared(pair);
    }

    fun add_stk1_stk5(router: &Router, factory: &mut Factory, scenario: &mut ts::Scenario, amount_a: u64, amount_b: u64) {
        let mut pair = ts::take_shared<Pair<test_coins::STK1, test_coins::STK5>>(scenario);
        let coin_a = coin::mint_for_testing<test_coins::STK1>(amount_a, ts::ctx(scenario));
        let coin_b = coin::mint_for_testing<test_coins::STK5>(amount_b, ts::ctx(scenario));

        router::add_liquidity_for_testing(
            router, factory, &mut pair, coin_a, coin_b,
            (amount_a as u256), (amount_b as u256), (amount_a as u256), (amount_b as u256),
            utf8(b"STK1"), utf8(b"STK5"), 18446744073709551615, ts::ctx(scenario)
        );
        ts::return_shared(pair);
    }

    fun add_stk10_sui(router: &Router, factory: &mut Factory, scenario: &mut ts::Scenario, amount_a: u64, amount_b: u64) {
        let mut pair = ts::take_shared<Pair<test_coins::STK10, sui::sui::SUI>>(scenario);
        let coin_a = coin::mint_for_testing<test_coins::STK10>(amount_a, ts::ctx(scenario));
        let coin_b = coin::mint_for_testing<sui::sui::SUI>(amount_b, ts::ctx(scenario));

        router::add_liquidity_for_testing(
            router, factory, &mut pair, coin_a, coin_b,
            (amount_a as u256), (amount_b as u256), (amount_a as u256), (amount_b as u256),
            utf8(b"STK10"), utf8(b"SUI"), 18446744073709551615, ts::ctx(scenario)
        );
        ts::return_shared(pair);
    }

    fun add_stk1_usdc(router: &Router, factory: &mut Factory, scenario: &mut ts::Scenario, amount_a: u64, amount_b: u64) {
        let mut pair = ts::take_shared<Pair<test_coins::STK1, test_coins::USDC>>(scenario);
        let coin_a = coin::mint_for_testing<test_coins::STK1>(amount_a, ts::ctx(scenario));
        let coin_b = coin::mint_for_testing<test_coins::USDC>(amount_b, ts::ctx(scenario));

        router::add_liquidity_for_testing(
            router, factory, &mut pair, coin_a, coin_b,
            (amount_a as u256), (amount_b as u256), (amount_a as u256), (amount_b as u256),
            utf8(b"STK1"), utf8(b"USDC"), 18446744073709551615, ts::ctx(scenario)
        );
        ts::return_shared(pair);
    }

    fun add_stk5_usdt(router: &Router, factory: &mut Factory, scenario: &mut ts::Scenario, amount_a: u64, amount_b: u64) {
        let mut pair = ts::take_shared<Pair<test_coins::STK5, test_coins::USDT>>(scenario);
        let coin_a = coin::mint_for_testing<test_coins::STK5>(amount_a, ts::ctx(scenario));
        let coin_b = coin::mint_for_testing<test_coins::USDT>(amount_b, ts::ctx(scenario));

        router::add_liquidity_for_testing(
            router, factory, &mut pair, coin_a, coin_b,
            (amount_a as u256), (amount_b as u256), (amount_a as u256), (amount_b as u256),
            utf8(b"STK5"), utf8(b"USDT"), 18446744073709551615, ts::ctx(scenario)
        );
        ts::return_shared(pair);
    }

    // Remove liquidity helper functions
    fun remove_usdc_usdt(router: &Router, factory: &Factory, scenario: &mut ts::Scenario) {
        let mut pair = ts::take_shared<Pair<test_coins::USDC, test_coins::USDT>>(scenario);
        let lp_coin = ts::take_from_address<Coin<LPCoin<test_coins::USDC, test_coins::USDT>>>(scenario, ADMIN);
        let lp_balance = coin::value(&lp_coin);
        let mut lp_coins = vector::empty<Coin<LPCoin<test_coins::USDC, test_coins::USDT>>>();
        vector::push_back(&mut lp_coins, lp_coin);

        router::remove_liquidity_for_testing(
            router, factory, &mut pair, lp_coins, (lp_balance as u256),
            0, 0, 18446744073709551615, ts::ctx(scenario)
        );
        ts::return_shared(pair);
    }

    fun remove_stk1_stk5(router: &Router, factory: &Factory, scenario: &mut ts::Scenario) {
        let mut pair = ts::take_shared<Pair<test_coins::STK1, test_coins::STK5>>(scenario);
        let lp_coin = ts::take_from_address<Coin<LPCoin<test_coins::STK1, test_coins::STK5>>>(scenario, ADMIN);
        let lp_balance = coin::value(&lp_coin);
        let mut lp_coins = vector::empty<Coin<LPCoin<test_coins::STK1, test_coins::STK5>>>();
        vector::push_back(&mut lp_coins, lp_coin);

        router::remove_liquidity_for_testing(
            router, factory, &mut pair, lp_coins, (lp_balance as u256),
            0, 0, 18446744073709551615, ts::ctx(scenario)
        );
        ts::return_shared(pair);
    }

    fun remove_stk10_sui(router: &Router, factory: &Factory, scenario: &mut ts::Scenario) {
        let mut pair = ts::take_shared<Pair<test_coins::STK10, sui::sui::SUI>>(scenario);
        let lp_coin = ts::take_from_address<Coin<LPCoin<test_coins::STK10, sui::sui::SUI>>>(scenario, ADMIN);
        let lp_balance = coin::value(&lp_coin);
        let mut lp_coins = vector::empty<Coin<LPCoin<test_coins::STK10, sui::sui::SUI>>>();
        vector::push_back(&mut lp_coins, lp_coin);

        router::remove_liquidity_for_testing(
            router, factory, &mut pair, lp_coins, (lp_balance as u256),
            0, 0, 18446744073709551615, ts::ctx(scenario)
        );
        ts::return_shared(pair);
    }

    fun remove_stk1_usdc(router: &Router, factory: &Factory, scenario: &mut ts::Scenario) {
        let mut pair = ts::take_shared<Pair<test_coins::STK1, test_coins::USDC>>(scenario);
        let lp_coin = ts::take_from_address<Coin<LPCoin<test_coins::STK1, test_coins::USDC>>>(scenario, ADMIN);
        let lp_balance = coin::value(&lp_coin);
        let mut lp_coins = vector::empty<Coin<LPCoin<test_coins::STK1, test_coins::USDC>>>();
        vector::push_back(&mut lp_coins, lp_coin);

        router::remove_liquidity_for_testing(
            router, factory, &mut pair, lp_coins, (lp_balance as u256),
            0, 0, 18446744073709551615, ts::ctx(scenario)
        );
        ts::return_shared(pair);
    }

    fun remove_stk5_usdt(router: &Router, factory: &Factory, scenario: &mut ts::Scenario) {
        let mut pair = ts::take_shared<Pair<test_coins::STK5, test_coins::USDT>>(scenario);
        let lp_coin = ts::take_from_address<Coin<LPCoin<test_coins::STK5, test_coins::USDT>>>(scenario, ADMIN);
        let lp_balance = coin::value(&lp_coin);
        let mut lp_coins = vector::empty<Coin<LPCoin<test_coins::STK5, test_coins::USDT>>>();
        vector::push_back(&mut lp_coins, lp_coin);

        router::remove_liquidity_for_testing(
            router, factory, &mut pair, lp_coins, (lp_balance as u256),
            0, 0, 18446744073709551615, ts::ctx(scenario)
        );
        ts::return_shared(pair);
    }
}