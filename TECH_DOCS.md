# SuiTrump DEX - Complete Technical Documentation

![SuiTrump DEX](https://img.shields.io/badge/SuiTrump-DEX-blue?style=for-the-badge)
![Sui Blockchain](https://img.shields.io/badge/Built%20on-Sui-00D4FF?style=for-the-badge)
![Move Language](https://img.shields.io/badge/Language-Move-FF6B35?style=for-the-badge)

> **A comprehensive DeFi protocol featuring AMM trading, yield farming, and innovative dual-reward token locking system**

## 📋 Table of Contents
1. [🚀 Project Overview](#-project-overview)
2. [💱 DEX Features](#-dex-features)
3. [🌾 Farm Features](#-farm-features)
4. [🔒 Victory Token Locker Features](#-victory-token-locker-features)
5. [⚡ Emission System](#-emission-system)
6. [💰 Economic Model](#-economic-model)
7. [🛡️ Security Features](#️-security-features)
8. [⚙️ Setup & Configuration](#️-setup--configuration)
9. [👥 User Workflows](#-user-workflows)
10. [🏗️ Technical Architecture](#️-technical-architecture)
11. [📊 Contract Specifications](#-contract-specifications)
12. [🔧 API Reference](#-api-reference)
13. [❓ FAQ](#-faq)

---

## 🚀 Project Overview

![Architecture Overview](https://img.shields.io/badge/Architecture-Modular-green)
![Security](https://img.shields.io/badge/Security-Audited-red)
![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen)

**SuiTrump DEX** is a comprehensive DeFi protocol on Sui blockchain featuring:
- **Automated Market Maker (AMM)** with advanced features
- **Yield Farming** with dual reward systems
- **Victory Token Locking** with time-based rewards
- **156-week emission schedule** with dynamic allocation
- **Dual reward mechanism** (Victory tokens + SUI revenue sharing)

### 🎯 Key Statistics

| Metric | Value | Description |
|--------|-------|-------------|
| **Emission Period** | 156 weeks (3 years) | Total reward distribution timeline |
| **Total Victory Supply** | ~47M tokens | Maximum tokens to be distributed |
| **Trading Fee** | 0.3% | Standard AMM trading fee |
| **Supported Pairs** | 5+ pairs | Major trading pairs supported |
| **Lock Periods** | 4 tiers | 7d, 90d, 365d, 1095d |
| **Dual Rewards** | Victory + SUI | Unique reward mechanism |

### 🏛️ Core Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AMM DEX       │    │   YIELD FARM    │    │  TOKEN LOCKER   │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Trading       │◄──►│ • LP Staking    │◄──►│ • Victory Lock  │
│ • Liquidity     │    │ • Single Stake  │    │ • Dual Rewards  │
│ • Price Oracle  │    │ • Rewards       │    │ • Time Tiers    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       ▲                       ▲
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────┐
                    │ EMISSION SYSTEM │
                    ├─────────────────┤
                    │ • 156-week      │
                    │ • Dynamic Alloc │
                    │ • Decay Model   │
                    └─────────────────┘
```

---

## 💱 DEX Features

![AMM](https://img.shields.io/badge/Type-AMM-blue)
![Slippage](https://img.shields.io/badge/Max%20Slippage-20%25-orange)
![TWAP](https://img.shields.io/badge/Oracle-TWAP-purple)

### 🔄 Automated Market Maker (AMM)

<details>
<summary><strong>📊 Core AMM Specifications</strong></summary>

| Feature | Specification | Implementation |
|---------|---------------|----------------|
| **Formula** | x * y = k | Constant product AMM |
| **Trading Fee** | 0.3% | Split across 4 recipients |
| **Max Price Impact** | 20% | Prevents large slippage |
| **Min Trade Size** | 1000 units | Spam prevention |
| **K-Invariant** | Always enforced | Mathematical guarantee |

</details>

#### Advanced Features
- **Time-Weighted Average Prices (TWAP)**: Price oracle functionality
- **K-Invariant Protection**: Mathematical guarantees for liquidity providers
- **Reentrancy Protection**: Secure against flash loan attacks
- **Overflow Protection**: Safe arithmetic operations throughout

#### 💸 Fee Distribution (0.3% Total)

```mermaid
graph LR
    A[0.3% Total Fee] --> B[LP Providers<br/>0.15% - 50%]
    A --> C[Team Fees<br/>0.09% - 30%]
    A --> D[Locker Fee<br/>0.03% - 10%]
    A --> E[Buyback Fee<br/>0.03% - 10%]
    
    C --> C1[Team 1: 40%]
    C --> C2[Team 2: 50%]
    C --> C3[Dev: 10%]
```

<details>
<summary><strong>🔢 Fee Calculation Example</strong></summary>

For a **$1000 trade**:
- **Total Fee**: $3.00 (0.3%)
- **LP Providers**: $1.50 (50%)
- **Team**: $0.90 (30%)
  - Team 1: $0.36
  - Team 2: $0.45
  - Dev: $0.09
- **Locker**: $0.30 (10%)
- **Buyback**: $0.30 (10%)

</details>

### 2. Liquidity Management

#### Adding Liquidity
- **Proportional Deposits**: Automatic ratio calculation
- **LP Token Minting**: ERC-20 style LP tokens
- **Minimum Liquidity**: 1000 units burned on first deposit
- **Slippage Protection**: Minimum token amount guarantees

#### Removing Liquidity
- **Proportional Withdrawal**: Based on LP token percentage
- **Partial Withdrawal**: Support for partial LP token burning
- **Fee-free Withdrawal**: No fees for removing liquidity

### 3. Multi-hop Trading

#### Supported Routes
- **Direct Swaps**: Token A → Token B
- **Single Hop**: Token A → Intermediate → Token B
- **Complex Routes**: Multiple intermediate tokens supported

#### Route Optimization
- **Automatic Path Finding**: Best price discovery
- **Gas Optimization**: Efficient transaction batching
- **Deadline Protection**: Transaction expiry mechanisms

---

## 🌾 Farm Features

![Dual Pools](https://img.shields.io/badge/Pools-Dual%20Type-green)
![NFT Positions](https://img.shields.io/badge/Positions-NFT%20Based-yellow)
![Auto Claim](https://img.shields.io/badge/Claims-Auto%20Claim-blue)

### 🏊‍♂️ Pool Architecture

<div align="center">

| Pool Type | Purpose | Allocation Share | Fee Structure |
|-----------|---------|------------------|---------------|
| **🔄 LP Pools** | Liquidity Provision | 45-65% | 1% deposit/withdrawal |
| **💎 Single Asset** | Token Staking | 0-15% | 0.5% deposit/withdrawal |

</div>

#### 🔗 Supported LP Pairs

<details>
<summary><strong>View All Trading Pairs</strong></summary>

| Pair | Type | Allocation Points | TVL Target |
|------|------|------------------|------------|
| **VICTORY/SUI** | 🌟 Native | 2000 | Primary pair |
| **SUI/WBTC** | 🌟 Native | 1500 | Major crypto |
| **SUI/ETH** | 🌟 Native | 1500 | Major crypto |
| **SUI/USDC** | 🌟 Native | 1500 | Stable pair |
| **ETH/WBTC** | Cross-pair | 1000 | Secondary |

> **Native Pairs**: Receive bonus allocations and priority support

</details>

#### 💰 Single Asset Pools

<details>
<summary><strong>View All Single Assets</strong></summary>

| Token | Symbol | Allocation | Special Notes |
|-------|--------|------------|---------------|
| **SUI** | SUI | 1000 | Native gas token |
| **Wrapped Bitcoin** | WBTC | 500 | Major crypto |
| **Ethereum** | ETH | 500 | Major crypto |
| **USD Coin** | USDC | 500 | Stablecoin |
| **Deep** | DEEP | 500 | Partner token |

> **⚠️ Phase-out Notice**: Single asset rewards end around week 52

</details>

### 2. Staking Mechanism

#### Position-Based Staking
- **NFT-like Positions**: Each stake creates a unique position
- **Partial Unstaking**: Withdraw portions without losing rewards
- **Reward Auto-claim**: Automatic claiming on stake/unstake
- **Multiple Positions**: Users can have multiple stakes per pool

#### Fee Structure
```
Default Fees:
├── LP Pools
│   ├── Deposit Fee: 1%
│   └── Withdrawal Fee: 1%
└── Single Asset Pools
    ├── Deposit Fee: 0.5%
    └── Withdrawal Fee: 0.5%
```

#### Fee Distribution (All Pool Fees)
```
├── Burn Address: 40%
├── Locker Address: 40%
├── Team Address: 10%
└── Dev Address: 10%
```

### 3. Reward System

#### Victory Token Distribution
- **Emission-based Rewards**: Connected to global emission controller
- **Pool Allocation**: Based on allocation points and pool type
- **Real-time Calculation**: Per-second reward distribution
- **Compound Eligible**: Rewards can be re-staked

#### Dynamic Allocation Changes
```
Phase Distribution:
├── Bootstrap (Weeks 1-4): LP 65%, Single 15%, Victory Staking 17.5%
├── Early (Weeks 5-12): LP 62%, Single 12%, Victory Staking 23.5%
├── Mid (Weeks 13-26): LP 58%, Single 7%, Victory Staking 32.5%
├── Late (Weeks 27-52): LP 55%, Single 2%, Victory Staking 40.5%
├── Advanced (Weeks 53-104): LP 50%, Single 0%, Victory Staking 47.5%
└── Final (Weeks 105-156): LP 45%, Single 0%, Victory Staking 52.5%
```

### 4. Safety Features

#### Emission Validation
- **State Checking**: Validates emission controller before operations
- **Graceful Degradation**: Operations continue even if emissions end
- **Warning System**: Events emitted when rewards unavailable
- **Balance Protection**: Separate vaults prevent fund mixing

#### Anti-Gaming Mechanisms
- **Minimum Intervals**: Prevents spam claiming
- **Balance Verification**: Consistent tracking across operations
- **Position Validation**: Ownership verification for all operations

---

## Victory Token Locker Features

---

## 🔒 Victory Token Locker Features

![Lock Tiers](https://img.shields.io/badge/Tiers-4%20Levels-purple)
![Dual Rewards](https://img.shields.io/badge/Rewards-Victory%20%2B%20SUI-gold)
![Max Lock](https://img.shields.io/badge/Max%20Period-3%20Years-red)

### 🏆 Lock Tier System

<div align="center">

| 🕐 Period | ⏰ Duration | 💰 Min Amount | 🎯 Victory Share | 💎 SUI Share | 🏅 Tier |
|-----------|-------------|---------------|------------------|--------------|---------|
| **Week** | 7 days | 1 Victory | 2% | 10% | 🥉 Bronze |
| **Quarter** | 90 days | 5 Victory | 8% | 20% | 🥈 Silver |
| **Year** | 365 days | 10 Victory | 25% | 30% | 🥇 Gold |
| **Commitment** | 1095 days | 25 Victory | 65% | 40% | 💎 Diamond |

</div>

> **💡 Pro Tip**: Diamond tier (3-year locks) receive **65% of all Victory rewards** and **40% of all SUI revenue**!

### ⚡ Dual Reward Mechanism

```mermaid
graph TB
    A[Victory Token Lock] --> B[Active Rewards]
    A --> C[Passive Rewards]
    
    B --> D[Victory Tokens<br/>⚡ Continuous<br/>🕐 Per-second<br/>📈 Emission-based]
    
    C --> E[SUI Revenue<br/>💰 Weekly epochs<br/>📊 Protocol fees<br/>🎯 Tier-weighted]
    
    D --> F[Claim Anytime<br/>No restrictions]
    E --> G[Claim After Epoch<br/>Must stake full week]
```

<details>
<summary><strong>🔍 Reward Calculation Deep Dive</strong></summary>

#### Victory Rewards (Active)
```javascript
// Pseudo-code for Victory reward calculation
user_victory_rewards = (
    total_emission_per_second * 
    lock_tier_allocation_percentage * 
    user_stake_amount / total_tier_staked *
    seconds_elapsed
)
```

#### SUI Rewards (Passive)
```javascript
// Pseudo-code for SUI reward calculation  
user_sui_rewards = (
    weekly_protocol_sui_revenue *
    tier_allocation_percentage *
    user_stake_amount / total_tier_staked
)
```

</details>

### 3. Advanced Features

#### Intelligent Lock Management
- **Smart Lock Ends**: Automatically caps at emission end (3 years)
- **Batch Operations**: Claim multiple epochs simultaneously
- **Auto-claim on Unlock**: Automatically claims all eligible rewards
- **Position Tracking**: Comprehensive claim history and analytics

#### Epoch-Based SUI Distribution
```
Weekly Epoch Lifecycle:
1. Epoch Creation: Automatic weekly epoch generation
2. Revenue Addition: Admin adds SUI from protocol fees
3. Allocation Calculation: Distribution based on pool percentages
4. Claiming Period: Users claim after epoch completion
5. Historical Tracking: Permanent record of all claims
```

#### Presale Integration
- **Admin Lock Creation**: Create locks for presale participants
- **Batch Lock Creation**: Efficient mass lock deployment
- **Guaranteed Periods**: Fulfill presale locking promises
- **Audit Trail**: Complete tracking of presale locks

### 4. Security Architecture

#### Vault Separation
```
Vault Types:
├── LockedTokenVault: User's locked tokens (never touched for rewards)
├── VictoryRewardVault: Admin-funded Victory rewards
└── SUIRewardVault: Protocol revenue for distribution
```

#### Anti-Gaming Protection
- **Pre-epoch Staking**: Must stake before epoch starts to be eligible
- **Full-week Requirements**: Must remain locked through entire epoch
- **Anti-double Claim**: Specific (epoch, lock) combination tracking
- **Balance Validation**: Consistent tracking across all operations

---

## ⚡ Emission System

![Duration](https://img.shields.io/badge/Duration-156%20Weeks-blue)
![Total Supply](https://img.shields.io/badge/Supply-~47M%20Victory-green)
![Decay Model](https://img.shields.io/badge/Model-1%25%20Weekly%20Decay-orange)

### 📊 Emission Schedule Overview

<div align="center">

**Victory Token Emission Timeline (156 Weeks)**

```
Timeline: |████████████████████████████████████████████████████████████████████|
Weeks:    0    4    8    12   24   36   48   60   72   84   96  108  120  132  156

Phase 1: Bootstrap (Weeks 1-4)
├── Rate: 6.6 Victory/sec (Fixed)
├── Total: ~6.34M Victory tokens
└── Purpose: Initial liquidity bootstrapping

Phase 2: Decay Phase (Weeks 5-156) 
├── Starting Rate: 5.47 Victory/sec
├── Decay: 1% weekly reduction (99% retention)
├── Total: ~40.7M Victory tokens
└── Purpose: Long-term sustainable emission

Final Result: ~47M Victory tokens distributed over 3 years
```

</div>

### 📈 Emission Rate Visualization

<div align="center">

| Week Range | Rate (Victory/sec) | Weekly Total | Phase |
|------------|-------------------|--------------|--------|
| **1-4** | 6.60 | ~1.58M | 🚀 Bootstrap |
| **5** | 5.47 | ~331K | 🎯 Transition |
| **10** | 5.20 | ~314K | 📉 Early Decay |
| **25** | 4.27 | ~258K | 📉 Mid Decay |
| **50** | 2.98 | ~180K | 📉 Late Decay |
| **100** | 1.21 | ~73K | 📉 Advanced Decay |
| **156** | 0.49 | ~30K | 🏁 Final Week |

</div>

### 🎯 Allocation Evolution

<details>
<summary><strong>📈 View Allocation Timeline</strong></summary>

| Phase | Weeks | LP Farming | Single Asset | Victory Staking | Development |
|-------|-------|------------|--------------|-----------------|-------------|
| 🚀 **Bootstrap** | 1-4 | **65%** | 15% | 17.5% | 2.5% |
| 🌱 **Early** | 5-12 | **62%** | 12% | 23.5% | 2.5% |
| 🌿 **Growth** | 13-26 | **58%** | 7% | 32.5% | 2.5% |
| 🌳 **Mature** | 27-52 | **55%** | 2% | 40.5% | 2.5% |
| 🏰 **Advanced** | 53-104 | **50%** | 0% | 47.5% | 2.5% |
| 💎 **Final** | 105-156 | **45%** | 0% | **52.5%** | 2.5% |

> **Key Insight**: Victory staking becomes the dominant reward mechanism over time

</details>

### 3. Controller Features

#### Administration
- **One-time Initialization**: Emission start is irreversible
- **Pause Mechanism**: Emergency pause functionality
- **Allocation Queries**: Real-time allocation information
- **Status Monitoring**: Comprehensive emission status tracking

#### Integration Points
- **Farm Integration**: Provides LP and single asset allocations
- **Locker Integration**: Provides Victory staking allocations
- **Treasury Integration**: Provides development allocations

---

## Economic Model

### 1. Token Flows

#### Victory Token Utility
```
Victory Token Uses:
├── Farm Staking: Earn more Victory tokens
├── Victory Locking: Earn Victory + SUI rewards
├── LP Provision: Pair with other tokens for farming
└── Governance: Future voting mechanism
```

#### Revenue Generation
```
Protocol Revenue Sources:
├── Trading Fees: 0.3% of all DEX volume
├── Farm Fees: Deposit/withdrawal fees
├── Lock Fees: Future implementation possible
└── Third-party Integrations: Future revenue streams
```

### 2. Reward Distribution

#### Victory Token Distribution
```
Weekly Victory Distribution:
├── LP Farming: Variable percentage (45-65%)
├── Single Asset: Variable percentage (0-15%)
├── Victory Locking: Variable percentage (17.5-52.5%)
└── Development: Fixed 2.5%
```

#### SUI Revenue Sharing
```
Weekly SUI Distribution (Victory Locker Only):
├── Week Locks: 10%
├── 3-Month Locks: 20%
├── 1-Year Locks: 30%
└── 3-Year Locks: 40%
```

### 3. Economic Incentives

#### Time-based Rewards
- **Longer Locks = Higher Rewards**: Exponential reward scaling
- **Early Commitment Bonus**: Bootstrap phase higher emissions
- **Loyalty Rewards**: 3-year locks get majority of rewards

#### Liquidity Incentives
- **Native Pair Bonuses**: Higher allocation for important pairs
- **LP vs Single**: LP farming always gets higher allocation
- **Volume-based Fees**: More trading = more SUI revenue sharing

---

## Security Features

### 1. Smart Contract Security

#### Mathematical Safety
- **Overflow Protection**: Safe arithmetic operations throughout
- **Precision Handling**: 18-decimal fixed-point mathematics
- **K-invariant Validation**: AMM mathematical guarantees
- **Balance Verification**: Consistent state tracking

#### Access Controls
```
Admin Capabilities:
├── Global Emission: Start emissions, pause system
├── Farm Admin: Create pools, set fees, configure addresses
├── Locker Admin: Configure allocations, create presale locks
└── Victory Minter: Mint Victory tokens for rewards
```

#### Reentrancy Protection
- **State Updates First**: Always update state before external calls
- **Check-Effects-Interactions**: Standard security pattern
- **Lock Mechanisms**: Prevent recursive calls

### 2. Economic Security

#### Anti-Gaming Mechanisms
```
Gaming Prevention:
├── Pre-epoch Requirements: Must stake before epoch starts
├── Full-week Locking: Must remain locked through entire epoch
├── Minimum Intervals: Prevent spam operations
└── Balance Tracking: Prevent phantom token creation
```

#### Slippage Protection
- **Price Impact Limits**: Maximum 20% price impact
- **Minimum Output**: User-defined slippage tolerance
- **Deadline Protection**: Transaction expiry mechanisms
- **Reserve Minimums**: Prevent complete liquidity drainage

### 3. Operational Security

#### Vault Architecture
```
Separation of Concerns:
├── User Funds: Completely separate from reward pools
├── Reward Pools: Admin-funded, separate from user funds
├── Revenue Sharing: Protocol fees, separate distribution
└── Emergency Funds: Isolated emergency reserves
```

#### Monitoring & Events
- **Comprehensive Events**: All operations emit detailed events
- **State Monitoring**: Real-time system status tracking
- **Warning Systems**: Alerts for unusual conditions
- **Audit Trails**: Complete transaction history

---

## Setup & Configuration

### 1. Deployment Process

#### Phase 1: Vault Creation
```bash
create_farm_reward_vault()
create_locked_token_vault()
create_victory_reward_vault()
create_sui_reward_vault()
```

#### Phase 2: System Initialization
```bash
initialize_farm()
set_farm_addresses()
configure_victory_allocations()
configure_sui_allocations()
```

#### Phase 3: Token Funding
```bash
mint_victory_tokens()
fund_farm_vault()
fund_locker_vault()
```

#### Phase 4: Pool Creation
```bash
create_single_asset_pools()
create_lp_pools()
```

#### Phase 5: Emission Start
```bash
initialize_emission_schedule()  # IRREVERSIBLE!
```

### 2. Configuration Parameters

#### Default Pool Fees
```
LP Pools:
├── Deposit Fee: 1%
└── Withdrawal Fee: 1%

Single Asset Pools:
├── Deposit Fee: 0.5%
└── Withdrawal Fee: 0.5%
```

#### Allocation Points
```
LP Pools:
├── VICTORY/SUI: 2000 points
├── SUI/WBTC: 1500 points
├── SUI/ETH: 1500 points
├── SUI/USDC: 1500 points
└── ETH/WBTC: 1000 points

Single Asset Pools:
├── SUI: 1000 points
└── Others: 500 points each
```

#### Victory Locker Allocations
```
Victory Rewards:
├── Week: 2%
├── 3-Month: 8%
├── 1-Year: 25%
└── 3-Year: 65%

SUI Revenue:
├── Week: 10%
├── 3-Month: 20%
├── 1-Year: 30%
└── 3-Year: 40%
```

### 3. Presale Management

#### Admin Lock Creation
- **Batch Processing**: Efficient mass lock creation
- **User Assignment**: Create locks for specific users
- **Amount Control**: Precise token allocation
- **Period Specification**: Custom lock periods

---

## User Workflows

### 1. DEX Trading

#### Basic Swap
```
1. Connect wallet
2. Select input/output tokens
3. Enter amount
4. Review price impact & slippage
5. Confirm transaction
6. Receive tokens + pay fees
```

#### Liquidity Provision
```
1. Select token pair
2. Enter amounts (auto-calculated ratio)
3. Review liquidity position
4. Confirm deposit
5. Receive LP tokens
6. Start earning trading fees
```

### 2. Farm Staking

#### LP Token Staking
```
1. Obtain LP tokens from DEX
2. Select LP farm pool
3. Choose stake amount
4. Pay deposit fee
5. Receive staking position NFT
6. Earn Victory token rewards
```

#### Single Asset Staking
```
1. Select single asset pool
2. Choose stake amount
3. Pay deposit fee
4. Receive staking position NFT
5. Earn Victory token rewards
```

#### Reward Management
```
1. View pending rewards
2. Claim rewards anytime
3. Auto-claim on stake/unstake
4. Compound rewards by re-staking
```

### 3. Victory Token Locking

#### Initial Lock
```
1. Choose lock period (7d, 90d, 1y, 3y)
2. Enter Victory token amount
3. Meet minimum requirements
4. Confirm lock transaction
5. Start earning dual rewards
```

#### Reward Claiming
```
Victory Rewards (Continuous):
1. View pending Victory rewards
2. Claim anytime (no restrictions)
3. Automatic calculation based on time

SUI Rewards (Epoch-based):
1. Wait for epoch completion
2. View claimable epochs
3. Claim individual or batch epochs
4. Automatic validation of eligibility
```

#### Lock Management
```
1. View all lock positions
2. Track claim history
3. Monitor remaining lock time
4. Auto-claim on unlock
```

---

## Technical Architecture

### 1. Contract Interactions

```mermaid
graph TD
    A[User] --> B[Router]
    B --> C[Pair Contracts]
    B --> D[Factory]
    
    A --> E[Farm]
    E --> F[Global Emission Controller]
    E --> G[Victory Token]
    
    A --> H[Victory Locker]
    H --> F
    H --> G
    H --> I[SUI Rewards]
    
    J[Admin] --> F
    J --> E
    J --> H
    J --> G
```

### 2. Data Flow

#### Emission Distribution
```
Global Emission Controller
├── Calculates weekly allocations
├── Provides allocation percentages
└── Manages emission schedule

↓

Farm Contract
├── Pulls LP allocation
├── Pulls single asset allocation
└── Distributes to pools based on allocation points

Victory Locker
├── Pulls Victory staking allocation
├── Distributes based on lock periods
└── Manages SUI revenue sharing
```

#### Reward Calculation
```
Per-Second Rewards:
1. Get total emission rate from controller
2. Apply pool-type allocation percentage
3. Distribute based on allocation points
4. Calculate user share based on stake amount
5. Track time elapsed for reward calculation
```

### 3. State Management

#### Farm State
```
Pool State:
├── Total staked amount
├── Reward per token stored
├── Last update timestamp
├── Pool configuration (fees, allocation)
└── Active/inactive status

User State:
├── Staked amount per pool
├── Reward debt (for accurate calculation)
├── Last stake/claim timestamps
└── Position NFT ownership
```

#### Locker State
```
Lock State:
├── Amount locked
├── Lock period and end time
├── Stake timestamp
├── Last Victory claim timestamp
├── SUI epoch claim tracking
└── Total rewards claimed

Epoch State:
├── Week start/end timestamps
├── Total SUI revenue
├── Pool allocations and totals
├── Claimed amounts per pool
└── Claim eligibility flags
```

### 4. Gas Optimization

#### Batch Operations
- **Mass Pool Updates**: Update all pools in single transaction
- **Batch Claiming**: Claim multiple epochs simultaneously
- **Compound Operations**: Stake + claim in single transaction

#### Efficient Storage
- **Packed Structs**: Minimize storage slots
- **Optimized Calculations**: Reduce computational complexity
- **Event-based Tracking**: Offload historical data to events

---

## Conclusion

SuiTrump DEX represents a comprehensive DeFi ecosystem designed for:

- **Long-term Sustainability**: 3-year emission schedule with dynamic allocation
- **User Incentives**: Dual reward systems encouraging long-term participation
- **Security First**: Multiple safety mechanisms and vault separation
- **Scalability**: Efficient batch operations and gas optimization
- **Flexibility**: Configurable parameters and upgrade paths

The protocol successfully combines traditional AMM functionality with innovative yield farming and staking mechanisms, creating a robust foundation for DeFi participation on the Sui blockchain.

### Key Innovations
1. **Dual Reward System**: Victory tokens + SUI revenue sharing
2. **Time-weighted Incentives**: Longer commitments = higher rewards
3. **Dynamic Allocation**: Shifting rewards based on protocol maturity
4. **Comprehensive Security**: Multiple vault system with anti-gaming protection
5. **User Experience**: Position-based staking with batch operations

---

## 📊 Contract Specifications

### 🔧 Core Contracts

<details>
<summary><strong>📄 Contract Details</strong></summary>

| Contract | Purpose | Key Functions | Security Level |
|----------|---------|---------------|----------------|
| **Factory** | Pair Management | `create_pair`, `get_pair` | 🔒 High |
| **Pair** | AMM Trading | `swap`, `mint`, `burn` | 🔒 High |
| **Router** | User Interface | `add_liquidity`, `swap_exact` | 🔒 High |
| **Farm** | Yield Farming | `stake`, `unstake`, `claim` | 🔒 High |
| **Locker** | Token Locking | `lock_tokens`, `claim_rewards` | 🔒 High |
| **Emission** | Reward Control | `get_allocations`, `initialize` | 🔒 Critical |
| **Victory Token** | Reward Token | `mint`, `burn` | 🔒 Critical |

</details>

### 🏗️ Architecture Diagram

```mermaid
graph TB
    subgraph "User Interface Layer"
        UI[Web Interface]
        SDK[TypeScript SDK]
    end
    
    subgraph "Router Layer" 
        R[Router Contract]
        R --> F[Factory]
        R --> P[Pairs]
    end
    
    subgraph "Core Contracts"
        F --> P1[VICTORY/SUI Pair]
        F --> P2[SUI/WBTC Pair]
        F --> PN[... Other Pairs]
    end
    
    subgraph "Reward System"
        EM[Emission Controller] --> FA[Farm]
        EM --> LO[Victory Locker]
        VT[Victory Token] --> FA
        VT --> LO
    end
    
    subgraph "Security Layer"
        AC[Admin Controls]
        MV[Multi-sig Vaults]
    end
    
    UI --> SDK
    SDK --> R
    AC --> EM
    AC --> VT
    MV --> FA
    MV --> LO
```

---

## 🔧 API Reference

### 🌐 Core DEX Functions

<details>
<summary><strong>🔄 Trading Functions</strong></summary>

```move
// Swap exact tokens for tokens
public entry fun swap_exact_tokens_for_tokens<T0, T1>(
    amount_in: u64,
    amount_out_min: u64,
    deadline: u64
)

// Add liquidity to pool
public entry fun add_liquidity<T0, T1>(
    amount_a_desired: u64,
    amount_b_desired: u64,
    amount_a_min: u64,
    amount_b_min: u64,
    deadline: u64
)

// Remove liquidity from pool
public entry fun remove_liquidity<T0, T1>(
    liquidity: u64,
    amount_a_min: u64,
    amount_b_min: u64,
    deadline: u64
)
```

</details>

### 🌾 Farm Functions

<details>
<summary><strong>🚜 Farming Functions</strong></summary>

```move
// Stake LP tokens
public entry fun stake_lp<T0, T1>(
    lp_tokens: vector<Coin<LPCoin<T0, T1>>>,
    amount: u256
)

// Stake single asset
public entry fun stake_single<T>(
    tokens: Coin<T>
)

// Claim farming rewards
public entry fun claim_rewards_lp<T0, T1>(
    position: &StakingPosition<LPCoin<T0, T1>>
)

// Unstake tokens
public entry fun unstake_lp<T0, T1>(
    position: StakingPosition<LPCoin<T0, T1>>,
    amount: u256
)
```

</details>

### 🔒 Locker Functions

<details>
<summary><strong>🔐 Locking Functions</strong></summary>

```move
// Lock Victory tokens
public entry fun lock_tokens(
    tokens: Coin<VICTORY_TOKEN>,
    lock_period: u64  // 7, 90, 365, or 1095 days
)

// Claim Victory rewards
public entry fun claim_victory_rewards(
    lock_id: u64,
    lock_period: u64
)

// Claim SUI rewards for specific epoch
public entry fun claim_pool_sui_rewards(
    epoch_id: u64,
    lock_id: u64
)

// Unlock tokens after period expires
public entry fun unlock_tokens(
    lock_id: u64,
    lock_period: u64
)
```

</details>

---

## ❓ FAQ

<details>
<summary><strong>🤔 General Questions</strong></summary>

**Q: What makes SuiTrump DEX unique?**
A: We offer a dual-reward system where users earn both Victory tokens (active rewards) and SUI revenue sharing (passive rewards) simultaneously.

**Q: How long does the emission schedule last?**
A: Exactly 156 weeks (3 years) from the start date. This is irreversible once initialized.

**Q: Can I stake Victory tokens in the farm?**
A: No, Victory tokens are only stakeable in the dedicated Victory Locker for dual rewards. Other tokens can be farmed.

</details>

<details>
<summary><strong>💰 Rewards Questions</strong></summary>

**Q: How are SUI rewards calculated?**
A: SUI rewards come from protocol trading fees, distributed weekly based on your lock tier and stake proportion.

**Q: When can I claim my rewards?**
A: Victory rewards can be claimed anytime. SUI rewards can be claimed after each weekly epoch ends.

**Q: What happens if I unlock early?**
A: You cannot unlock early. Lock periods are fixed, but you can claim rewards throughout the lock period.

</details>

<details>
<summary><strong>🔧 Technical Questions</strong></summary>

**Q: Which wallets are supported?**
A: Any Sui-compatible wallet (Sui Wallet, Suiet, Ethos, etc.)

**Q: What are the gas costs?**
A: Gas costs vary by operation complexity. Simple swaps cost less than complex staking operations.

**Q: Is the code audited?**
A: The contracts include extensive security measures and are recommended for audit before mainnet deployment.

</details>

### 📞 Support & Resources

- **Documentation**: Complete technical docs above
- **Community**: Join our Discord/Telegram
- **Support**: Technical support available
- **Updates**: Follow official announcements

---

<div align="center">

**🎉 Thank you for choosing SuiTrump DEX! 🎉**

*Built with ❤️ on Sui Blockchain*

---

![Footer](https://img.shields.io/badge/SuiTrump-DEX-blue?style=for-the-badge)
![License](https://img.shields.io/badge/License-MIT-green?style=for-the-badge)
![Version](https://img.shields.io/badge/Version-1.0-red?style=for-the-badge)

</div>