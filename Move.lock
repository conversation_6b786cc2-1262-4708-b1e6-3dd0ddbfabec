# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "4C2A630DF0FD525D7A07D72F29DE8C949407F6E8ADBEB6469B2565F432C29693"
deps_digest = "F8BBB0CCB2491CA29A3DF03D6F92277A4F3574266507ACD77214D37ECA3F3082"
dependencies = [
  { id = "Sui", name = "Sui" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "framework/testnet", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "framework/testnet", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[move.toolchain-version]
compiler-version = "1.55.0"
edition = "2024.beta"
flavor = "sui"

[env]

[env.devnet]
chain-id = "f4a15ff1"
original-published-id = "0x594ac1bc574441936cc2c8bfe8ee98a8a1cc3cc2565934a35e4fbaa8ef9df9a9"
latest-published-id = "0x594ac1bc574441936cc2c8bfe8ee98a8a1cc3cc2565934a35e4fbaa8ef9df9a9"
published-version = "1"

[env.testnet]
chain-id = "4c78adac"
original-published-id = "0xae12b04f2a46546b7f83c412a467a0abb1f6424415c633a63be2e0377122bf98"
latest-published-id = "0xae12b04f2a46546b7f83c412a467a0abb1f6424415c633a63be2e0377122bf98"
published-version = "1"
