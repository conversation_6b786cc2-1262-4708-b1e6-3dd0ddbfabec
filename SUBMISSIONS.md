# Bug Report Submission Guidelines

## Required Bug Report Format

**Please provide as much detail as possible in your submission.** The more comprehensive your analysis, the better we can understand and implement improvements.

To qualify for rewards, your report must include:

### 1. **Vulnerability Summary**
```
Title: [Concise description of the vulnerability]
Severity: [Critical/High/Medium/Low]
Component: [Contract file and function name]
Impact: [Estimated funds at risk or protocol impact]
```

### 2. **Detailed Technical Analysis**
```
Root Cause: [Explain the underlying technical issue]
Attack Vector: [How the vulnerability can be exploited]
Prerequisites: [Required conditions for the attack]
Affected Functions: [List all vulnerable functions]
```

### 3. **Proof of Concept (MANDATORY)**

#### A. Complete Attack Test Case
```move
#[test]
fun test_vulnerability_poc() {
    // Setup test environment
    let mut scenario = test_scenario::begin(@attacker);
    let ctx = test_scenario::ctx(&mut scenario);
    
    // 1. Initialize contracts
    let (factory, pair, farm, etc.) = setup_test_environment(&mut scenario);
    
    // 2. Setup vulnerable state
    // Add initial liquidity, create pools, etc.
    
    // 3. Execute attack
    let before_balance = get_attacker_balance();
    execute_exploit(&mut pair, attack_params);
    let after_balance = get_attacker_balance();
    
    // 4. Verify exploitation
    assert!(after_balance > before_balance + expected_profit);
    assert!(protocol_is_damaged());
    
    test_scenario::end(scenario);
}

#[test] 
fun test_vulnerability_fix() {
    // Same setup as POC
    let mut scenario = test_scenario::begin(@attacker);
    
    // Apply the proposed fix
    // Then run same attack - should fail
    let result = execute_exploit(&mut fixed_pair, attack_params);
    
    // Verify attack fails with fixed code
    assert!(result.is_err()); // Attack should be prevented
    
    test_scenario::end(scenario);
}
```

#### B. Step-by-Step Attack Instructions
```bash
# 1. Setup test environment
sui move test --filter vulnerability_poc

# 2. Analyze logs before fix
echo "Pre-fix attack results:"
sui move test --filter vulnerability_poc -v 2>&1 | grep "Balance\|Profit\|Exploit"

# 3. Apply fix and re-test
# [Include exact code changes needed]

# 4. Verify fix effectiveness
sui move test --filter vulnerability_fix -v

# 5. Log analysis commands
echo "Analyzing transaction logs:"
sui move test -v 2>&1 | grep -E "(ERROR|SUCCESS|BALANCE_CHANGE)"
```

#### C. Mathematical Proof (if applicable)
```
Initial State:
- Pool reserves: R0 = 1000, R1 = 1000
- K = R0 * R1 = 1,000,000

Attack Execution:
- Input: X tokens
- Expected output (honest): Y tokens
- Actual output (exploit): Y + ε tokens

Proof of Profit:
- Attacker profit: ε = [mathematical formula]
- Protocol loss: [formula showing fund drainage]
- Why attack succeeds: [mathematical explanation]
```

### 4. **Proposed Fix & Analysis**

#### A. Root Cause Analysis
```
The vulnerability exists because:
1. [Primary cause - e.g., "K-invariant check happens before fee extraction"]
2. [Secondary factors - e.g., "No atomic validation of final state"]
3. [Edge case conditions - e.g., "Race condition in multi-step operations"]
```

#### B. Proposed Solution
```move
// FIXED VERSION
public fun secure_function(...) {
    // 1. Validate all inputs first
    assert!(input_validation);
    
    // 2. Perform operations atomically
    let result = atomic_operation();
    
    // 3. Validate final state
    assert!(invariant_check(result));
    
    result
}
```

#### C. Fix Effectiveness Analysis
```
Why this fix works:
1. [Explanation of how fix prevents the exploit]
2. [Analysis of edge cases covered]
3. [Performance/gas impact assessment]

Alternative solutions considered:
1. [Alternative approach A] - Why rejected: [reason]
2. [Alternative approach B] - Why rejected: [reason]

Security properties ensured:
✅ Prevents original attack vector
✅ Maintains protocol functionality  
✅ No new vulnerabilities introduced
✅ Backwards compatibility preserved
```

### 5. **Impact Assessment**

```
Affected Components:
- [Contract A]: [Specific impact]
- [Contract B]: [Specific impact]

Funds at Risk:
- Maximum theoretical loss: [amount/percentage]
- Realistic attack profitability: [analysis]
- Required attacker capital: [amount]

Attack Complexity:
- Technical difficulty: [Low/Medium/High]
- Required knowledge: [Specific expertise needed]
- Reproducibility: [How easily can attack be repeated]

User Impact:
- Direct user fund loss: [Yes/No + details]
- Protocol disruption: [Yes/No + details]  
- Reputation damage: [Assessment]
```

## 🎯 Areas of Interest for Improvement

### **Issue #117: Dynamic Emission Calculation Optimization**

We're looking for improvements to our dynamic emission calculation system. Current implementation works as designed, but we believe there may be opportunities for optimization in:

- **Calculation efficiency** in weekly emission rate updates
- **Gas optimization** for large-scale emission distributions
- **Edge case handling** during phase transitions (bootstrap to post-bootstrap)
- **Precision improvements** in decay rate calculations over 156-week period

### **Issue #119: Emission Timing and Allocation Logic Enhancement**

Our current emission timing and allocation system is functional, but we welcome suggestions for enhancements in:

- **Synchronization accuracy** between different reward pools
- **Allocation distribution** across multiple lock periods simultaneously  
- **State consistency** during concurrent claim operations
- **Reward calculation** precision across varying time periods

**Enhanced Focus**: If your report addresses Issue #117 or #119, please provide:
- **Multiple improvement approaches** with detailed trade-off analysis
- **Mathematical proofs** showing correctness and efficiency gains
- **Comprehensive testing scenarios** covering edge cases
- **Performance impact analysis** on the dynamic emission system
- **Backwards compatibility assessment** with existing locks and stakes

## High Priority Target Areas

**Critical Vulnerabilities:**
- Smart contract exploits that can drain funds
- AMM manipulation attacks (flash loans, sandwich attacks, etc.)
- Reward calculation errors leading to inflation/deflation
- Access control bypasses
- Arithmetic overflow/underflow vulnerabilities
- K-invariant violations in AMM pairs

**High Priority Contract Files:**
- `pair.move` - Core AMM swap logic and fee extraction
- `farm.move` - Reward distribution and staking mechanisms
- `token_locker.move` - Lock/unlock logic and claim validations
- `router.move` - Multi-hop swap vulnerabilities
- `global_emission_controller.move` - Emission timing and allocation logic

**Note**: We will prioritize fixing reported bugs based on severity and impact. Comprehensive, well-documented reports with working proof-of-concepts receive the highest priority and consideration for rewards.